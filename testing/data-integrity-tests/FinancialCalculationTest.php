<?php

namespace Tests\DataIntegrity;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\BranchTransaction;
use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use Carbon\Carbon;

class FinancialCalculationTest extends TestCase
{
    use RefreshDatabase;

    protected $branch;
    protected $member;
    protected $admin;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->branch = Branch::factory()->create();
        $this->member = Member::factory()->create([
            'branch_id' => $this->branch->id,
        ]);
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);
    }

    /** @test */
    public function test_loan_interest_calculation_accuracy()
    {
        $loanAmount = 100000;
        $interestRate = 15.0; // 15% annual
        $termMonths = 12;

        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_amount' => $loanAmount,
            'interest_rate' => $interestRate,
            'loan_term_months' => $termMonths,
        ]);

        // Calculate expected values
        $expectedTotalInterest = ($loanAmount * $interestRate * $termMonths) / (12 * 100);
        $expectedTotalAmount = $loanAmount + $expectedTotalInterest;
        $expectedMonthlyInstallment = $expectedTotalAmount / $termMonths;

        // Verify loan calculations
        $this->assertEquals($expectedTotalInterest, $loan->total_interest);
        $this->assertEquals($expectedTotalAmount, $loan->total_amount);

        // Create installments and verify calculations
        for ($i = 1; $i <= $termMonths; $i++) {
            $installment = Installment::factory()->create([
                'loan_id' => $loan->id,
                'installment_number' => $i,
                'due_date' => Carbon::now()->addMonths($i),
                'principal_amount' => $loanAmount / $termMonths,
                'interest_amount' => $expectedTotalInterest / $termMonths,
                'total_amount' => $expectedMonthlyInstallment,
            ]);

            $this->assertEquals($expectedMonthlyInstallment, $installment->total_amount);
        }

        // Verify total installment amounts equal loan total
        $totalInstallmentAmount = Installment::where('loan_id', $loan->id)->sum('total_amount');
        $this->assertEquals($expectedTotalAmount, $totalInstallmentAmount);
    }

    /** @test */
    public function test_compound_interest_calculation()
    {
        $principal = 50000;
        $rate = 12.0; // 12% annual
        $compoundingPeriods = 12; // Monthly compounding
        $years = 2;

        // A = P(1 + r/n)^(nt)
        $expectedAmount = $principal * pow((1 + ($rate / 100) / $compoundingPeriods), $compoundingPeriods * $years);
        $expectedInterest = $expectedAmount - $principal;

        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_amount' => $principal,
            'interest_rate' => $rate,
            'loan_term_months' => $years * 12,
            'interest_type' => 'compound',
        ]);

        // Allow for small floating point differences
        $this->assertEqualsWithDelta($expectedInterest, $loan->total_interest, 0.01);
        $this->assertEqualsWithDelta($expectedAmount, $loan->total_amount, 0.01);
    }

    /** @test */
    public function test_late_fee_calculation()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_amount' => 100000,
            'late_fee_rate' => 2.0, // 2% per month
        ]);

        $installment = Installment::factory()->create([
            'loan_id' => $loan->id,
            'total_amount' => 10000,
            'due_date' => Carbon::now()->subDays(30), // 30 days overdue
            'status' => 'pending',
        ]);

        // Calculate late fee (2% of installment amount)
        $expectedLateFee = $installment->total_amount * 0.02;
        $calculatedLateFee = $installment->calculateLateFee();

        $this->assertEquals($expectedLateFee, $calculatedLateFee);
    }

    /** @test */
    public function test_partial_payment_calculation()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
        ]);

        $installment = Installment::factory()->create([
            'loan_id' => $loan->id,
            'principal_amount' => 8000,
            'interest_amount' => 2000,
            'total_amount' => 10000,
            'status' => 'pending',
        ]);

        // Make partial payment of 6000
        $partialPayment = 6000;
        
        // Interest should be paid first, then principal
        $expectedInterestPaid = min($partialPayment, $installment->interest_amount);
        $expectedPrincipalPaid = $partialPayment - $expectedInterestPaid;
        $expectedRemainingAmount = $installment->total_amount - $partialPayment;

        $installment->recordPartialPayment($partialPayment);

        $this->assertEquals($expectedInterestPaid, $installment->interest_paid);
        $this->assertEquals($expectedPrincipalPaid, $installment->principal_paid);
        $this->assertEquals($expectedRemainingAmount, $installment->remaining_amount);
    }

    /** @test */
    public function test_early_settlement_calculation()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_amount' => 120000,
            'total_amount' => 138000,
            'loan_term_months' => 12,
        ]);

        // Create installments
        for ($i = 1; $i <= 12; $i++) {
            Installment::factory()->create([
                'loan_id' => $loan->id,
                'installment_number' => $i,
                'total_amount' => 11500,
                'status' => $i <= 4 ? 'paid' : 'pending',
            ]);
        }

        // Calculate early settlement after 4 payments
        $paidAmount = 4 * 11500; // 46000
        $remainingInstallments = 8;
        $remainingAmount = $remainingInstallments * 11500; // 92000

        // Early settlement discount (e.g., 10% of remaining interest)
        $remainingInterest = $remainingAmount - ($loan->loan_amount - ($paidAmount * 0.7)); // Approximate
        $discount = $remainingInterest * 0.1;
        $expectedSettlementAmount = $remainingAmount - $discount;

        $settlementCalculation = $loan->calculateEarlySettlement();

        $this->assertArrayHasKey('remaining_amount', $settlementCalculation);
        $this->assertArrayHasKey('discount_amount', $settlementCalculation);
        $this->assertArrayHasKey('settlement_amount', $settlementCalculation);
        
        $this->assertGreaterThan(0, $settlementCalculation['discount_amount']);
        $this->assertLessThan($remainingAmount, $settlementCalculation['settlement_amount']);
    }

    /** @test */
    public function test_savings_interest_calculation()
    {
        $savingAccount = SavingAccount::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'account_type' => 'regular',
            'interest_rate' => 8.0, // 8% annual
            'balance' => 50000,
        ]);

        // Calculate monthly interest
        $monthlyInterestRate = $savingAccount->interest_rate / 12 / 100;
        $expectedMonthlyInterest = $savingAccount->balance * $monthlyInterestRate;

        $calculatedInterest = $savingAccount->calculateMonthlyInterest();

        $this->assertEqualsWithDelta($expectedMonthlyInterest, $calculatedInterest, 0.01);
    }

    /** @test */
    public function test_currency_precision_handling()
    {
        // Test that all financial calculations maintain proper precision
        $amounts = [
            123.456, // Should round to 123.46
            999.999, // Should round to 1000.00
            0.001,   // Should round to 0.00
            1234.567, // Should round to 1234.57
        ];

        foreach ($amounts as $amount) {
            $loan = Loan::factory()->create([
                'member_id' => $this->member->id,
                'branch_id' => $this->branch->id,
                'loan_amount' => $amount,
            ]);

            // Verify amount is stored with 2 decimal places
            $this->assertEquals(round($amount, 2), $loan->loan_amount);
            
            // Verify database storage precision
            $storedLoan = Loan::find($loan->id);
            $this->assertEquals(round($amount, 2), $storedLoan->loan_amount);
        }
    }

    /** @test */
    public function test_balance_calculation_accuracy()
    {
        $savingAccount = SavingAccount::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'balance' => 0,
        ]);

        $transactions = [
            ['type' => 'deposit', 'amount' => 10000],
            ['type' => 'deposit', 'amount' => 5000],
            ['type' => 'withdrawal', 'amount' => 3000],
            ['type' => 'deposit', 'amount' => 2000],
            ['type' => 'withdrawal', 'amount' => 1000],
        ];

        $expectedBalance = 0;
        foreach ($transactions as $transaction) {
            SavingTransaction::factory()->create([
                'saving_account_id' => $savingAccount->id,
                'transaction_type' => $transaction['type'],
                'amount' => $transaction['amount'],
            ]);

            if ($transaction['type'] === 'deposit') {
                $expectedBalance += $transaction['amount'];
            } else {
                $expectedBalance -= $transaction['amount'];
            }
        }

        // Calculate balance from transactions
        $calculatedBalance = $savingAccount->calculateBalanceFromTransactions();

        $this->assertEquals($expectedBalance, $calculatedBalance);
        $this->assertEquals(13000, $calculatedBalance); // 10000 + 5000 - 3000 + 2000 - 1000
    }

    /** @test */
    public function test_branch_transaction_totals()
    {
        $transactions = [
            ['type' => 'income', 'amount' => 50000],
            ['type' => 'expense', 'amount' => 20000],
            ['type' => 'income', 'amount' => 30000],
            ['type' => 'expense', 'amount' => 15000],
        ];

        foreach ($transactions as $transaction) {
            BranchTransaction::factory()->create([
                'branch_id' => $this->branch->id,
                'transaction_type' => $transaction['type'],
                'amount' => $transaction['amount'],
            ]);
        }

        $totalIncome = BranchTransaction::where('branch_id', $this->branch->id)
            ->where('transaction_type', 'income')
            ->sum('amount');

        $totalExpense = BranchTransaction::where('branch_id', $this->branch->id)
            ->where('transaction_type', 'expense')
            ->sum('amount');

        $netAmount = $totalIncome - $totalExpense;

        $this->assertEquals(80000, $totalIncome); // 50000 + 30000
        $this->assertEquals(35000, $totalExpense); // 20000 + 15000
        $this->assertEquals(45000, $netAmount); // 80000 - 35000
    }

    /** @test */
    public function test_loan_to_value_ratio_calculation()
    {
        $collateralValue = 150000;
        $loanAmount = 100000;

        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_amount' => $loanAmount,
            'collateral_value' => $collateralValue,
        ]);

        $expectedLTV = ($loanAmount / $collateralValue) * 100;
        $calculatedLTV = $loan->calculateLoanToValueRatio();

        $this->assertEqualsWithDelta($expectedLTV, $calculatedLTV, 0.01);
        $this->assertEquals(66.67, round($calculatedLTV, 2));
    }

    /** @test */
    public function test_debt_to_income_ratio_calculation()
    {
        $monthlyIncome = 50000;
        $monthlyLoanPayment = 10000;

        $this->member->update(['monthly_income' => $monthlyIncome]);

        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
        ]);

        Installment::factory()->create([
            'loan_id' => $loan->id,
            'total_amount' => $monthlyLoanPayment,
        ]);

        $expectedDTI = ($monthlyLoanPayment / $monthlyIncome) * 100;
        $calculatedDTI = $this->member->calculateDebtToIncomeRatio();

        $this->assertEqualsWithDelta($expectedDTI, $calculatedDTI, 0.01);
        $this->assertEquals(20.0, $calculatedDTI);
    }

    /** @test */
    public function test_financial_report_accuracy()
    {
        // Create test data
        $loans = Loan::factory()->count(5)->create([
            'branch_id' => $this->branch->id,
            'loan_amount' => 100000,
            'status' => 'active',
        ]);

        $totalDisbursed = $loans->sum('loan_amount');
        $totalCollected = 0;

        foreach ($loans as $loan) {
            $installments = Installment::factory()->count(3)->create([
                'loan_id' => $loan->id,
                'total_amount' => 10000,
                'status' => 'paid',
            ]);
            $totalCollected += $installments->sum('total_amount');
        }

        // Generate financial report
        $report = $this->branch->generateFinancialReport();

        $this->assertEquals($totalDisbursed, $report['total_disbursed']);
        $this->assertEquals($totalCollected, $report['total_collected']);
        $this->assertEquals($totalDisbursed - $totalCollected, $report['outstanding_amount']);
    }

    /** @test */
    public function test_rounding_consistency()
    {
        // Test that rounding is consistent across all calculations
        $testAmounts = [
            1234.565, // Should round to 1234.57 (round half up)
            1234.564, // Should round to 1234.56
            1234.566, // Should round to 1234.57
        ];

        foreach ($testAmounts as $amount) {
            $loan = Loan::factory()->create([
                'member_id' => $this->member->id,
                'branch_id' => $this->branch->id,
                'loan_amount' => $amount,
            ]);

            // Verify consistent rounding
            $expectedRounded = round($amount, 2);
            $this->assertEquals($expectedRounded, $loan->loan_amount);
        }
    }
}
