<?php

namespace Tests\Security;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;

class SecurityTestSuite extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $manager;
    protected $fieldOfficer;
    protected $branch;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->branch = Branch::factory()->create();
        
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $this->manager = User::factory()->create([
            'role' => 'manager',
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        $this->fieldOfficer = User::factory()->create([
            'role' => 'field_officer',
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        Storage::fake('public');
    }

    /** @test */
    public function test_sql_injection_in_login_form()
    {
        $maliciousInputs = [
            "admin' OR '1'='1",
            "admin'; DROP TABLE users; --",
            "admin' UNION SELECT * FROM users --",
            "' OR 1=1 --",
            "admin' OR 'x'='x",
        ];

        foreach ($maliciousInputs as $input) {
            $response = $this->post('/login', [
                'email' => $input,
                'password' => 'password',
            ]);

            // Should not be authenticated with malicious input
            $this->assertGuest();
            $response->assertSessionHasErrors();
        }
    }

    /** @test */
    public function test_sql_injection_in_search_fields()
    {
        $this->actingAs($this->admin);

        $maliciousInputs = [
            "'; DROP TABLE members; --",
            "' UNION SELECT * FROM users --",
            "' OR '1'='1",
            "1' OR 1=1 --",
        ];

        foreach ($maliciousInputs as $input) {
            $response = $this->get("/admin/members?search=" . urlencode($input));
            
            // Should return normal response, not execute SQL
            $response->assertStatus(200);
            
            // Verify database integrity
            $this->assertDatabaseHas('members', ['id' => 1]); // Assuming at least one member exists
        }
    }

    /** @test */
    public function test_xss_prevention_in_user_input()
    {
        $this->actingAs($this->admin);

        $xssPayloads = [
            '<script>alert("XSS")</script>',
            '<img src="x" onerror="alert(\'XSS\')">',
            'javascript:alert("XSS")',
            '<svg onload="alert(\'XSS\')">',
            '"><script>alert("XSS")</script>',
        ];

        foreach ($xssPayloads as $payload) {
            $memberData = [
                'name' => $payload,
                'father_name' => 'Test Father',
                'nid_number' => '1234567890123',
                'phone_number' => '01712345678',
                'branch_id' => $this->branch->id,
                'present_address' => $payload,
                'monthly_income' => 50000,
            ];

            $response = $this->post('/admin/members', $memberData);

            if ($response->isRedirection()) {
                // If member was created, check that XSS is escaped
                $member = Member::where('nid_number', '1234567890123')->first();
                if ($member) {
                    $viewResponse = $this->get("/admin/members/{$member->id}");
                    $viewResponse->assertStatus(200);
                    
                    // Verify XSS payload is escaped in output
                    $viewResponse->assertDontSee($payload, false); // Don't escape for assertion
                    $viewResponse->assertSee(htmlspecialchars($payload, ENT_QUOTES, 'UTF-8'));
                    
                    $member->delete(); // Clean up for next iteration
                }
            }
        }
    }

    /** @test */
    public function test_csrf_protection_on_forms()
    {
        $this->actingAs($this->admin);

        // Test POST request without CSRF token
        $response = $this->withoutMiddleware(\App\Http\Middleware\VerifyCsrfToken::class)
            ->post('/admin/members', [
                'name' => 'Test Member',
                'nid_number' => '1234567890123',
                'phone_number' => '01712345678',
                'branch_id' => $this->branch->id,
            ]);

        // With CSRF middleware disabled, this should work
        // But with CSRF enabled (normal case), it should fail
        $this->withMiddleware(\App\Http\Middleware\VerifyCsrfToken::class);
        
        $response = $this->post('/admin/members', [
            'name' => 'Test Member 2',
            'nid_number' => '1234567890124',
            'phone_number' => '01712345679',
            'branch_id' => $this->branch->id,
        ]);

        $response->assertStatus(419); // CSRF token mismatch
    }

    /** @test */
    public function test_file_upload_security()
    {
        $this->actingAs($this->admin);

        $maliciousFiles = [
            // PHP file disguised as image
            UploadedFile::fake()->createWithContent('malicious.jpg', '<?php echo "Hacked!"; ?>'),
            
            // Executable file
            UploadedFile::fake()->createWithContent('virus.exe', 'MZ executable content'),
            
            // Script file
            UploadedFile::fake()->createWithContent('script.js', 'alert("XSS")'),
            
            // Large file (potential DoS)
            UploadedFile::fake()->create('large.jpg', 10240), // 10MB
        ];

        foreach ($maliciousFiles as $file) {
            $memberData = [
                'name' => 'Test Member',
                'nid_number' => '1234567890123',
                'phone_number' => '01712345678',
                'branch_id' => $this->branch->id,
                'photo' => $file,
                'monthly_income' => 50000,
            ];

            $response = $this->post('/admin/members', $memberData);

            // Should reject malicious files
            $response->assertSessionHasErrors('photo');
        }
    }

    /** @test */
    public function test_authentication_bypass_attempts()
    {
        // Test direct access to protected routes
        $protectedRoutes = [
            '/admin/dashboard',
            '/admin/members',
            '/admin/loans',
            '/manager/dashboard',
            '/field-officer/dashboard',
        ];

        foreach ($protectedRoutes as $route) {
            $response = $this->get($route);
            $response->assertRedirect('/login');
        }
    }

    /** @test */
    public function test_authorization_bypass_attempts()
    {
        // Test field officer trying to access admin routes
        $this->actingAs($this->fieldOfficer);

        $adminRoutes = [
            '/admin/dashboard',
            '/admin/users',
            '/admin/branches',
        ];

        foreach ($adminRoutes as $route) {
            $response = $this->get($route);
            $response->assertStatus(403); // Forbidden
        }

        // Test manager trying to access other branch data
        $otherBranch = Branch::factory()->create();
        $otherMember = Member::factory()->create(['branch_id' => $otherBranch->id]);

        $this->actingAs($this->manager);
        
        $response = $this->get("/manager/members/{$otherMember->id}");
        $response->assertStatus(403); // Should not access other branch member
    }

    /** @test */
    public function test_session_security()
    {
        // Test session fixation prevention
        $response = $this->get('/login');
        $sessionId1 = session()->getId();

        $this->post('/login', [
            'email' => $this->admin->email,
            'password' => 'password',
        ]);

        $sessionId2 = session()->getId();
        
        // Session ID should change after login
        $this->assertNotEquals($sessionId1, $sessionId2);
    }

    /** @test */
    public function test_password_security_requirements()
    {
        $weakPasswords = [
            '123456',
            'password',
            'admin',
            '12345678',
            'qwerty',
            'abc123',
        ];

        foreach ($weakPasswords as $password) {
            $userData = [
                'name' => 'Test User',
                'email' => '<EMAIL>',
                'password' => $password,
                'password_confirmation' => $password,
                'role' => 'field_officer',
                'branch_id' => $this->branch->id,
            ];

            $response = $this->actingAs($this->admin)
                ->post('/admin/users', $userData);

            // Should reject weak passwords
            $response->assertSessionHasErrors('password');
        }
    }

    /** @test */
    public function test_brute_force_protection()
    {
        $email = $this->admin->email;
        
        // Attempt multiple failed logins
        for ($i = 0; $i < 6; $i++) {
            $response = $this->post('/login', [
                'email' => $email,
                'password' => 'wrong_password',
            ]);
        }

        // After multiple failed attempts, should be rate limited
        $response = $this->post('/login', [
            'email' => $email,
            'password' => 'password', // Correct password
        ]);

        $response->assertStatus(429); // Too Many Requests
    }

    /** @test */
    public function test_sensitive_data_exposure()
    {
        $this->actingAs($this->admin);

        // Test that sensitive data is not exposed in responses
        $response = $this->get('/admin/users');
        
        $response->assertStatus(200);
        $response->assertDontSee('password');
        $response->assertDontSee('remember_token');
    }

    /** @test */
    public function test_directory_traversal_prevention()
    {
        $this->actingAs($this->admin);

        $maliciousPaths = [
            '../../../etc/passwd',
            '..\\..\\..\\windows\\system32\\config\\sam',
            '....//....//....//etc/passwd',
            '%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd',
        ];

        foreach ($maliciousPaths as $path) {
            $response = $this->get('/admin/files/' . urlencode($path));
            
            // Should not allow directory traversal
            $response->assertStatus(404); // Not found or forbidden
        }
    }

    /** @test */
    public function test_information_disclosure_prevention()
    {
        // Test error pages don't reveal sensitive information
        $response = $this->get('/non-existent-route');
        
        $response->assertStatus(404);
        $response->assertDontSee('stack trace');
        $response->assertDontSee('database');
        $response->assertDontSee('password');
    }

    /** @test */
    public function test_secure_headers_present()
    {
        $response = $this->get('/login');
        
        // Check for security headers
        $response->assertHeader('X-Frame-Options');
        $response->assertHeader('X-Content-Type-Options', 'nosniff');
        $response->assertHeader('X-XSS-Protection');
    }

    /** @test */
    public function test_input_length_limits()
    {
        $this->actingAs($this->admin);

        // Test extremely long input
        $longString = str_repeat('A', 10000);

        $memberData = [
            'name' => $longString,
            'nid_number' => '1234567890123',
            'phone_number' => '01712345678',
            'branch_id' => $this->branch->id,
            'present_address' => $longString,
            'monthly_income' => 50000,
        ];

        $response = $this->post('/admin/members', $memberData);

        // Should reject overly long input
        $response->assertSessionHasErrors(['name', 'present_address']);
    }

    /** @test */
    public function test_mass_assignment_protection()
    {
        $this->actingAs($this->admin);

        // Try to mass assign protected fields
        $memberData = [
            'name' => 'Test Member',
            'nid_number' => '1234567890123',
            'phone_number' => '01712345678',
            'branch_id' => $this->branch->id,
            'id' => 999999, // Try to set ID
            'created_at' => '2020-01-01', // Try to set timestamp
            'is_admin' => true, // Try to set admin flag
            'monthly_income' => 50000,
        ];

        $response = $this->post('/admin/members', $memberData);

        if ($response->isRedirection()) {
            $member = Member::where('nid_number', '1234567890123')->first();
            
            // Protected fields should not be mass assigned
            $this->assertNotEquals(999999, $member->id);
            $this->assertNotEquals('2020-01-01', $member->created_at->format('Y-m-d'));
        }
    }

    /** @test */
    public function test_api_rate_limiting()
    {
        $this->actingAs($this->admin);

        // Make multiple rapid API requests
        for ($i = 0; $i < 100; $i++) {
            $response = $this->get('/api/members');
            
            if ($response->status() === 429) {
                // Rate limit hit
                break;
            }
        }

        // Should eventually hit rate limit
        $this->assertEquals(429, $response->status());
    }
}
