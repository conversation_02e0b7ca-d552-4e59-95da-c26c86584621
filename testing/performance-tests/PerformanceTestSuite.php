<?php

namespace Tests\Performance;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\BranchTransaction;

class PerformanceTestSuite extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $branch;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->branch = Branch::factory()->create();
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        // Create test data for performance testing
        $this->createLargeDataset();
    }

    protected function createLargeDataset()
    {
        // Create multiple branches
        Branch::factory()->count(10)->create();

        // Create members (1000 records)
        Member::factory()->count(1000)->create([
            'branch_id' => $this->branch->id,
        ]);

        // Create loans (500 records)
        $members = Member::limit(500)->get();
        foreach ($members as $member) {
            Loan::factory()->create([
                'member_id' => $member->id,
                'branch_id' => $member->branch_id,
            ]);
        }

        // Create installments (5000 records)
        $loans = Loan::all();
        foreach ($loans as $loan) {
            Installment::factory()->count(10)->create([
                'loan_id' => $loan->id,
            ]);
        }

        // Create transactions (2000 records)
        BranchTransaction::factory()->count(2000)->create([
            'branch_id' => $this->branch->id,
        ]);
    }

    /** @test */
    public function test_dashboard_loading_performance()
    {
        $this->actingAs($this->admin);

        $startTime = microtime(true);
        
        $response = $this->get('/admin/dashboard');
        
        $endTime = microtime(true);
        $loadTime = $endTime - $startTime;

        $response->assertStatus(200);
        
        // Dashboard should load within 2 seconds
        $this->assertLessThan(2.0, $loadTime, "Dashboard took {$loadTime} seconds to load");
    }

    /** @test */
    public function test_member_list_pagination_performance()
    {
        $this->actingAs($this->admin);

        $startTime = microtime(true);
        
        $response = $this->get('/admin/members?page=1');
        
        $endTime = microtime(true);
        $loadTime = $endTime - $startTime;

        $response->assertStatus(200);
        
        // Member list should load within 1 second
        $this->assertLessThan(1.0, $loadTime, "Member list took {$loadTime} seconds to load");
    }

    /** @test */
    public function test_search_functionality_performance()
    {
        $this->actingAs($this->admin);

        $member = Member::first();
        
        $startTime = microtime(true);
        
        $response = $this->get("/admin/members?search={$member->name}");
        
        $endTime = microtime(true);
        $loadTime = $endTime - $startTime;

        $response->assertStatus(200);
        
        // Search should complete within 0.5 seconds
        $this->assertLessThan(0.5, $loadTime, "Search took {$loadTime} seconds to complete");
    }

    /** @test */
    public function test_database_query_performance()
    {
        // Test complex query performance
        $startTime = microtime(true);
        
        $result = DB::table('members')
            ->join('loans', 'members.id', '=', 'loans.member_id')
            ->join('installments', 'loans.id', '=', 'installments.loan_id')
            ->select('members.name', 'loans.loan_amount', DB::raw('COUNT(installments.id) as installment_count'))
            ->groupBy('members.id', 'members.name', 'loans.loan_amount')
            ->having('installment_count', '>', 5)
            ->get();
        
        $endTime = microtime(true);
        $queryTime = $endTime - $startTime;

        // Complex query should complete within 0.3 seconds
        $this->assertLessThan(0.3, $queryTime, "Complex query took {$queryTime} seconds");
        $this->assertNotEmpty($result);
    }

    /** @test */
    public function test_report_generation_performance()
    {
        $this->actingAs($this->admin);

        $startTime = microtime(true);
        
        $response = $this->get('/admin/reports/members?format=pdf');
        
        $endTime = microtime(true);
        $loadTime = $endTime - $startTime;

        $response->assertStatus(200);
        
        // Report generation should complete within 5 seconds
        $this->assertLessThan(5.0, $loadTime, "Report generation took {$loadTime} seconds");
    }

    /** @test */
    public function test_bulk_data_processing_performance()
    {
        $this->actingAs($this->admin);

        // Test bulk member creation
        $memberData = [];
        for ($i = 0; $i < 100; $i++) {
            $memberData[] = [
                'name' => "Test Member {$i}",
                'nid_number' => '1234567890' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'phone_number' => '01712345' . str_pad($i, 3, '0', STR_PAD_LEFT),
                'branch_id' => $this->branch->id,
                'monthly_income' => 50000,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        $startTime = microtime(true);
        
        Member::insert($memberData);
        
        $endTime = microtime(true);
        $insertTime = $endTime - $startTime;

        // Bulk insert should complete within 1 second
        $this->assertLessThan(1.0, $insertTime, "Bulk insert took {$insertTime} seconds");
    }

    /** @test */
    public function test_concurrent_user_simulation()
    {
        // Simulate multiple concurrent requests
        $responses = [];
        $startTime = microtime(true);

        for ($i = 0; $i < 10; $i++) {
            $user = User::factory()->create(['role' => 'admin']);
            $responses[] = $this->actingAs($user)->get('/admin/dashboard');
        }

        $endTime = microtime(true);
        $totalTime = $endTime - $startTime;

        // All requests should complete within 5 seconds
        $this->assertLessThan(5.0, $totalTime, "Concurrent requests took {$totalTime} seconds");

        foreach ($responses as $response) {
            $response->assertStatus(200);
        }
    }

    /** @test */
    public function test_memory_usage_optimization()
    {
        $this->actingAs($this->admin);

        $memoryBefore = memory_get_usage(true);
        
        // Load large dataset
        $response = $this->get('/admin/members?per_page=100');
        
        $memoryAfter = memory_get_usage(true);
        $memoryUsed = $memoryAfter - $memoryBefore;

        $response->assertStatus(200);
        
        // Memory usage should be reasonable (less than 50MB)
        $this->assertLessThan(50 * 1024 * 1024, $memoryUsed, "Memory usage: " . ($memoryUsed / 1024 / 1024) . " MB");
    }

    /** @test */
    public function test_cache_performance()
    {
        $this->actingAs($this->admin);

        // First request (no cache)
        $startTime = microtime(true);
        $response1 = $this->get('/admin/dashboard');
        $endTime = microtime(true);
        $firstLoadTime = $endTime - $startTime;

        // Second request (with cache)
        $startTime = microtime(true);
        $response2 = $this->get('/admin/dashboard');
        $endTime = microtime(true);
        $secondLoadTime = $endTime - $startTime;

        $response1->assertStatus(200);
        $response2->assertStatus(200);

        // Cached request should be faster
        $this->assertLessThan($firstLoadTime, $secondLoadTime, "Cache not improving performance");
    }

    /** @test */
    public function test_database_connection_pooling()
    {
        $connections = [];
        $startTime = microtime(true);

        // Create multiple database connections
        for ($i = 0; $i < 20; $i++) {
            $connections[] = DB::connection();
            Member::count(); // Execute query to establish connection
        }

        $endTime = microtime(true);
        $connectionTime = $endTime - $startTime;

        // Connection establishment should be efficient
        $this->assertLessThan(2.0, $connectionTime, "Database connections took {$connectionTime} seconds");
    }

    /** @test */
    public function test_large_dataset_handling()
    {
        $this->actingAs($this->admin);

        // Test handling of large result sets
        $startTime = microtime(true);
        
        $members = Member::with(['loans.installments'])
            ->limit(100)
            ->get();
        
        $endTime = microtime(true);
        $queryTime = $endTime - $startTime;

        // Large dataset query should complete within 1 second
        $this->assertLessThan(1.0, $queryTime, "Large dataset query took {$queryTime} seconds");
        $this->assertNotEmpty($members);
    }

    /** @test */
    public function test_file_upload_performance()
    {
        $this->actingAs($this->admin);

        // Create a test file
        $file = \Illuminate\Http\UploadedFile::fake()->image('test.jpg', 1024, 1024); // 1MB image

        $memberData = [
            'name' => 'Test Member',
            'nid_number' => '1234567890123',
            'phone_number' => '01712345678',
            'branch_id' => $this->branch->id,
            'photo' => $file,
            'monthly_income' => 50000,
        ];

        $startTime = microtime(true);
        
        $response = $this->post('/admin/members', $memberData);
        
        $endTime = microtime(true);
        $uploadTime = $endTime - $startTime;

        // File upload should complete within 3 seconds
        $this->assertLessThan(3.0, $uploadTime, "File upload took {$uploadTime} seconds");
        $response->assertRedirect();
    }

    /** @test */
    public function test_api_response_time()
    {
        $this->actingAs($this->admin);

        $endpoints = [
            '/api/members',
            '/api/loans',
            '/api/branches',
            '/api/transactions',
        ];

        foreach ($endpoints as $endpoint) {
            $startTime = microtime(true);
            
            $response = $this->get($endpoint);
            
            $endTime = microtime(true);
            $responseTime = $endTime - $startTime;

            $response->assertStatus(200);
            
            // API responses should be within 0.5 seconds
            $this->assertLessThan(0.5, $responseTime, "API {$endpoint} took {$responseTime} seconds");
        }
    }

    /** @test */
    public function test_session_performance()
    {
        $startTime = microtime(true);

        // Test session operations
        for ($i = 0; $i < 100; $i++) {
            session(['test_key_' . $i => 'test_value_' . $i]);
        }

        for ($i = 0; $i < 100; $i++) {
            $value = session('test_key_' . $i);
        }

        $endTime = microtime(true);
        $sessionTime = $endTime - $startTime;

        // Session operations should be fast
        $this->assertLessThan(0.1, $sessionTime, "Session operations took {$sessionTime} seconds");
    }

    /** @test */
    public function test_pagination_performance_with_large_dataset()
    {
        $this->actingAs($this->admin);

        // Test pagination on different pages
        $pages = [1, 5, 10, 20];

        foreach ($pages as $page) {
            $startTime = microtime(true);
            
            $response = $this->get("/admin/members?page={$page}&per_page=50");
            
            $endTime = microtime(true);
            $loadTime = $endTime - $startTime;

            $response->assertStatus(200);
            
            // Each page should load within 1 second
            $this->assertLessThan(1.0, $loadTime, "Page {$page} took {$loadTime} seconds to load");
        }
    }

    /** @test */
    public function test_calculation_performance()
    {
        // Test financial calculation performance
        $loans = Loan::with('installments')->limit(100)->get();

        $startTime = microtime(true);

        foreach ($loans as $loan) {
            // Perform complex calculations
            $totalPaid = $loan->installments->where('status', 'paid')->sum('total_amount');
            $remainingAmount = $loan->total_amount - $totalPaid;
            $overdueAmount = $loan->installments
                ->where('status', 'pending')
                ->where('due_date', '<', now())
                ->sum('total_amount');
        }

        $endTime = microtime(true);
        $calculationTime = $endTime - $startTime;

        // Calculations should complete within 0.5 seconds
        $this->assertLessThan(0.5, $calculationTime, "Calculations took {$calculationTime} seconds");
    }

    /** @test */
    public function test_export_performance()
    {
        $this->actingAs($this->admin);

        $startTime = microtime(true);
        
        $response = $this->get('/admin/members/export?format=excel');
        
        $endTime = microtime(true);
        $exportTime = $endTime - $startTime;

        $response->assertStatus(200);
        
        // Export should complete within 10 seconds
        $this->assertLessThan(10.0, $exportTime, "Export took {$exportTime} seconds");
    }
}
