# Sonali Microfinance - Administrator Guide

## 📋 Table of Contents

1. [System Overview](#system-overview)
2. [Installation & Setup](#installation--setup)
3. [User Management](#user-management)
4. [System Configuration](#system-configuration)
5. [Database Management](#database-management)
6. [Security Administration](#security-administration)
7. [Backup & Recovery](#backup--recovery)
8. [Performance Monitoring](#performance-monitoring)
9. [Troubleshooting](#troubleshooting)
10. [Maintenance Procedures](#maintenance-procedures)

## 🖥️ System Overview

### Architecture
- **Framework**: Laravel 12.x
- **Database**: MySQL 8.0
- **Web Server**: Nginx
- **PHP Version**: 8.2
- **Cache**: Redis
- **Queue**: Redis/Database

### System Requirements
- **Server**: Ubuntu 20.04 LTS or higher
- **RAM**: Minimum 4GB (8GB recommended)
- **Storage**: 50GB SSD minimum
- **CPU**: 2 cores minimum (4 cores recommended)
- **Network**: Stable internet connection

### Key Components
- **Application Server**: Laravel application
- **Database Server**: MySQL with optimized configuration
- **Cache Server**: Redis for session and application caching
- **File Storage**: Local storage with backup capabilities
- **Email Service**: SMTP configuration for notifications

## 🚀 Installation & Setup

### Initial Installation

#### Prerequisites
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install nginx mysql-server php8.2 php8.2-fpm redis-server nodejs npm composer
```

#### Application Setup
```bash
# Clone repository
cd /var/www
sudo git clone <repository-url> sonali

# Install dependencies
cd sonali
composer install --no-dev --optimize-autoloader
npm ci --production && npm run build

# Set permissions
sudo chown -R www-data:www-data /var/www/sonali
sudo chmod -R 755 /var/www/sonali
sudo chmod -R 775 storage bootstrap/cache
```

#### Environment Configuration
```bash
# Copy environment file
cp .env.production .env

# Generate application key
php artisan key:generate

# Run migrations
php artisan migrate --force

# Seed initial data
php artisan db:seed --force
```

### SSL Certificate Setup
```bash
# Install Certbot
sudo snap install --classic certbot

# Obtain certificate
sudo certbot --nginx -d www.sonalibd.org
```

### Service Configuration

#### Nginx Configuration
```nginx
# /etc/nginx/sites-available/sonalibd.org
server {
    listen 443 ssl http2;
    server_name www.sonalibd.org;
    root /var/www/sonali/public;
    
    # SSL configuration
    ssl_certificate /etc/letsencrypt/live/www.sonalibd.org/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/www.sonalibd.org/privkey.pem;
    
    # PHP-FPM configuration
    location ~ \.php$ {
        fastcgi_pass unix:/var/run/php/php8.2-fpm.sock;
        fastcgi_param SCRIPT_FILENAME $realpath_root$fastcgi_script_name;
        include fastcgi_params;
    }
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }
}
```

## 👥 User Management

### Creating Administrator Account
```bash
# Create admin user via artisan command
php artisan make:admin

# Or via tinker
php artisan tinker
User::create([
    'name' => 'System Administrator',
    'email' => '<EMAIL>',
    'password' => Hash::make('secure_password'),
    'role' => 'admin',
    'is_active' => true
]);
```

### User Role Management

#### Available Roles
- **admin**: Full system access
- **manager**: Branch management access
- **field_officer**: Operational access
- **member**: Self-service access

#### Permission Matrix
| Feature | Admin | Manager | Field Officer | Member |
|---------|-------|---------|---------------|--------|
| User Management | ✅ | ❌ | ❌ | ❌ |
| Branch Management | ✅ | ✅ | ❌ | ❌ |
| Member Registration | ✅ | ✅ | ✅ | ❌ |
| Loan Approval | ✅ | ✅ | ❌ | ❌ |
| Installment Collection | ✅ | ✅ | ✅ | ❌ |
| Reports | ✅ | ✅ | ✅ | ✅ |

### Bulk User Operations

#### Import Users from CSV
```bash
# Prepare CSV file with columns: name,email,role,branch_id
php artisan users:import users.csv
```

#### Export User List
```bash
# Export all users
php artisan users:export

# Export by role
php artisan users:export --role=manager
```

### Password Policies

#### Default Policy
- Minimum 8 characters
- Must contain uppercase, lowercase, number
- Cannot be common passwords
- Expires every 90 days

#### Configuring Password Policy
```php
// config/auth.php
'password_policy' => [
    'min_length' => 8,
    'require_uppercase' => true,
    'require_lowercase' => true,
    'require_numbers' => true,
    'require_symbols' => true,
    'expiry_days' => 90,
    'history_count' => 5,
]
```

## ⚙️ System Configuration

### Application Settings

#### Environment Variables
```bash
# Core settings
APP_NAME="Sonali Microfinance"
APP_ENV=production
APP_DEBUG=false
APP_URL=https://www.sonalibd.org

# Database settings
DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=sonali_db
DB_USERNAME=sonali_user
DB_PASSWORD=secure_password

# Cache settings
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Mail settings
MAIL_MAILER=smtp
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=app_password
```

#### System Parameters
```bash
# Configure system parameters
php artisan config:set loan.max_amount 500000
php artisan config:set loan.min_amount 10000
php artisan config:set loan.max_term 36
php artisan config:set interest.default_rate 15.0
```

### Branch Configuration

#### Creating Branches
```bash
# Via artisan command
php artisan branch:create "Main Branch" "MB001" "Dhaka"

# Via admin panel
# Navigate to Admin → Branches → Add New Branch
```

#### Branch Settings
- Branch code (unique identifier)
- Branch name and address
- Manager assignment
- Operating parameters
- Interest rates and fees

### Financial Configuration

#### Interest Rate Settings
```php
// Configure in admin panel or database
'interest_rates' => [
    'regular_loan' => 15.0,
    'micro_loan' => 18.0,
    'emergency_loan' => 20.0,
    'savings_account' => 8.0,
]
```

#### Fee Structure
```php
'fees' => [
    'membership_fee' => 500,
    'loan_processing_fee' => 2.0, // percentage
    'late_payment_fee' => 2.0, // percentage per month
    'account_maintenance' => 100, // monthly
]
```

## 🗄️ Database Management

### Database Maintenance

#### Regular Maintenance Tasks
```bash
# Optimize database tables
php artisan db:optimize

# Clean up old sessions
php artisan session:gc

# Clear expired cache
php artisan cache:clear

# Optimize autoloader
composer dump-autoload --optimize
```

#### Database Backup
```bash
# Manual backup
mysqldump --single-transaction --routines --triggers sonali_db > backup_$(date +%Y%m%d).sql

# Automated backup (add to cron)
0 2 * * * mysqldump --single-transaction sonali_db > /var/backups/sonali_$(date +\%Y\%m\%d).sql
```

#### Database Restore
```bash
# Restore from backup
mysql sonali_db < backup_20240101.sql

# Verify restore
php artisan migrate:status
```

### Data Migration

#### Running Migrations
```bash
# Run pending migrations
php artisan migrate

# Rollback migrations
php artisan migrate:rollback

# Reset and re-run all migrations
php artisan migrate:fresh --seed
```

#### Creating Custom Migrations
```bash
# Create new migration
php artisan make:migration add_new_field_to_members_table

# Create migration with model
php artisan make:model NewModel -m
```

### Database Monitoring

#### Performance Monitoring
```sql
-- Check slow queries
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- Check table sizes
SELECT 
    table_name AS "Table",
    ROUND(((data_length + index_length) / 1024 / 1024), 2) AS "Size (MB)"
FROM information_schema.TABLES 
WHERE table_schema = "sonali_db"
ORDER BY (data_length + index_length) DESC;
```

#### Index Optimization
```sql
-- Check missing indexes
SELECT * FROM sys.schema_unused_indexes;

-- Analyze table usage
ANALYZE TABLE members, loans, installments;
```

## 🔒 Security Administration

### Security Configuration

#### Firewall Setup
```bash
# Configure UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable
```

#### SSL/TLS Configuration
```bash
# Test SSL configuration
openssl s_client -connect www.sonalibd.org:443

# Check certificate expiry
openssl x509 -in /etc/letsencrypt/live/www.sonalibd.org/cert.pem -text -noout
```

### Access Control

#### IP Whitelisting
```nginx
# In Nginx configuration
location /admin {
    allow ***********/24;
    allow 10.0.0.0/8;
    deny all;
    
    try_files $uri $uri/ /index.php?$query_string;
}
```

#### Rate Limiting
```php
// In routes/web.php
Route::middleware(['throttle:5,1'])->group(function () {
    Route::post('/login', [AuthController::class, 'login']);
});
```

### Security Monitoring

#### Log Analysis
```bash
# Check authentication logs
sudo tail -f /var/log/auth.log

# Check application logs
tail -f storage/logs/laravel.log

# Check Nginx access logs
sudo tail -f /var/log/nginx/access.log
```

#### Intrusion Detection
```bash
# Install and configure fail2ban
sudo apt install fail2ban

# Configure jail for Laravel
sudo nano /etc/fail2ban/jail.local
```

### Security Auditing

#### Regular Security Checks
```bash
# Check for security updates
sudo apt list --upgradable

# Scan for vulnerabilities
composer audit

# Check file permissions
find /var/www/sonali -type f -perm 777
```

## 💾 Backup & Recovery

### Backup Strategy

#### Automated Backup Script
```bash
#!/bin/bash
# /usr/local/bin/sonali-backup.sh

BACKUP_DIR="/var/backups/sonali"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory
mkdir -p $BACKUP_DIR

# Database backup
mysqldump --single-transaction sonali_db > $BACKUP_DIR/db_$DATE.sql

# Application files backup
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /var/www/sonali

# Clean old backups (keep 30 days)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

#### Backup Verification
```bash
# Test database backup
mysql -e "CREATE DATABASE test_restore;"
mysql test_restore < $BACKUP_DIR/db_latest.sql
mysql -e "DROP DATABASE test_restore;"

# Test file backup
tar -tzf $BACKUP_DIR/files_latest.tar.gz > /dev/null
```

### Disaster Recovery

#### Recovery Procedures
1. **Assess the situation**
2. **Restore from latest backup**
3. **Verify data integrity**
4. **Test system functionality**
5. **Notify stakeholders**

#### Recovery Testing
```bash
# Monthly recovery test
./scripts/test-recovery.sh

# Document recovery time
echo "Recovery completed in: $(date)" >> recovery.log
```

## 📊 Performance Monitoring

### System Monitoring

#### Resource Monitoring
```bash
# CPU and memory usage
htop

# Disk usage
df -h

# Network usage
iftop

# Database performance
mysqladmin processlist
```

#### Application Monitoring
```bash
# Laravel logs
tail -f storage/logs/laravel.log

# Queue monitoring
php artisan queue:monitor

# Cache statistics
redis-cli info stats
```

### Performance Optimization

#### Database Optimization
```sql
-- Optimize tables
OPTIMIZE TABLE members, loans, installments;

-- Update statistics
ANALYZE TABLE members, loans, installments;

-- Check query performance
EXPLAIN SELECT * FROM loans WHERE status = 'active';
```

#### Application Optimization
```bash
# Clear and cache configurations
php artisan config:cache
php artisan route:cache
php artisan view:cache

# Optimize Composer autoloader
composer dump-autoload --optimize
```

### Performance Metrics

#### Key Performance Indicators
- **Response Time**: < 2 seconds
- **Database Query Time**: < 100ms
- **Memory Usage**: < 80%
- **CPU Usage**: < 70%
- **Disk Usage**: < 85%

## 🔧 Troubleshooting

### Common Issues

#### Application Errors
```bash
# Check Laravel logs
tail -f storage/logs/laravel.log

# Check PHP-FPM logs
sudo tail -f /var/log/php8.2-fpm.log

# Check Nginx logs
sudo tail -f /var/log/nginx/error.log
```

#### Database Issues
```bash
# Check MySQL status
sudo systemctl status mysql

# Check MySQL logs
sudo tail -f /var/log/mysql/error.log

# Test database connection
php artisan tinker
DB::connection()->getPdo();
```

#### Performance Issues
```bash
# Check system resources
top
free -h
df -h

# Check slow queries
mysql -e "SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 5;"
```

### Emergency Procedures

#### Service Recovery
```bash
# Restart services
sudo systemctl restart nginx
sudo systemctl restart php8.2-fpm
sudo systemctl restart mysql
sudo systemctl restart redis-server

# Check service status
sudo systemctl status nginx php8.2-fpm mysql redis-server
```

#### Data Recovery
```bash
# Restore from backup
mysql sonali_db < /var/backups/sonali/db_latest.sql

# Verify data integrity
php artisan migrate:status
php artisan db:check-integrity
```

## 🛠️ Maintenance Procedures

### Daily Maintenance
- [ ] Check system logs for errors
- [ ] Verify backup completion
- [ ] Monitor system resources
- [ ] Check application performance

### Weekly Maintenance
- [ ] Update system packages
- [ ] Optimize database tables
- [ ] Clean temporary files
- [ ] Review security logs

### Monthly Maintenance
- [ ] Full system backup test
- [ ] Security audit
- [ ] Performance review
- [ ] Update documentation

### Quarterly Maintenance
- [ ] Disaster recovery test
- [ ] Security penetration test
- [ ] Capacity planning review
- [ ] System upgrade planning

---

**Document Version**: 1.0
**Last Updated**: [Current Date]
**Contact**: <EMAIL>

For technical support, contact the system administrator or development team.
