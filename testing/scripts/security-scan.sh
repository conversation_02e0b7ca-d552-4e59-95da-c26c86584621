#!/bin/bash

# Security Scanning Script for Sonali Microfinance
# Performs comprehensive security testing and vulnerability assessment

set -e

echo "🔒 Starting Security Scan for Sonali Microfinance..."

# Configuration
PROJECT_ROOT="/var/www/sonali"
SCAN_RESULTS_DIR="$PROJECT_ROOT/testing/results/security"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Create results directory
mkdir -p "$SCAN_RESULTS_DIR"

cd "$PROJECT_ROOT"

# Step 1: Dependency Vulnerability Scan
print_step "1. Scanning PHP dependencies for vulnerabilities..."

if command -v composer &> /dev/null; then
    composer audit --format=json > "$SCAN_RESULTS_DIR/composer_audit_$TIMESTAMP.json" 2>&1 || true
    print_status "PHP dependency scan completed"
else
    print_warning "Composer not found, skipping PHP dependency scan"
fi

# Step 2: NPM Vulnerability Scan
print_step "2. Scanning NPM dependencies for vulnerabilities..."

if command -v npm &> /dev/null; then
    npm audit --json > "$SCAN_RESULTS_DIR/npm_audit_$TIMESTAMP.json" 2>&1 || true
    print_status "NPM dependency scan completed"
else
    print_warning "NPM not found, skipping NPM dependency scan"
fi

# Step 3: Static Code Analysis
print_step "3. Running static code analysis..."

# PHP Security Checker (if available)
if command -v phpcs &> /dev/null; then
    phpcs --standard=Security app/ > "$SCAN_RESULTS_DIR/phpcs_security_$TIMESTAMP.txt" 2>&1 || true
    print_status "PHP security analysis completed"
fi

# Step 4: File Permission Check
print_step "4. Checking file permissions..."

cat > "$SCAN_RESULTS_DIR/file_permissions_$TIMESTAMP.txt" << EOF
File Permission Security Check - $(date)
========================================

Checking for world-writable files:
$(find . -type f -perm 0002 2>/dev/null || echo "None found")

Checking for files with 777 permissions:
$(find . -type f -perm 0777 2>/dev/null || echo "None found")

Checking for SUID files:
$(find . -type f -perm -4000 2>/dev/null || echo "None found")

Checking for SGID files:
$(find . -type f -perm -2000 2>/dev/null || echo "None found")

Checking sensitive file permissions:
$(ls -la .env* 2>/dev/null || echo ".env files not found")
$(ls -la config/ 2>/dev/null | head -5)
$(ls -la storage/ 2>/dev/null | head -5)
EOF

print_status "File permission check completed"

# Step 5: Configuration Security Check
print_step "5. Checking configuration security..."

cat > "$SCAN_RESULTS_DIR/config_security_$TIMESTAMP.txt" << EOF
Configuration Security Check - $(date)
=====================================

APP_DEBUG setting:
$(grep "APP_DEBUG" .env 2>/dev/null || echo "APP_DEBUG not found in .env")

APP_ENV setting:
$(grep "APP_ENV" .env 2>/dev/null || echo "APP_ENV not found in .env")

Database credentials check:
$(grep "DB_" .env 2>/dev/null | grep -v "DB_PASSWORD" || echo "Database config not found")

Session security:
$(grep "SESSION_" .env 2>/dev/null || echo "Session config not found")

Mail configuration:
$(grep "MAIL_" .env 2>/dev/null | grep -v "MAIL_PASSWORD" || echo "Mail config not found")
EOF

print_status "Configuration security check completed"

# Step 6: Web Application Security Test
print_step "6. Running web application security tests..."

# Test for common vulnerabilities
cat > "$SCAN_RESULTS_DIR/webapp_security_$TIMESTAMP.txt" << EOF
Web Application Security Test - $(date)
======================================

Testing for common security headers:
EOF

# Test security headers (if curl is available)
if command -v curl &> /dev/null; then
    echo "Testing security headers for https://www.sonalibd.org..." >> "$SCAN_RESULTS_DIR/webapp_security_$TIMESTAMP.txt"
    
    # Test various security headers
    curl -I -s https://www.sonalibd.org 2>/dev/null | grep -E "(X-Frame-Options|X-Content-Type-Options|X-XSS-Protection|Strict-Transport-Security|Content-Security-Policy)" >> "$SCAN_RESULTS_DIR/webapp_security_$TIMESTAMP.txt" 2>&1 || echo "Could not retrieve headers" >> "$SCAN_RESULTS_DIR/webapp_security_$TIMESTAMP.txt"
else
    echo "curl not available, skipping header tests" >> "$SCAN_RESULTS_DIR/webapp_security_$TIMESTAMP.txt"
fi

print_status "Web application security test completed"

# Step 7: Database Security Check
print_step "7. Checking database security..."

cat > "$SCAN_RESULTS_DIR/database_security_$TIMESTAMP.txt" << EOF
Database Security Check - $(date)
=================================

MySQL version:
$(mysql --version 2>/dev/null || echo "MySQL not accessible")

Database user privileges check:
EOF

# Check database security (if mysql is accessible)
if command -v mysql &> /dev/null && [ -f .env ]; then
    DB_USER=$(grep "DB_USERNAME" .env | cut -d'=' -f2)
    DB_PASS=$(grep "DB_PASSWORD" .env | cut -d'=' -f2)
    DB_NAME=$(grep "DB_DATABASE" .env | cut -d'=' -f2)
    
    if [ ! -z "$DB_USER" ] && [ ! -z "$DB_PASS" ]; then
        mysql -u"$DB_USER" -p"$DB_PASS" -e "SHOW GRANTS;" 2>/dev/null >> "$SCAN_RESULTS_DIR/database_security_$TIMESTAMP.txt" || echo "Could not check database privileges" >> "$SCAN_RESULTS_DIR/database_security_$TIMESTAMP.txt"
    fi
else
    echo "Database not accessible for security check" >> "$SCAN_RESULTS_DIR/database_security_$TIMESTAMP.txt"
fi

print_status "Database security check completed"

# Step 8: SSL/TLS Security Check
print_step "8. Checking SSL/TLS configuration..."

if command -v openssl &> /dev/null; then
    cat > "$SCAN_RESULTS_DIR/ssl_security_$TIMESTAMP.txt" << EOF
SSL/TLS Security Check - $(date)
===============================

SSL Certificate Information:
$(echo | openssl s_client -servername www.sonalibd.org -connect www.sonalibd.org:443 2>/dev/null | openssl x509 -noout -text | grep -E "(Subject:|Issuer:|Not Before:|Not After:)" || echo "Could not retrieve SSL certificate info")

SSL Configuration Test:
$(echo | openssl s_client -servername www.sonalibd.org -connect www.sonalibd.org:443 2>/dev/null | grep -E "(Protocol|Cipher)" || echo "Could not test SSL configuration")
EOF
    print_status "SSL/TLS security check completed"
else
    print_warning "OpenSSL not found, skipping SSL/TLS check"
fi

# Step 9: Log Analysis for Security Events
print_step "9. Analyzing logs for security events..."

cat > "$SCAN_RESULTS_DIR/log_security_$TIMESTAMP.txt" << EOF
Security Log Analysis - $(date)
==============================

Recent authentication failures:
$(grep -i "failed\|error\|unauthorized" storage/logs/laravel.log 2>/dev/null | tail -10 || echo "No recent authentication failures found")

Recent suspicious activities:
$(grep -i "injection\|script\|hack\|attack" storage/logs/laravel.log 2>/dev/null | tail -10 || echo "No suspicious activities found")

System authentication logs:
$(sudo grep -i "authentication failure\|invalid user\|failed password" /var/log/auth.log 2>/dev/null | tail -5 || echo "System auth logs not accessible")
EOF

print_status "Log analysis completed"

# Step 10: Generate Security Report
print_step "10. Generating comprehensive security report..."

cat > "$SCAN_RESULTS_DIR/security_report_$TIMESTAMP.html" << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Security Scan Report - Sonali Microfinance</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #c0392b; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .critical { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .info { background: #d1ecf1; border-color: #bee5eb; }
        .pass { background: #d4edda; border-color: #c3e6cb; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔒 Security Scan Report</h1>
        <p>Sonali Microfinance System</p>
        <p>Generated: $(date)</p>
    </div>

    <div class="section info">
        <h2>📋 Scan Summary</h2>
        <p>This report contains the results of a comprehensive security scan performed on the Sonali Microfinance system.</p>
        <ul>
            <li>Dependency vulnerability scan</li>
            <li>Static code analysis</li>
            <li>Configuration security check</li>
            <li>File permission audit</li>
            <li>Web application security test</li>
            <li>Database security assessment</li>
            <li>SSL/TLS configuration review</li>
            <li>Security log analysis</li>
        </ul>
    </div>

    <div class="section">
        <h2>🔍 Scan Results</h2>
        <table>
            <tr>
                <th>Security Check</th>
                <th>Status</th>
                <th>Details</th>
            </tr>
            <tr>
                <td>Dependency Vulnerabilities</td>
                <td>$([ -f "$SCAN_RESULTS_DIR/composer_audit_$TIMESTAMP.json" ] && echo "✅ Completed" || echo "⚠️ Skipped")</td>
                <td>PHP and NPM dependencies scanned</td>
            </tr>
            <tr>
                <td>File Permissions</td>
                <td>✅ Completed</td>
                <td>File system permissions audited</td>
            </tr>
            <tr>
                <td>Configuration Security</td>
                <td>✅ Completed</td>
                <td>Application configuration reviewed</td>
            </tr>
            <tr>
                <td>Web Application Security</td>
                <td>✅ Completed</td>
                <td>Security headers and configuration tested</td>
            </tr>
            <tr>
                <td>Database Security</td>
                <td>$(command -v mysql &> /dev/null && echo "✅ Completed" || echo "⚠️ Limited")</td>
                <td>Database access and privileges checked</td>
            </tr>
            <tr>
                <td>SSL/TLS Security</td>
                <td>$(command -v openssl &> /dev/null && echo "✅ Completed" || echo "⚠️ Skipped")</td>
                <td>SSL certificate and configuration verified</td>
            </tr>
            <tr>
                <td>Log Analysis</td>
                <td>✅ Completed</td>
                <td>Security events and anomalies reviewed</td>
            </tr>
        </table>
    </div>

    <div class="section">
        <h2>📊 Detailed Results</h2>
        <p>Detailed scan results are available in the following files:</p>
        <ul>
            <li><a href="composer_audit_$TIMESTAMP.json">PHP Dependency Audit</a></li>
            <li><a href="npm_audit_$TIMESTAMP.json">NPM Dependency Audit</a></li>
            <li><a href="file_permissions_$TIMESTAMP.txt">File Permissions Check</a></li>
            <li><a href="config_security_$TIMESTAMP.txt">Configuration Security</a></li>
            <li><a href="webapp_security_$TIMESTAMP.txt">Web Application Security</a></li>
            <li><a href="database_security_$TIMESTAMP.txt">Database Security</a></li>
            <li><a href="ssl_security_$TIMESTAMP.txt">SSL/TLS Security</a></li>
            <li><a href="log_security_$TIMESTAMP.txt">Security Log Analysis</a></li>
        </ul>
    </div>

    <div class="section warning">
        <h2>⚠️ Recommendations</h2>
        <ul>
            <li>Review and address any dependency vulnerabilities found</li>
            <li>Ensure all sensitive files have proper permissions</li>
            <li>Verify APP_DEBUG is set to false in production</li>
            <li>Implement all recommended security headers</li>
            <li>Regularly update SSL certificates</li>
            <li>Monitor logs for suspicious activities</li>
            <li>Perform regular security scans</li>
            <li>Keep all software components updated</li>
        </ul>
    </div>

    <div class="section">
        <h2>📞 Support</h2>
        <p>For security concerns or questions about this report:</p>
        <ul>
            <li>Security Team: <EMAIL></li>
            <li>System Administrator: <EMAIL></li>
            <li>Emergency Contact: +880-XXXX-XXXXXX</li>
        </ul>
    </div>
</body>
</html>
EOF

print_status "Security report generated"

# Step 11: Summary
print_step "11. Security scan summary..."

echo ""
print_status "🎉 Security Scan Completed!"
echo ""
print_status "Scan Results:"
echo "  Timestamp: $TIMESTAMP"
echo "  Results Directory: $SCAN_RESULTS_DIR"
echo "  HTML Report: security_report_$TIMESTAMP.html"
echo ""
print_status "Files Generated:"
ls -la "$SCAN_RESULTS_DIR"/*_$TIMESTAMP.* 2>/dev/null || echo "  No files generated"
echo ""

# Send notification (if mail is configured)
if command -v mail &> /dev/null; then
    echo "Security scan completed for Sonali Microfinance. Report: $SCAN_RESULTS_DIR/security_report_$TIMESTAMP.html" | mail -s "Security Scan Report" <EMAIL>
    print_status "Notification sent to security team"
fi

print_warning "Please review all generated reports and address any security issues found."
print_status "Regular security scans should be performed monthly or after any system changes."

echo ""
print_status "🔒 Security scan completed successfully!"
