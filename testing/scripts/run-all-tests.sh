#!/bin/bash

# Comprehensive Test Execution Script for Sonali Microfinance
# This script runs all test suites and generates comprehensive reports

set -e

echo "🧪 Starting Comprehensive Test Suite for Sonali Microfinance..."

# Configuration
PROJECT_ROOT="/var/www/sonali"
TEST_RESULTS_DIR="$PROJECT_ROOT/testing/results"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
REPORT_FILE="$TEST_RESULTS_DIR/test_report_$TIMESTAMP.html"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Function to log test results
log_result() {
    local test_type="$1"
    local status="$2"
    local details="$3"
    echo "$(date '+%Y-%m-%d %H:%M:%S') | $test_type | $status | $details" >> "$TEST_RESULTS_DIR/test_log_$TIMESTAMP.txt"
}

# Create results directory
mkdir -p "$TEST_RESULTS_DIR"

# Change to project directory
cd "$PROJECT_ROOT"

# Initialize test report
cat > "$REPORT_FILE" << 'EOF'
<!DOCTYPE html>
<html>
<head>
    <title>Sonali Microfinance - Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .pass { background: #d4edda; border-color: #c3e6cb; }
        .fail { background: #f8d7da; border-color: #f5c6cb; }
        .warning { background: #fff3cd; border-color: #ffeaa7; }
        .metrics { display: flex; justify-content: space-around; margin: 20px 0; }
        .metric { text-align: center; padding: 10px; background: #f8f9fa; border-radius: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background: #f2f2f2; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 Sonali Microfinance - Comprehensive Test Report</h1>
        <p>Generated on: $(date)</p>
        <p>Test Suite Version: 1.0</p>
    </div>
EOF

# Step 1: Environment Setup
print_step "1. Setting up test environment..."

# Clear cache and optimize
php artisan config:clear
php artisan cache:clear
php artisan route:clear
php artisan view:clear

# Prepare test database
php artisan migrate:fresh --env=testing --force
php artisan db:seed --env=testing --force

print_status "Test environment prepared"

# Step 2: Unit Tests
print_step "2. Running Unit Tests..."

echo "<div class='section'><h2>📋 Unit Tests</h2>" >> "$REPORT_FILE"

if php artisan test --testsuite=Unit --coverage-html="$TEST_RESULTS_DIR/coverage" --log-junit="$TEST_RESULTS_DIR/unit_results.xml" > "$TEST_RESULTS_DIR/unit_output.txt" 2>&1; then
    print_status "Unit tests passed"
    log_result "Unit Tests" "PASS" "All unit tests completed successfully"
    echo "<div class='pass'><h3>✅ Unit Tests - PASSED</h3>" >> "$REPORT_FILE"
else
    print_error "Unit tests failed"
    log_result "Unit Tests" "FAIL" "Some unit tests failed"
    echo "<div class='fail'><h3>❌ Unit Tests - FAILED</h3>" >> "$REPORT_FILE"
fi

echo "<pre>$(cat $TEST_RESULTS_DIR/unit_output.txt)</pre></div>" >> "$REPORT_FILE"

# Step 3: Feature Tests
print_step "3. Running Feature Tests..."

echo "<div class='section'><h2>🔧 Feature Tests</h2>" >> "$REPORT_FILE"

if php artisan test --testsuite=Feature --log-junit="$TEST_RESULTS_DIR/feature_results.xml" > "$TEST_RESULTS_DIR/feature_output.txt" 2>&1; then
    print_status "Feature tests passed"
    log_result "Feature Tests" "PASS" "All feature tests completed successfully"
    echo "<div class='pass'><h3>✅ Feature Tests - PASSED</h3>" >> "$REPORT_FILE"
else
    print_error "Feature tests failed"
    log_result "Feature Tests" "FAIL" "Some feature tests failed"
    echo "<div class='fail'><h3>❌ Feature Tests - FAILED</h3>" >> "$REPORT_FILE"
fi

echo "<pre>$(cat $TEST_RESULTS_DIR/feature_output.txt)</pre></div>" >> "$REPORT_FILE"

# Step 4: Security Tests
print_step "4. Running Security Tests..."

echo "<div class='section'><h2>🔒 Security Tests</h2>" >> "$REPORT_FILE"

if php artisan test testing/security-tests/ --log-junit="$TEST_RESULTS_DIR/security_results.xml" > "$TEST_RESULTS_DIR/security_output.txt" 2>&1; then
    print_status "Security tests passed"
    log_result "Security Tests" "PASS" "All security tests completed successfully"
    echo "<div class='pass'><h3>✅ Security Tests - PASSED</h3>" >> "$REPORT_FILE"
else
    print_error "Security tests failed"
    log_result "Security Tests" "FAIL" "Some security tests failed"
    echo "<div class='fail'><h3>❌ Security Tests - FAILED</h3>" >> "$REPORT_FILE"
fi

echo "<pre>$(cat $TEST_RESULTS_DIR/security_output.txt)</pre></div>" >> "$REPORT_FILE"

# Step 5: Performance Tests
print_step "5. Running Performance Tests..."

echo "<div class='section'><h2>⚡ Performance Tests</h2>" >> "$REPORT_FILE"

if php artisan test testing/performance-tests/ --log-junit="$TEST_RESULTS_DIR/performance_results.xml" > "$TEST_RESULTS_DIR/performance_output.txt" 2>&1; then
    print_status "Performance tests passed"
    log_result "Performance Tests" "PASS" "All performance tests completed successfully"
    echo "<div class='pass'><h3>✅ Performance Tests - PASSED</h3>" >> "$REPORT_FILE"
else
    print_warning "Performance tests had issues"
    log_result "Performance Tests" "WARNING" "Some performance benchmarks not met"
    echo "<div class='warning'><h3>⚠️ Performance Tests - WARNING</h3>" >> "$REPORT_FILE"
fi

echo "<pre>$(cat $TEST_RESULTS_DIR/performance_output.txt)</pre></div>" >> "$REPORT_FILE"

# Step 6: Data Integrity Tests
print_step "6. Running Data Integrity Tests..."

echo "<div class='section'><h2>💾 Data Integrity Tests</h2>" >> "$REPORT_FILE"

if php artisan test testing/data-integrity-tests/ --log-junit="$TEST_RESULTS_DIR/data_results.xml" > "$TEST_RESULTS_DIR/data_output.txt" 2>&1; then
    print_status "Data integrity tests passed"
    log_result "Data Integrity Tests" "PASS" "All data integrity tests completed successfully"
    echo "<div class='pass'><h3>✅ Data Integrity Tests - PASSED</h3>" >> "$REPORT_FILE"
else
    print_error "Data integrity tests failed"
    log_result "Data Integrity Tests" "FAIL" "Some data integrity tests failed"
    echo "<div class='fail'><h3>❌ Data Integrity Tests - FAILED</h3>" >> "$REPORT_FILE"
fi

echo "<pre>$(cat $TEST_RESULTS_DIR/data_output.txt)</pre></div>" >> "$REPORT_FILE"

# Step 7: Code Quality Analysis
print_step "7. Running Code Quality Analysis..."

echo "<div class='section'><h2>📊 Code Quality Analysis</h2>" >> "$REPORT_FILE"

# Run PHP CodeSniffer (if available)
if command -v phpcs &> /dev/null; then
    phpcs --standard=PSR12 app/ > "$TEST_RESULTS_DIR/phpcs_output.txt" 2>&1 || true
    echo "<h3>PHP CodeSniffer Results</h3>" >> "$REPORT_FILE"
    echo "<pre>$(cat $TEST_RESULTS_DIR/phpcs_output.txt)</pre>" >> "$REPORT_FILE"
fi

# Run PHP Mess Detector (if available)
if command -v phpmd &> /dev/null; then
    phpmd app/ html cleancode,codesize,controversial,design,naming,unusedcode > "$TEST_RESULTS_DIR/phpmd_output.html" 2>&1 || true
    echo "<h3>PHP Mess Detector Results</h3>" >> "$REPORT_FILE"
    echo "<iframe src='phpmd_output.html' width='100%' height='400px'></iframe>" >> "$REPORT_FILE"
fi

echo "</div>" >> "$REPORT_FILE"

# Step 8: Generate Test Metrics
print_step "8. Generating Test Metrics..."

# Calculate test metrics
TOTAL_TESTS=$(find "$TEST_RESULTS_DIR" -name "*.xml" -exec grep -h "tests=" {} \; | sed 's/.*tests="\([0-9]*\)".*/\1/' | awk '{sum += $1} END {print sum}')
TOTAL_FAILURES=$(find "$TEST_RESULTS_DIR" -name "*.xml" -exec grep -h "failures=" {} \; | sed 's/.*failures="\([0-9]*\)".*/\1/' | awk '{sum += $1} END {print sum}')
TOTAL_ERRORS=$(find "$TEST_RESULTS_DIR" -name "*.xml" -exec grep -h "errors=" {} \; | sed 's/.*errors="\([0-9]*\)".*/\1/' | awk '{sum += $1} END {print sum}')

PASS_RATE=$(echo "scale=2; (($TOTAL_TESTS - $TOTAL_FAILURES - $TOTAL_ERRORS) / $TOTAL_TESTS) * 100" | bc -l)

# Add metrics to report
cat >> "$REPORT_FILE" << EOF
<div class="section">
    <h2>📈 Test Metrics Summary</h2>
    <div class="metrics">
        <div class="metric">
            <h3>Total Tests</h3>
            <p style="font-size: 24px; color: #2c3e50;">$TOTAL_TESTS</p>
        </div>
        <div class="metric">
            <h3>Pass Rate</h3>
            <p style="font-size: 24px; color: #27ae60;">$PASS_RATE%</p>
        </div>
        <div class="metric">
            <h3>Failures</h3>
            <p style="font-size: 24px; color: #e74c3c;">$TOTAL_FAILURES</p>
        </div>
        <div class="metric">
            <h3>Errors</h3>
            <p style="font-size: 24px; color: #f39c12;">$TOTAL_ERRORS</p>
        </div>
    </div>
    
    <table>
        <tr>
            <th>Test Suite</th>
            <th>Status</th>
            <th>Tests</th>
            <th>Failures</th>
            <th>Errors</th>
        </tr>
EOF

# Add individual test suite results
for xml_file in "$TEST_RESULTS_DIR"/*.xml; do
    if [ -f "$xml_file" ]; then
        suite_name=$(basename "$xml_file" .xml | sed 's/_results//' | sed 's/_/ /g' | sed 's/\b\w/\U&/g')
        tests=$(grep -o 'tests="[0-9]*"' "$xml_file" | sed 's/tests="\([0-9]*\)"/\1/')
        failures=$(grep -o 'failures="[0-9]*"' "$xml_file" | sed 's/failures="\([0-9]*\)"/\1/')
        errors=$(grep -o 'errors="[0-9]*"' "$xml_file" | sed 's/errors="\([0-9]*\)"/\1/')
        
        if [ "$failures" -eq 0 ] && [ "$errors" -eq 0 ]; then
            status="✅ PASS"
        else
            status="❌ FAIL"
        fi
        
        echo "<tr><td>$suite_name</td><td>$status</td><td>$tests</td><td>$failures</td><td>$errors</td></tr>" >> "$REPORT_FILE"
    fi
done

echo "</table></div>" >> "$REPORT_FILE"

# Step 9: Generate Coverage Report Link
if [ -d "$TEST_RESULTS_DIR/coverage" ]; then
    echo "<div class='section'><h2>📊 Code Coverage Report</h2>" >> "$REPORT_FILE"
    echo "<p><a href='coverage/index.html' target='_blank'>View Detailed Coverage Report</a></p></div>" >> "$REPORT_FILE"
fi

# Close HTML report
cat >> "$REPORT_FILE" << 'EOF'
    <div class="section">
        <h2>📝 Recommendations</h2>
        <ul>
            <li>Maintain test coverage above 80%</li>
            <li>Fix all critical security vulnerabilities</li>
            <li>Optimize performance bottlenecks identified</li>
            <li>Review and update test cases regularly</li>
            <li>Implement continuous integration for automated testing</li>
        </ul>
    </div>
    
    <div class="section">
        <h2>📞 Support</h2>
        <p>For questions about test results or failures, contact:</p>
        <ul>
            <li>QA Team: <EMAIL></li>
            <li>Development Team: <EMAIL></li>
            <li>System Administrator: <EMAIL></li>
        </ul>
    </div>
</body>
</html>
EOF

# Step 10: Generate Summary
print_step "10. Generating Test Summary..."

echo ""
print_status "🎉 Test Execution Completed!"
echo ""
print_status "Test Summary:"
echo "  Total Tests: $TOTAL_TESTS"
echo "  Pass Rate: $PASS_RATE%"
echo "  Failures: $TOTAL_FAILURES"
echo "  Errors: $TOTAL_ERRORS"
echo ""
print_status "Reports Generated:"
echo "  HTML Report: $REPORT_FILE"
echo "  Test Log: $TEST_RESULTS_DIR/test_log_$TIMESTAMP.txt"
echo "  Coverage Report: $TEST_RESULTS_DIR/coverage/index.html"
echo ""

# Send notification (if configured)
if command -v mail &> /dev/null; then
    echo "Test execution completed. Pass rate: $PASS_RATE%. View report: $REPORT_FILE" | mail -s "Test Report - Sonali Microfinance" <EMAIL>
fi

# Exit with appropriate code
if [ "$TOTAL_FAILURES" -eq 0 ] && [ "$TOTAL_ERRORS" -eq 0 ]; then
    print_status "All tests passed! ✅"
    exit 0
else
    print_error "Some tests failed! ❌"
    exit 1
fi
