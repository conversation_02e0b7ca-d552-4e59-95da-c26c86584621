<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\LoanApplication;
use App\Models\Loan;
use App\Models\Installment;
use Carbon\Carbon;

class LoanManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $manager;
    protected $fieldOfficer;
    protected $branch;
    protected $member;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test branch
        $this->branch = Branch::factory()->create([
            'name' => 'Test Branch',
            'code' => 'TB001',
            'is_active' => true,
        ]);

        // Create test users
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $this->manager = User::factory()->create([
            'role' => 'manager',
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        $this->fieldOfficer = User::factory()->create([
            'role' => 'field_officer',
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        // Create test member
        $this->member = Member::factory()->create([
            'branch_id' => $this->branch->id,
            'is_active' => true,
            'monthly_income' => 50000,
        ]);
    }

    /** @test */
    public function can_create_loan_application_with_valid_data()
    {
        $applicationData = [
            'member_id' => $this->member->id,
            'loan_amount' => 100000,
            'loan_purpose' => 'Business expansion',
            'loan_term_months' => 12,
            'interest_rate' => 15.0,
            'guarantor_name' => 'John Guarantor',
            'guarantor_phone' => '***********',
            'guarantor_address' => '123 Guarantor St',
            'collateral_description' => 'Gold ornaments',
            'collateral_value' => 150000,
        ];

        $response = $this->actingAs($this->fieldOfficer)
            ->post('/field-officer/loan-applications', $applicationData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('loan_applications', [
            'member_id' => $this->member->id,
            'loan_amount' => 100000,
            'status' => 'pending',
            'applied_by' => $this->fieldOfficer->id,
        ]);
    }

    /** @test */
    public function validates_loan_amount_limits()
    {
        $applicationData = [
            'member_id' => $this->member->id,
            'loan_amount' => 1000000, // Exceeds maximum limit
            'loan_purpose' => 'Business expansion',
            'loan_term_months' => 12,
        ];

        $response = $this->actingAs($this->fieldOfficer)
            ->post('/field-officer/loan-applications', $applicationData);

        $response->assertSessionHasErrors('loan_amount');
    }

    /** @test */
    public function validates_member_eligibility_for_new_loan()
    {
        // Create existing active loan for member
        $existingLoan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'status' => 'active',
        ]);

        $applicationData = [
            'member_id' => $this->member->id,
            'loan_amount' => 50000,
            'loan_purpose' => 'Business expansion',
            'loan_term_months' => 12,
        ];

        $response = $this->actingAs($this->fieldOfficer)
            ->post('/field-officer/loan-applications', $applicationData);

        $response->assertSessionHasErrors('member_id');
    }

    /** @test */
    public function manager_can_approve_loan_application()
    {
        $application = LoanApplication::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'status' => 'pending',
            'loan_amount' => 100000,
            'loan_term_months' => 12,
            'interest_rate' => 15.0,
        ]);

        $response = $this->actingAs($this->manager)
            ->patch("/manager/loan-applications/{$application->id}/approve");

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('loan_applications', [
            'id' => $application->id,
            'status' => 'approved',
            'approved_by' => $this->manager->id,
        ]);

        // Check if loan was created
        $this->assertDatabaseHas('loans', [
            'member_id' => $this->member->id,
            'loan_amount' => 100000,
            'status' => 'active',
        ]);
    }

    /** @test */
    public function manager_can_reject_loan_application()
    {
        $application = LoanApplication::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'status' => 'pending',
        ]);

        $rejectionData = [
            'rejection_reason' => 'Insufficient income documentation',
        ];

        $response = $this->actingAs($this->manager)
            ->patch("/manager/loan-applications/{$application->id}/reject", $rejectionData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('loan_applications', [
            'id' => $application->id,
            'status' => 'rejected',
            'rejection_reason' => 'Insufficient income documentation',
            'reviewed_by' => $this->manager->id,
        ]);
    }

    /** @test */
    public function loan_approval_creates_installment_schedule()
    {
        $application = LoanApplication::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'status' => 'pending',
            'loan_amount' => 120000,
            'loan_term_months' => 12,
            'interest_rate' => 15.0,
        ]);

        $this->actingAs($this->manager)
            ->patch("/manager/loan-applications/{$application->id}/approve");

        $loan = Loan::where('member_id', $this->member->id)->first();
        $this->assertNotNull($loan);

        // Check installment schedule creation
        $installments = Installment::where('loan_id', $loan->id)->get();
        $this->assertEquals(12, $installments->count());

        // Verify installment calculations
        $monthlyInstallment = $installments->first();
        $this->assertGreaterThan(0, $monthlyInstallment->principal_amount);
        $this->assertGreaterThan(0, $monthlyInstallment->interest_amount);
        $this->assertEquals(
            $monthlyInstallment->principal_amount + $monthlyInstallment->interest_amount,
            $monthlyInstallment->total_amount
        );
    }

    /** @test */
    public function can_calculate_loan_interest_correctly()
    {
        $loanAmount = 100000;
        $interestRate = 15.0;
        $termMonths = 12;

        $application = LoanApplication::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_amount' => $loanAmount,
            'loan_term_months' => $termMonths,
            'interest_rate' => $interestRate,
            'status' => 'pending',
        ]);

        $this->actingAs($this->manager)
            ->patch("/manager/loan-applications/{$application->id}/approve");

        $loan = Loan::where('member_id', $this->member->id)->first();
        
        // Calculate expected total interest
        $expectedTotalInterest = ($loanAmount * $interestRate * $termMonths) / (12 * 100);
        $expectedTotalAmount = $loanAmount + $expectedTotalInterest;

        $this->assertEquals($expectedTotalAmount, $loan->total_amount);
        $this->assertEquals($expectedTotalInterest, $loan->total_interest);
    }

    /** @test */
    public function can_view_loan_details()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
        ]);

        $response = $this->actingAs($this->manager)
            ->get("/manager/loans/{$loan->id}");

        $response->assertStatus(200);
        $response->assertSee($loan->loan_number);
        $response->assertSee($this->member->name);
        $response->assertSee(number_format($loan->loan_amount));
    }

    /** @test */
    public function can_search_loans_by_loan_number()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_number' => 'LN001',
        ]);

        $response = $this->actingAs($this->manager)
            ->get('/manager/loans?search=LN001');

        $response->assertStatus(200);
        $response->assertSee('LN001');
        $response->assertSee($this->member->name);
    }

    /** @test */
    public function can_filter_loans_by_status()
    {
        Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'status' => 'active',
        ]);

        $completedMember = Member::factory()->create(['branch_id' => $this->branch->id]);
        Loan::factory()->create([
            'member_id' => $completedMember->id,
            'branch_id' => $this->branch->id,
            'status' => 'completed',
        ]);

        $response = $this->actingAs($this->manager)
            ->get('/manager/loans?status=active');

        $response->assertStatus(200);
        $response->assertSee($this->member->name);
        $response->assertDontSee($completedMember->name);
    }

    /** @test */
    public function can_view_overdue_loans()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'status' => 'active',
        ]);

        // Create overdue installment
        Installment::factory()->create([
            'loan_id' => $loan->id,
            'due_date' => Carbon::now()->subDays(10),
            'status' => 'pending',
        ]);

        $response = $this->actingAs($this->manager)
            ->get('/manager/loans/overdue');

        $response->assertStatus(200);
        $response->assertSee($loan->loan_number);
        $response->assertSee('Overdue');
    }

    /** @test */
    public function can_calculate_early_settlement_amount()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'loan_amount' => 100000,
            'total_amount' => 115000,
            'status' => 'active',
        ]);

        // Create some paid installments
        Installment::factory()->count(3)->create([
            'loan_id' => $loan->id,
            'status' => 'paid',
            'total_amount' => 9583.33,
        ]);

        // Create remaining installments
        Installment::factory()->count(9)->create([
            'loan_id' => $loan->id,
            'status' => 'pending',
            'total_amount' => 9583.33,
        ]);

        $response = $this->actingAs($this->manager)
            ->get("/manager/loans/{$loan->id}/early-settlement");

        $response->assertStatus(200);
        $response->assertJsonStructure([
            'remaining_principal',
            'early_settlement_amount',
            'discount_amount',
        ]);
    }

    /** @test */
    public function can_process_early_loan_settlement()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
            'status' => 'active',
        ]);

        $settlementData = [
            'settlement_amount' => 85000,
            'settlement_date' => Carbon::now()->format('Y-m-d'),
            'notes' => 'Early settlement with discount',
        ];

        $response = $this->actingAs($this->manager)
            ->post("/manager/loans/{$loan->id}/settle", $settlementData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('loans', [
            'id' => $loan->id,
            'status' => 'completed',
            'settlement_date' => $settlementData['settlement_date'],
        ]);
    }

    /** @test */
    public function validates_loan_application_required_fields()
    {
        $response = $this->actingAs($this->fieldOfficer)
            ->post('/field-officer/loan-applications', []);

        $response->assertSessionHasErrors([
            'member_id',
            'loan_amount',
            'loan_purpose',
            'loan_term_months',
        ]);
    }

    /** @test */
    public function field_officer_can_only_create_applications_for_their_branch()
    {
        $otherBranch = Branch::factory()->create();
        $otherMember = Member::factory()->create(['branch_id' => $otherBranch->id]);

        $applicationData = [
            'member_id' => $otherMember->id,
            'loan_amount' => 50000,
            'loan_purpose' => 'Business',
            'loan_term_months' => 12,
        ];

        $response = $this->actingAs($this->fieldOfficer)
            ->post('/field-officer/loan-applications', $applicationData);

        $response->assertSessionHasErrors('member_id');
    }

    /** @test */
    public function can_generate_loan_agreement_document()
    {
        $loan = Loan::factory()->create([
            'member_id' => $this->member->id,
            'branch_id' => $this->branch->id,
        ]);

        $response = $this->actingAs($this->manager)
            ->get("/manager/loans/{$loan->id}/agreement");

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/pdf');
    }

    /** @test */
    public function can_export_loan_report()
    {
        Loan::factory()->count(5)->create([
            'branch_id' => $this->branch->id,
        ]);

        $response = $this->actingAs($this->manager)
            ->get('/manager/loans/export');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }

    /** @test */
    public function admin_can_view_all_branch_loans()
    {
        $anotherBranch = Branch::factory()->create();
        
        Loan::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        Loan::factory()->create([
            'branch_id' => $anotherBranch->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get('/admin/loans');

        $response->assertStatus(200);
        // Admin should see loans from all branches
        $this->assertEquals(2, Loan::count());
    }
}
