<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;

class MemberManagementTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $admin;
    protected $manager;
    protected $fieldOfficer;
    protected $branch;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create test branch
        $this->branch = Branch::factory()->create([
            'name' => 'Test Branch',
            'code' => 'TB001',
            'is_active' => true,
        ]);

        // Create test users
        $this->admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $this->manager = User::factory()->create([
            'role' => 'manager',
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        $this->fieldOfficer = User::factory()->create([
            'role' => 'field_officer',
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        // Setup storage for testing
        Storage::fake('public');
    }

    /** @test */
    public function admin_can_access_member_management_page()
    {
        $response = $this->actingAs($this->admin)
            ->get('/admin/members');

        $response->assertStatus(200);
        $response->assertSee('Member Management');
        $response->assertSee('Add New Member');
    }

    /** @test */
    public function manager_can_access_member_management_for_their_branch()
    {
        $response = $this->actingAs($this->manager)
            ->get('/manager/members');

        $response->assertStatus(200);
        $response->assertSee('Member Management');
    }

    /** @test */
    public function field_officer_can_access_member_management_for_their_branch()
    {
        $response = $this->actingAs($this->fieldOfficer)
            ->get('/field-officer/members');

        $response->assertStatus(200);
        $response->assertSee('Member Management');
    }

    /** @test */
    public function can_create_new_member_with_valid_data()
    {
        $memberData = [
            'name' => 'John Doe',
            'father_name' => 'Robert Doe',
            'mother_name' => 'Jane Doe',
            'nid_number' => '1234567890123',
            'phone_number' => '***********',
            'email' => '<EMAIL>',
            'date_of_birth' => '1990-01-01',
            'gender' => 'male',
            'marital_status' => 'married',
            'occupation' => 'Business',
            'monthly_income' => 50000,
            'present_address' => '123 Main St, Dhaka',
            'permanent_address' => '123 Main St, Dhaka',
            'branch_id' => $this->branch->id,
            'photo' => UploadedFile::fake()->image('member.jpg', 300, 300),
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/members', $memberData);

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('members', [
            'name' => 'John Doe',
            'nid_number' => '1234567890123',
            'phone_number' => '***********',
            'branch_id' => $this->branch->id,
        ]);

        // Verify photo was uploaded
        $member = Member::where('nid_number', '1234567890123')->first();
        $this->assertNotNull($member->photo);
        Storage::disk('public')->assertExists($member->photo);
    }

    /** @test */
    public function cannot_create_member_with_duplicate_nid()
    {
        // Create existing member
        Member::factory()->create([
            'nid_number' => '1234567890123',
            'branch_id' => $this->branch->id,
        ]);

        $memberData = [
            'name' => 'Jane Doe',
            'nid_number' => '1234567890123', // Duplicate NID
            'phone_number' => '01712345679',
            'branch_id' => $this->branch->id,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/members', $memberData);

        $response->assertSessionHasErrors('nid_number');
        $this->assertEquals(1, Member::where('nid_number', '1234567890123')->count());
    }

    /** @test */
    public function cannot_create_member_with_duplicate_phone()
    {
        // Create existing member
        Member::factory()->create([
            'phone_number' => '***********',
            'branch_id' => $this->branch->id,
        ]);

        $memberData = [
            'name' => 'Jane Doe',
            'nid_number' => '1234567890124',
            'phone_number' => '***********', // Duplicate phone
            'branch_id' => $this->branch->id,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/members', $memberData);

        $response->assertSessionHasErrors('phone_number');
    }

    /** @test */
    public function validates_nid_number_format()
    {
        $memberData = [
            'name' => 'John Doe',
            'nid_number' => '123456789', // Invalid NID format
            'phone_number' => '***********',
            'branch_id' => $this->branch->id,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/members', $memberData);

        $response->assertSessionHasErrors('nid_number');
    }

    /** @test */
    public function validates_phone_number_format()
    {
        $memberData = [
            'name' => 'John Doe',
            'nid_number' => '1234567890123',
            'phone_number' => '123456789', // Invalid phone format
            'branch_id' => $this->branch->id,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/members', $memberData);

        $response->assertSessionHasErrors('phone_number');
    }

    /** @test */
    public function validates_age_restrictions()
    {
        $memberData = [
            'name' => 'Young Person',
            'nid_number' => '1234567890123',
            'phone_number' => '***********',
            'date_of_birth' => now()->subYears(16)->format('Y-m-d'), // Too young
            'branch_id' => $this->branch->id,
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/members', $memberData);

        $response->assertSessionHasErrors('date_of_birth');
    }

    /** @test */
    public function validates_photo_upload()
    {
        $memberData = [
            'name' => 'John Doe',
            'nid_number' => '1234567890123',
            'phone_number' => '***********',
            'branch_id' => $this->branch->id,
            'photo' => UploadedFile::fake()->create('document.pdf', 1000), // Invalid file type
        ];

        $response = $this->actingAs($this->admin)
            ->post('/admin/members', $memberData);

        $response->assertSessionHasErrors('photo');
    }

    /** @test */
    public function can_update_member_information()
    {
        $member = Member::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        $updateData = [
            'name' => 'Updated Name',
            'phone_number' => '01712345679',
            'monthly_income' => 60000,
        ];

        $response = $this->actingAs($this->admin)
            ->put("/admin/members/{$member->id}", array_merge($member->toArray(), $updateData));

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('members', [
            'id' => $member->id,
            'name' => 'Updated Name',
            'phone_number' => '01712345679',
            'monthly_income' => 60000,
        ]);
    }

    /** @test */
    public function can_search_members_by_name()
    {
        Member::factory()->create([
            'name' => 'John Doe',
            'branch_id' => $this->branch->id,
        ]);

        Member::factory()->create([
            'name' => 'Jane Smith',
            'branch_id' => $this->branch->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get('/admin/members?search=John');

        $response->assertStatus(200);
        $response->assertSee('John Doe');
        $response->assertDontSee('Jane Smith');
    }

    /** @test */
    public function can_search_members_by_nid()
    {
        Member::factory()->create([
            'name' => 'John Doe',
            'nid_number' => '1234567890123',
            'branch_id' => $this->branch->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get('/admin/members?search=1234567890123');

        $response->assertStatus(200);
        $response->assertSee('John Doe');
    }

    /** @test */
    public function can_filter_members_by_branch()
    {
        $anotherBranch = Branch::factory()->create();

        Member::factory()->create([
            'name' => 'Branch 1 Member',
            'branch_id' => $this->branch->id,
        ]);

        Member::factory()->create([
            'name' => 'Branch 2 Member',
            'branch_id' => $anotherBranch->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get("/admin/members?branch_id={$this->branch->id}");

        $response->assertStatus(200);
        $response->assertSee('Branch 1 Member');
        $response->assertDontSee('Branch 2 Member');
    }

    /** @test */
    public function can_view_member_details()
    {
        $member = Member::factory()->create([
            'branch_id' => $this->branch->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get("/admin/members/{$member->id}");

        $response->assertStatus(200);
        $response->assertSee($member->name);
        $response->assertSee($member->nid_number);
        $response->assertSee($member->phone_number);
    }

    /** @test */
    public function can_deactivate_member()
    {
        $member = Member::factory()->create([
            'branch_id' => $this->branch->id,
            'is_active' => true,
        ]);

        $response = $this->actingAs($this->admin)
            ->patch("/admin/members/{$member->id}/deactivate");

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('members', [
            'id' => $member->id,
            'is_active' => false,
        ]);
    }

    /** @test */
    public function can_reactivate_member()
    {
        $member = Member::factory()->create([
            'branch_id' => $this->branch->id,
            'is_active' => false,
        ]);

        $response = $this->actingAs($this->admin)
            ->patch("/admin/members/{$member->id}/activate");

        $response->assertRedirect();
        $response->assertSessionHas('success');

        $this->assertDatabaseHas('members', [
            'id' => $member->id,
            'is_active' => true,
        ]);
    }

    /** @test */
    public function manager_can_only_see_their_branch_members()
    {
        $anotherBranch = Branch::factory()->create();

        Member::factory()->create([
            'name' => 'Own Branch Member',
            'branch_id' => $this->branch->id,
        ]);

        Member::factory()->create([
            'name' => 'Other Branch Member',
            'branch_id' => $anotherBranch->id,
        ]);

        $response = $this->actingAs($this->manager)
            ->get('/manager/members');

        $response->assertStatus(200);
        $response->assertSee('Own Branch Member');
        $response->assertDontSee('Other Branch Member');
    }

    /** @test */
    public function validates_required_fields()
    {
        $response = $this->actingAs($this->admin)
            ->post('/admin/members', []);

        $response->assertSessionHasErrors([
            'name',
            'nid_number',
            'phone_number',
            'branch_id',
        ]);
    }

    /** @test */
    public function can_export_members_list()
    {
        Member::factory()->count(5)->create([
            'branch_id' => $this->branch->id,
        ]);

        $response = $this->actingAs($this->admin)
            ->get('/admin/members/export');

        $response->assertStatus(200);
        $response->assertHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    }
}
