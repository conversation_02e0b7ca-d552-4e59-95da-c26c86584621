# MySQL Production Configuration for Sonali Microfinance
# /etc/mysql/mysql.conf.d/sonali-production.cnf

[mysqld]
# Basic Settings
user = mysql
pid-file = /var/run/mysqld/mysqld.pid
socket = /var/run/mysqld/mysqld.sock
port = 3306
basedir = /usr
datadir = /var/lib/mysql
tmpdir = /tmp
lc-messages-dir = /usr/share/mysql

# Network Settings
bind-address = 127.0.0.1
max_connections = 200
max_user_connections = 180
thread_cache_size = 16
table_open_cache = 4000
table_definition_cache = 1400

# Memory Settings
innodb_buffer_pool_size = 1G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT

# Query Cache (disabled for MySQL 8.0+)
# query_cache_type = 1
# query_cache_size = 64M
# query_cache_limit = 2M

# Temporary Tables
tmp_table_size = 64M
max_heap_table_size = 64M

# MyISAM Settings
key_buffer_size = 32M
myisam_sort_buffer_size = 8M

# Connection Settings
wait_timeout = 600
interactive_timeout = 600
connect_timeout = 10

# Slow Query Log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1

# Error Log
log_error = /var/log/mysql/error.log

# Binary Logging
log_bin = /var/log/mysql/mysql-bin.log
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# InnoDB Settings
innodb_file_per_table = 1
innodb_open_files = 400
innodb_io_capacity = 400
innodb_read_io_threads = 4
innodb_write_io_threads = 4
innodb_thread_concurrency = 0
innodb_lock_wait_timeout = 120

# Security Settings
local_infile = 0
skip_show_database
sql_mode = STRICT_TRANS_TABLES,NO_ZERO_DATE,NO_ZERO_IN_DATE,ERROR_FOR_DIVISION_BY_ZERO

# Character Set
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci

# SSL Settings (if needed)
# ssl_ca = /etc/mysql/ssl/ca-cert.pem
# ssl_cert = /etc/mysql/ssl/server-cert.pem
# ssl_key = /etc/mysql/ssl/server-key.pem

[mysql]
default_character_set = utf8mb4

[mysqldump]
quick
quote_names
max_allowed_packet = 16M

[client]
default_character_set = utf8mb4
