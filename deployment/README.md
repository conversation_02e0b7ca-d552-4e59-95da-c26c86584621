# Sonali Microfinance - Production Deployment Guide

This guide provides comprehensive instructions for deploying the Sonali Microfinance application to a production VPS environment.

## 📋 Table of Contents

1. [Prerequisites](#prerequisites)
2. [Server Requirements](#server-requirements)
3. [Installation Process](#installation-process)
4. [Configuration](#configuration)
5. [Security Setup](#security-setup)
6. [SSL Certificate Setup](#ssl-certificate-setup)
7. [Backup & Monitoring](#backup--monitoring)
8. [Maintenance](#maintenance)
9. [Troubleshooting](#troubleshooting)

## 🔧 Prerequisites

### Server Requirements

- **Operating System**: Ubuntu 20.04 LTS or 22.04 LTS
- **RAM**: Minimum 2GB (4GB recommended)
- **Storage**: Minimum 20GB SSD
- **CPU**: 2 cores minimum
- **Network**: Public IP address with domain pointing to server

### Domain Setup

- Domain: `www.sonalibd.org`
- DNS A record pointing to server IP
- Optional: CDN setup for static assets

## 🚀 Installation Process

### Step 1: Initial Server Setup

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Clone or upload application files
cd /var/www
sudo git clone <repository-url> sonali
# OR upload files via SCP/SFTP

cd sonali
```

### Step 2: Run Automated Installation

```bash
# Make installation script executable
sudo chmod +x deployment/scripts/install.sh

# Run complete installation
sudo ./deployment/scripts/install.sh
```

The installation script will:
- Install Nginx, PHP 8.2, MySQL, Redis, Node.js
- Configure all services for production
- Set up security hardening
- Configure backup and monitoring systems
- Set proper file permissions

### Step 3: Manual Configuration (if needed)

If you prefer manual installation, follow these steps:

#### Install Web Server Stack

```bash
# Install Nginx
sudo apt install -y nginx

# Install PHP 8.2
sudo add-apt-repository ppa:ondrej/php
sudo apt update
sudo apt install -y php8.2 php8.2-fpm php8.2-mysql php8.2-xml php8.2-mbstring php8.2-curl php8.2-zip php8.2-gd php8.2-intl php8.2-bcmath php8.2-redis

# Install MySQL
sudo apt install -y mysql-server

# Install Redis
sudo apt install -y redis-server

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt install -y nodejs

# Install Composer
curl -sS https://getcomposer.org/installer | php
sudo mv composer.phar /usr/local/bin/composer
```

## ⚙️ Configuration

### Environment Configuration

1. **Copy production environment file:**
```bash
cp .env.production .env
```

2. **Update environment variables:**
```bash
sudo nano .env
```

Key settings to update:
- `APP_URL=https://www.sonalibd.org`
- `DB_PASSWORD=your_secure_password`
- `MAIL_*` settings for email notifications
- `LOG_SLACK_WEBHOOK_URL` for Slack alerts

### Database Setup

```bash
# Create database and user
sudo mysql
CREATE DATABASE sonali_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'sonali_user'@'localhost' IDENTIFIED BY 'Map13579@';
GRANT ALL PRIVILEGES ON sonali_db.* TO 'sonali_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;

# Run migrations
php artisan migrate --force
```

### Application Setup

```bash
# Install dependencies
composer install --no-dev --optimize-autoloader
npm ci --production
npm run build

# Generate application key
php artisan key:generate

# Cache configurations
php artisan config:cache
php artisan route:cache
php artisan view:cache
```

## 🔒 Security Setup

### Run Security Hardening

```bash
sudo chmod +x deployment/scripts/security-hardening.sh
sudo ./deployment/scripts/security-hardening.sh
```

This script configures:
- UFW firewall with proper rules
- MySQL security settings
- PHP security configurations
- File permissions
- Fail2ban for intrusion prevention
- Automatic security updates

### Manual Security Steps

1. **Change default passwords:**
```bash
# MySQL root password
sudo mysql_secure_installation

# Create strong passwords for all accounts
```

2. **Configure SSH security:**
```bash
# Edit SSH configuration
sudo nano /etc/ssh/sshd_config

# Recommended settings:
# Port 2222 (change from default 22)
# PermitRootLogin no
# PasswordAuthentication no (use SSH keys)
# AllowUsers your_username

sudo systemctl restart ssh
```

## 🔐 SSL Certificate Setup

### Automated SSL Setup

```bash
sudo chmod +x deployment/scripts/ssl-setup.sh
sudo ./deployment/scripts/ssl-setup.sh
```

### Manual SSL Setup

```bash
# Install Certbot
sudo snap install --classic certbot

# Obtain certificate
sudo certbot certonly --webroot -w /var/www/sonali/public -d www.sonalibd.org -d sonalibd.org

# Install Nginx configuration
sudo cp deployment/nginx/sonalibd.org.conf /etc/nginx/sites-available/
sudo ln -s /etc/nginx/sites-available/sonalibd.org.conf /etc/nginx/sites-enabled/
sudo nginx -t && sudo systemctl reload nginx
```

## 💾 Backup & Monitoring

### Backup System

The automated backup system creates:
- **Daily backups**: Kept for 7 days
- **Weekly backups**: Kept for 30 days  
- **Monthly backups**: Kept for 365 days

```bash
# Manual backup
sudo ./deployment/scripts/backup.sh

# View backup status
ls -la /var/backups/sonali/
```

### Monitoring System

```bash
# Run monitoring check
sudo ./deployment/scripts/monitoring.sh

# View monitoring reports
sudo ./deployment/scripts/monitoring.sh --report
```

Monitoring includes:
- System resource usage
- Service health checks
- Website response time
- SSL certificate expiration
- Error log analysis
- Backup verification

## 🔄 Deployment & Updates

### Automated Deployment

```bash
sudo chmod +x deployment/scripts/deploy.sh
sudo ./deployment/scripts/deploy.sh
```

The deployment script:
- Creates automatic backups
- Enables maintenance mode
- Updates code and dependencies
- Runs database migrations
- Clears and rebuilds caches
- Restarts services
- Performs health checks
- Includes rollback capability

### Manual Deployment Steps

```bash
# Enable maintenance mode
php artisan down

# Update code
git pull origin main

# Update dependencies
composer install --no-dev --optimize-autoloader
npm ci --production && npm run build

# Run migrations
php artisan migrate --force

# Clear and cache
php artisan optimize:clear
php artisan optimize

# Restart services
sudo systemctl restart php8.2-fpm
sudo systemctl reload nginx

# Disable maintenance mode
php artisan up
```

## 🛠️ Maintenance

### Regular Maintenance Tasks

1. **Weekly:**
```bash
# Update system packages
sudo apt update && sudo apt upgrade

# Check disk space
df -h

# Review error logs
sudo tail -f /var/log/nginx/sonalibd.org.error.log
sudo tail -f /var/www/sonali/storage/logs/laravel.log
```

2. **Monthly:**
```bash
# Clean old logs
sudo find /var/www/sonali/storage/logs -name "*.log" -mtime +30 -delete

# Optimize database
sudo mysqlcheck -o sonali_db

# Review backup integrity
sudo ./deployment/scripts/backup.sh
```

### Performance Optimization

```bash
# Apply performance configurations
sudo cp deployment/config/performance-optimization.conf /etc/nginx/conf.d/
sudo systemctl reload nginx

# Monitor performance
sudo ./deployment/scripts/monitoring.sh --report
```

## 🔍 Troubleshooting

### Common Issues

1. **Website not accessible:**
```bash
# Check Nginx status
sudo systemctl status nginx

# Check Nginx configuration
sudo nginx -t

# Check SSL certificate
sudo certbot certificates
```

2. **Database connection errors:**
```bash
# Check MySQL status
sudo systemctl status mysql

# Test database connection
mysql -u sonali_user -p sonali_db
```

3. **PHP errors:**
```bash
# Check PHP-FPM status
sudo systemctl status php8.2-fpm

# Check PHP error logs
sudo tail -f /var/log/php8.2-fpm.log
```

4. **Permission issues:**
```bash
# Reset file permissions
sudo chown -R www-data:www-data /var/www/sonali
sudo find /var/www/sonali -type d -exec chmod 755 {} \;
sudo find /var/www/sonali -type f -exec chmod 644 {} \;
sudo chmod -R 775 /var/www/sonali/storage
sudo chmod -R 775 /var/www/sonali/bootstrap/cache
```

### Log Locations

- **Nginx Access:** `/var/log/nginx/sonalibd.org.access.log`
- **Nginx Error:** `/var/log/nginx/sonalibd.org.error.log`
- **PHP-FPM:** `/var/log/php8.2-fpm.log`
- **MySQL:** `/var/log/mysql/error.log`
- **Laravel:** `/var/www/sonali/storage/logs/laravel.log`
- **System:** `/var/log/syslog`

### Emergency Procedures

1. **Rollback deployment:**
```bash
# The deploy script includes automatic rollback on failure
# Manual rollback:
sudo systemctl stop php8.2-fpm
sudo rsync -av /var/backups/sonali/current/ /var/www/sonali/
sudo systemctl start php8.2-fpm
```

2. **Restore from backup:**
```bash
# Extract backup
cd /var/backups/sonali/daily
sudo tar -xzf full_backup_YYYYMMDD_HHMMSS.tar.gz

# Restore files and database
# (Follow backup restoration procedures)
```

## 📞 Support

For technical support and maintenance:
- **Email:** <EMAIL>
- **Documentation:** This README and inline script comments
- **Logs:** Check system logs for detailed error information

## 📝 License

This deployment configuration is part of the Sonali Microfinance project.
