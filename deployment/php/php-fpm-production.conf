# PHP-FPM Production Configuration for Sonali Microfinance
# Pool: www

[www]
user = www-data
group = www-data

# Socket configuration
listen = /var/run/php/php8.2-fpm.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660

# Process management
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.max_requests = 1000

# Process priority
process.priority = -10

# Timeouts
request_terminate_timeout = 60s
request_slowlog_timeout = 30s

# Logging
slowlog = /var/log/php8.2-fpm-slow.log
access.log = /var/log/php8.2-fpm-access.log
access.format = "%R - %u %t \"%m %r%Q%q\" %s %f %{mili}d %{kilo}M %C%%"

# Security
security.limit_extensions = .php

# Environment variables
env[HOSTNAME] = $HOSTNAME
env[PATH] = /usr/local/bin:/usr/bin:/bin
env[TMP] = /tmp
env[TMPDIR] = /tmp
env[TEMP] = /tmp

# PHP Admin values
php_admin_value[sendmail_path] = /usr/sbin/sendmail -t -i -f <EMAIL>
php_admin_value[error_log] = /var/log/php8.2-fpm-error.log
php_admin_flag[log_errors] = on
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 60
php_admin_value[max_input_time] = 60
php_admin_value[post_max_size] = 20M
php_admin_value[upload_max_filesize] = 20M
php_admin_value[max_file_uploads] = 20

# Security settings
php_admin_value[disable_functions] = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source
php_admin_value[expose_php] = Off
php_admin_value[allow_url_fopen] = Off
php_admin_value[allow_url_include] = Off

# Session settings
php_admin_value[session.save_handler] = redis
php_admin_value[session.save_path] = "tcp://127.0.0.1:6379"
php_admin_value[session.gc_maxlifetime] = 7200

# OPcache settings
php_admin_value[opcache.enable] = 1
php_admin_value[opcache.memory_consumption] = 128
php_admin_value[opcache.interned_strings_buffer] = 8
php_admin_value[opcache.max_accelerated_files] = 4000
php_admin_value[opcache.revalidate_freq] = 60
php_admin_value[opcache.fast_shutdown] = 1
php_admin_value[opcache.enable_cli] = 1
