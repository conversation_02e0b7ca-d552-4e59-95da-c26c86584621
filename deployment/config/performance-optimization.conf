# Performance Optimization Configuration for Sonali Microfinance
# This file contains various performance optimization settings

# ============================================================================
# NGINX PERFORMANCE OPTIMIZATION
# ============================================================================

# Worker processes and connections
worker_processes auto;
worker_rlimit_nofile 65535;

events {
    worker_connections 4096;
    use epoll;
    multi_accept on;
}

http {
    # Basic settings
    sendfile on;
    tcp_nopush on;
    tcp_nodelay on;
    keepalive_timeout 65;
    keepalive_requests 1000;
    types_hash_max_size 2048;
    server_tokens off;
    
    # Buffer sizes
    client_body_buffer_size 128k;
    client_max_body_size 20m;
    client_header_buffer_size 1k;
    large_client_header_buffers 4 4k;
    output_buffers 1 32k;
    postpone_output 1460;
    
    # Timeouts
    client_header_timeout 3m;
    client_body_timeout 3m;
    send_timeout 3m;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml
        application/json
        application/ld+json
        application/manifest+json
        application/x-web-app-manifest+json
        font/opentype
        image/x-icon;
    
    # Brotli compression (if module available)
    # brotli on;
    # brotli_comp_level 6;
    # brotli_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;
    
    # Open file cache
    open_file_cache max=200000 inactive=20s;
    open_file_cache_valid 30s;
    open_file_cache_min_uses 2;
    open_file_cache_errors on;
    
    # FastCGI cache
    fastcgi_cache_path /var/cache/nginx/fastcgi levels=1:2 keys_zone=WORDPRESS:100m inactive=60m;
    fastcgi_cache_key "$scheme$request_method$host$request_uri";
    fastcgi_cache_use_stale error timeout invalid_header http_500;
    fastcgi_ignore_headers Cache-Control Expires Set-Cookie;
    
    # Rate limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    limit_req_zone $binary_remote_addr zone=api:10m rate=60r/m;
    limit_req_zone $binary_remote_addr zone=general:10m rate=10r/s;
    
    # Connection limiting
    limit_conn_zone $binary_remote_addr zone=conn_limit_per_ip:10m;
    limit_conn_zone $server_name zone=conn_limit_per_server:10m;
}

# ============================================================================
# PHP-FPM PERFORMANCE OPTIMIZATION
# ============================================================================

# Pool configuration for high performance
[www]
user = www-data
group = www-data

# Socket configuration
listen = /var/run/php/php8.2-fpm.sock
listen.owner = www-data
listen.group = www-data
listen.mode = 0660
listen.backlog = 511

# Process management (optimized for 4GB RAM server)
pm = dynamic
pm.max_children = 50
pm.start_servers = 10
pm.min_spare_servers = 5
pm.max_spare_servers = 20
pm.process_idle_timeout = 10s
pm.max_requests = 1000

# Process priority
process.priority = -10

# Timeouts
request_terminate_timeout = 60s
request_slowlog_timeout = 30s

# Memory and execution limits
php_admin_value[memory_limit] = 256M
php_admin_value[max_execution_time] = 60
php_admin_value[max_input_time] = 60
php_admin_value[post_max_size] = 20M
php_admin_value[upload_max_filesize] = 20M

# OPcache optimization
php_admin_value[opcache.enable] = 1
php_admin_value[opcache.enable_cli] = 1
php_admin_value[opcache.memory_consumption] = 256
php_admin_value[opcache.interned_strings_buffer] = 16
php_admin_value[opcache.max_accelerated_files] = 10000
php_admin_value[opcache.max_wasted_percentage] = 5
php_admin_value[opcache.use_cwd] = 1
php_admin_value[opcache.validate_timestamps] = 0
php_admin_value[opcache.revalidate_freq] = 0
php_admin_value[opcache.save_comments] = 1
php_admin_value[opcache.fast_shutdown] = 1

# Realpath cache
php_admin_value[realpath_cache_size] = 4096K
php_admin_value[realpath_cache_ttl] = 600

# ============================================================================
# MYSQL PERFORMANCE OPTIMIZATION
# ============================================================================

[mysqld]
# InnoDB settings for performance
innodb_buffer_pool_size = 1G
innodb_buffer_pool_instances = 4
innodb_log_file_size = 256M
innodb_log_buffer_size = 16M
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
innodb_open_files = 400
innodb_io_capacity = 400
innodb_read_io_threads = 4
innodb_write_io_threads = 4

# Query cache (MySQL 5.7 and below)
# query_cache_type = 1
# query_cache_size = 128M
# query_cache_limit = 2M

# Connection settings
max_connections = 200
max_user_connections = 180
thread_cache_size = 16
table_open_cache = 4000
table_definition_cache = 1400

# Temporary tables
tmp_table_size = 64M
max_heap_table_size = 64M

# MyISAM settings
key_buffer_size = 32M
myisam_sort_buffer_size = 8M

# Binary logging
log_bin = mysql-bin
binlog_format = ROW
expire_logs_days = 7
max_binlog_size = 100M

# Slow query log
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# ============================================================================
# REDIS PERFORMANCE OPTIMIZATION
# ============================================================================

# Redis configuration for Laravel caching
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistence settings (adjust based on needs)
save 900 1
save 300 10
save 60 10000

# Network settings
tcp-keepalive 300
timeout 0

# Performance settings
tcp-backlog 511
databases 16

# ============================================================================
# SYSTEM LEVEL OPTIMIZATIONS
# ============================================================================

# Kernel parameters for web server performance
# Add to /etc/sysctl.conf

# Network performance
net.core.rmem_default = 262144
net.core.rmem_max = 16777216
net.core.wmem_default = 262144
net.core.wmem_max = 16777216
net.ipv4.tcp_rmem = 4096 65536 16777216
net.ipv4.tcp_wmem = 4096 65536 16777216
net.ipv4.tcp_congestion_control = bbr

# File descriptor limits
fs.file-max = 2097152
fs.nr_open = 1048576

# Virtual memory
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# ============================================================================
# LARAVEL SPECIFIC OPTIMIZATIONS
# ============================================================================

# Environment variables for production
APP_ENV=production
APP_DEBUG=false

# Cache configuration
CACHE_DRIVER=redis
SESSION_DRIVER=redis
QUEUE_CONNECTION=redis

# Database connection pooling
DB_CONNECTION=mysql
DB_PERSISTENT=true

# File system optimizations
FILESYSTEM_DISK=public

# Asset optimization
MIX_ASSET_URL=https://cdn.sonalibd.org

# ============================================================================
# MONITORING AND LOGGING
# ============================================================================

# Log rotation for performance logs
# Add to /etc/logrotate.d/performance-logs

/var/log/nginx/performance.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        /usr/bin/systemctl reload nginx
    endscript
}

/var/log/php8.2-fpm-slow.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/bin/systemctl reload php8.2-fpm
    endscript
}

# ============================================================================
# CRON JOBS FOR MAINTENANCE
# ============================================================================

# Laravel scheduler
* * * * * www-data cd /var/www/sonali && php artisan schedule:run >> /dev/null 2>&1

# Clear expired sessions (daily)
0 2 * * * www-data cd /var/www/sonali && php artisan session:gc

# Optimize application (weekly)
0 3 * * 0 www-data cd /var/www/sonali && php artisan optimize:clear && php artisan optimize

# Clear logs older than 30 days
0 4 * * * root find /var/www/sonali/storage/logs -name "*.log" -mtime +30 -delete

# ============================================================================
# SECURITY HEADERS FOR PERFORMANCE
# ============================================================================

# Add to Nginx server block
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;

# Content Security Policy
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data: https:; connect-src 'self';" always;
