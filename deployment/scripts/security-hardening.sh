#!/bin/bash

# Security Hardening Script for Sonali Microfinance Production Server
# Run as root: sudo bash security-hardening.sh

set -e

echo "🔒 Starting Security Hardening for Sonali Microfinance..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

# 1. Update system packages
print_status "Updating system packages..."
apt update && apt upgrade -y

# 2. Configure UFW Firewall
print_status "Configuring UFW firewall..."
ufw --force reset
ufw default deny incoming
ufw default allow outgoing

# Allow SSH (change port if needed)
ufw allow 22/tcp comment 'SSH'

# Allow HTTP and HTTPS
ufw allow 80/tcp comment 'HTTP'
ufw allow 443/tcp comment 'HTTPS'

# Allow MySQL only from localhost
ufw allow from 127.0.0.1 to any port 3306 comment 'MySQL localhost'

# Allow Redis only from localhost
ufw allow from 127.0.0.1 to any port 6379 comment 'Redis localhost'

# Enable firewall
ufw --force enable

print_status "Firewall configured successfully"

# 3. Secure MySQL installation
print_status "Securing MySQL installation..."

# Create secure MySQL configuration
mysql -e "DELETE FROM mysql.user WHERE User='';"
mysql -e "DELETE FROM mysql.user WHERE User='root' AND Host NOT IN ('localhost', '127.0.0.1', '::1');"
mysql -e "DROP DATABASE IF EXISTS test;"
mysql -e "DELETE FROM mysql.db WHERE Db='test' OR Db='test\\_%';"
mysql -e "FLUSH PRIVILEGES;"

print_status "MySQL secured successfully"

# 4. Configure file permissions for Laravel
print_status "Setting proper file permissions..."

# Set ownership
chown -R www-data:www-data /var/www/sonali
chown -R www-data:www-data /var/www/sonali/storage
chown -R www-data:www-data /var/www/sonali/bootstrap/cache

# Set directory permissions
find /var/www/sonali -type d -exec chmod 755 {} \;

# Set file permissions
find /var/www/sonali -type f -exec chmod 644 {} \;

# Set executable permissions for artisan
chmod +x /var/www/sonali/artisan

# Set storage and cache permissions
chmod -R 775 /var/www/sonali/storage
chmod -R 775 /var/www/sonali/bootstrap/cache

# Secure sensitive files
chmod 600 /var/www/sonali/.env*
chmod 600 /var/www/sonali/composer.json
chmod 600 /var/www/sonali/composer.lock

print_status "File permissions set successfully"

# 5. Configure PHP security settings
print_status "Configuring PHP security settings..."

# Create PHP security configuration
cat > /etc/php/8.2/fpm/conf.d/99-security.ini << 'EOF'
; Security Configuration for Sonali Microfinance

; Disable dangerous functions
disable_functions = exec,passthru,shell_exec,system,proc_open,popen,curl_exec,curl_multi_exec,parse_ini_file,show_source,highlight_file,ini_alter,ini_get_all,ini_restore,get_cfg_var,phpinfo,php_uname

; Hide PHP version
expose_php = Off

; Disable remote file access
allow_url_fopen = Off
allow_url_include = Off

; Session security
session.cookie_httponly = 1
session.cookie_secure = 1
session.use_strict_mode = 1
session.cookie_samesite = "Strict"

; File upload security
file_uploads = On
upload_max_filesize = 20M
max_file_uploads = 20

; Memory and execution limits
memory_limit = 256M
max_execution_time = 60
max_input_time = 60

; Error handling
display_errors = Off
display_startup_errors = Off
log_errors = On
error_log = /var/log/php_errors.log

; SQL injection protection
magic_quotes_gpc = Off
magic_quotes_runtime = Off
magic_quotes_sybase = Off
EOF

print_status "PHP security configured successfully"

# 6. Configure log rotation
print_status "Setting up log rotation..."

cat > /etc/logrotate.d/sonali-microfinance << 'EOF'
/var/www/sonali/storage/logs/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        /usr/bin/systemctl reload php8.2-fpm
    endscript
}

/var/log/nginx/sonalibd.org.*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        /usr/bin/systemctl reload nginx
    endscript
}
EOF

print_status "Log rotation configured successfully"

# 7. Install and configure fail2ban
print_status "Installing and configuring fail2ban..."

apt install -y fail2ban

cat > /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3
backend = systemd

[sshd]
enabled = true
port = ssh
logpath = %(sshd_log)s
maxretry = 3

[nginx-http-auth]
enabled = true
filter = nginx-http-auth
port = http,https
logpath = /var/log/nginx/sonalibd.org.error.log
maxretry = 3

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
port = http,https
logpath = /var/log/nginx/sonalibd.org.error.log
maxretry = 5

[nginx-botsearch]
enabled = true
filter = nginx-botsearch
port = http,https
logpath = /var/log/nginx/sonalibd.org.access.log
maxretry = 2
EOF

systemctl enable fail2ban
systemctl start fail2ban

print_status "Fail2ban configured successfully"

# 8. Configure automatic security updates
print_status "Configuring automatic security updates..."

apt install -y unattended-upgrades

cat > /etc/apt/apt.conf.d/50unattended-upgrades << 'EOF'
Unattended-Upgrade::Allowed-Origins {
    "${distro_id}:${distro_codename}-security";
    "${distro_id}ESMApps:${distro_codename}-apps-security";
    "${distro_id}ESM:${distro_codename}-infra-security";
};

Unattended-Upgrade::Package-Blacklist {
};

Unattended-Upgrade::DevRelease "false";
Unattended-Upgrade::Remove-Unused-Dependencies "true";
Unattended-Upgrade::Automatic-Reboot "false";
Unattended-Upgrade::Automatic-Reboot-Time "02:00";
EOF

cat > /etc/apt/apt.conf.d/20auto-upgrades << 'EOF'
APT::Periodic::Update-Package-Lists "1";
APT::Periodic::Unattended-Upgrade "1";
APT::Periodic::AutocleanInterval "7";
EOF

print_status "Automatic security updates configured successfully"

# 9. Create backup directories with proper permissions
print_status "Creating backup directories..."

mkdir -p /var/backups/sonali/{database,files,logs}
chown -R www-data:www-data /var/backups/sonali
chmod -R 750 /var/backups/sonali

print_status "Backup directories created successfully"

# 10. Restart services
print_status "Restarting services..."

systemctl restart php8.2-fpm
systemctl restart nginx
systemctl restart mysql
systemctl restart fail2ban

print_status "Services restarted successfully"

echo ""
print_status "🎉 Security hardening completed successfully!"
echo ""
print_warning "Please remember to:"
echo "  1. Change default SSH port if needed"
echo "  2. Set up SSH key authentication"
echo "  3. Configure monitoring and alerting"
echo "  4. Test all security measures"
echo "  5. Update .env.production with actual credentials"
echo ""
