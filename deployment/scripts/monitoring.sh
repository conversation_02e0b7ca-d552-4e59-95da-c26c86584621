#!/bin/bash

# System Monitoring Script for Sonali Microfinance
# Monitors system health, performance, and sends alerts

set -e

echo "📊 Starting system monitoring for Sonali Microfinance..."

# Configuration
DOMAIN="www.sonalibd.org"
PROJECT_ROOT="/var/www/sonali"
LOG_FILE="/var/log/system-monitoring.log"
ALERT_EMAIL="<EMAIL>"
SLACK_WEBHOOK=""  # Add your Slack webhook URL here

# Thresholds
CPU_THRESHOLD=80
MEMORY_THRESHOLD=80
DISK_THRESHOLD=85
LOAD_THRESHOLD=5.0
RESPONSE_TIME_THRESHOLD=3000  # milliseconds

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

# Function to log with timestamp
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S'): $1" >> "$LOG_FILE"
}

# Function to send alert
send_alert() {
    local subject="$1"
    local message="$2"
    local severity="$3"
    
    log_message "ALERT [$severity]: $subject - $message"
    
    # Send email alert
    if command -v mail &> /dev/null; then
        echo "$message" | mail -s "[$severity] $subject - Sonali Microfinance" "$ALERT_EMAIL"
    fi
    
    # Send Slack alert (if webhook configured)
    if [ -n "$SLACK_WEBHOOK" ]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚨 [$severity] $subject\\n$message\"}" \
            "$SLACK_WEBHOOK" 2>/dev/null || true
    fi
}

# Function to check service status
check_service() {
    local service="$1"
    
    if systemctl is-active --quiet "$service"; then
        print_status "$service is running"
        return 0
    else
        print_error "$service is not running"
        send_alert "Service Down" "$service is not running on $(hostname)" "CRITICAL"
        return 1
    fi
}

# Function to check disk usage
check_disk_usage() {
    local path="$1"
    local usage=$(df "$path" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [ "$usage" -gt "$DISK_THRESHOLD" ]; then
        print_error "Disk usage for $path: ${usage}% (threshold: ${DISK_THRESHOLD}%)"
        send_alert "High Disk Usage" "Disk usage for $path is ${usage}% on $(hostname)" "WARNING"
        return 1
    else
        print_status "Disk usage for $path: ${usage}%"
        return 0
    fi
}

# Function to check memory usage
check_memory_usage() {
    local usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [ "$usage" -gt "$MEMORY_THRESHOLD" ]; then
        print_error "Memory usage: ${usage}% (threshold: ${MEMORY_THRESHOLD}%)"
        send_alert "High Memory Usage" "Memory usage is ${usage}% on $(hostname)" "WARNING"
        return 1
    else
        print_status "Memory usage: ${usage}%"
        return 0
    fi
}

# Function to check CPU usage
check_cpu_usage() {
    local usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    local usage_int=$(echo "$usage" | cut -d'.' -f1)
    
    if [ "$usage_int" -gt "$CPU_THRESHOLD" ]; then
        print_error "CPU usage: ${usage}% (threshold: ${CPU_THRESHOLD}%)"
        send_alert "High CPU Usage" "CPU usage is ${usage}% on $(hostname)" "WARNING"
        return 1
    else
        print_status "CPU usage: ${usage}%"
        return 0
    fi
}

# Function to check load average
check_load_average() {
    local load=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    
    if (( $(echo "$load > $LOAD_THRESHOLD" | bc -l) )); then
        print_error "Load average: $load (threshold: $LOAD_THRESHOLD)"
        send_alert "High Load Average" "Load average is $load on $(hostname)" "WARNING"
        return 1
    else
        print_status "Load average: $load"
        return 0
    fi
}

# Function to check website response time
check_website_response() {
    local url="https://$DOMAIN"
    local response_time=$(curl -o /dev/null -s -w '%{time_total}' "$url" | awk '{print $1*1000}')
    local response_time_int=$(echo "$response_time" | cut -d'.' -f1)
    
    if [ "$response_time_int" -gt "$RESPONSE_TIME_THRESHOLD" ]; then
        print_error "Website response time: ${response_time}ms (threshold: ${RESPONSE_TIME_THRESHOLD}ms)"
        send_alert "Slow Website Response" "Website response time is ${response_time}ms for $url" "WARNING"
        return 1
    else
        print_status "Website response time: ${response_time}ms"
        return 0
    fi
}

# Function to check SSL certificate
check_ssl_certificate() {
    local domain="$DOMAIN"
    local expiry_date=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)
    local expiry_epoch=$(date -d "$expiry_date" +%s)
    local current_epoch=$(date +%s)
    local days_until_expiry=$(( ($expiry_epoch - $current_epoch) / 86400 ))
    
    if [ "$days_until_expiry" -le 30 ]; then
        print_error "SSL certificate expires in $days_until_expiry days"
        send_alert "SSL Certificate Expiring" "SSL certificate for $domain expires in $days_until_expiry days" "WARNING"
        return 1
    else
        print_status "SSL certificate expires in $days_until_expiry days"
        return 0
    fi
}

# Function to check database connection
check_database_connection() {
    if mysql -u sonali_user -pMap13579@ -e "SELECT 1;" sonali_db >/dev/null 2>&1; then
        print_status "Database connection successful"
        return 0
    else
        print_error "Database connection failed"
        send_alert "Database Connection Failed" "Cannot connect to MySQL database on $(hostname)" "CRITICAL"
        return 1
    fi
}

# Function to check Laravel application
check_laravel_application() {
    cd "$PROJECT_ROOT"
    
    # Check if Laravel is responding
    if sudo -u www-data php artisan tinker --execute="echo 'Laravel OK';" 2>/dev/null | grep -q "Laravel OK"; then
        print_status "Laravel application is responding"
        return 0
    else
        print_error "Laravel application is not responding"
        send_alert "Laravel Application Error" "Laravel application is not responding on $(hostname)" "CRITICAL"
        return 1
    fi
}

# Function to check log files for errors
check_error_logs() {
    local error_count=0
    
    # Check Laravel logs
    if [ -f "$PROJECT_ROOT/storage/logs/laravel.log" ]; then
        local recent_errors=$(tail -n 100 "$PROJECT_ROOT/storage/logs/laravel.log" | grep -c "ERROR\|CRITICAL\|EMERGENCY" || echo 0)
        if [ "$recent_errors" -gt 10 ]; then
            print_error "High number of Laravel errors: $recent_errors in last 100 lines"
            error_count=$((error_count + recent_errors))
        fi
    fi
    
    # Check Nginx error logs
    if [ -f "/var/log/nginx/sonalibd.org.error.log" ]; then
        local nginx_errors=$(tail -n 100 "/var/log/nginx/sonalibd.org.error.log" | grep -c "error\|crit\|alert\|emerg" || echo 0)
        if [ "$nginx_errors" -gt 5 ]; then
            print_error "High number of Nginx errors: $nginx_errors in last 100 lines"
            error_count=$((error_count + nginx_errors))
        fi
    fi
    
    if [ "$error_count" -gt 0 ]; then
        send_alert "High Error Rate" "Found $error_count errors in recent logs on $(hostname)" "WARNING"
        return 1
    else
        print_status "Error logs look normal"
        return 0
    fi
}

# Function to check backup status
check_backup_status() {
    local backup_dir="/var/backups/sonali"
    local today=$(date +%Y%m%d)
    
    # Check if today's backup exists
    if find "$backup_dir" -name "*${today}*" -type f | grep -q .; then
        print_status "Today's backup found"
        return 0
    else
        print_error "Today's backup not found"
        send_alert "Backup Missing" "Today's backup was not found on $(hostname)" "WARNING"
        return 1
    fi
}

# Main monitoring function
run_monitoring() {
    local failed_checks=0
    
    log_message "Starting system monitoring check"
    
    print_step "System Services"
    check_service "nginx" || failed_checks=$((failed_checks + 1))
    check_service "php8.2-fpm" || failed_checks=$((failed_checks + 1))
    check_service "mysql" || failed_checks=$((failed_checks + 1))
    check_service "redis-server" || failed_checks=$((failed_checks + 1))
    
    print_step "System Resources"
    check_cpu_usage || failed_checks=$((failed_checks + 1))
    check_memory_usage || failed_checks=$((failed_checks + 1))
    check_load_average || failed_checks=$((failed_checks + 1))
    
    print_step "Disk Usage"
    check_disk_usage "/" || failed_checks=$((failed_checks + 1))
    check_disk_usage "/var" || failed_checks=$((failed_checks + 1))
    
    print_step "Application Health"
    check_website_response || failed_checks=$((failed_checks + 1))
    check_database_connection || failed_checks=$((failed_checks + 1))
    check_laravel_application || failed_checks=$((failed_checks + 1))
    
    print_step "Security & Maintenance"
    check_ssl_certificate || failed_checks=$((failed_checks + 1))
    check_error_logs || failed_checks=$((failed_checks + 1))
    check_backup_status || failed_checks=$((failed_checks + 1))
    
    log_message "Monitoring check completed - Failed checks: $failed_checks"
    
    if [ "$failed_checks" -eq 0 ]; then
        print_status "All checks passed ✅"
    else
        print_warning "$failed_checks checks failed ⚠️"
    fi
    
    return $failed_checks
}

# Generate monitoring report
generate_report() {
    local report_file="/var/log/monitoring-report-$(date +%Y%m%d).log"
    
    cat > "$report_file" << EOF
Sonali Microfinance System Monitoring Report
==========================================
Date: $(date)
Hostname: $(hostname)
Uptime: $(uptime)

System Information:
- OS: $(lsb_release -d | cut -f2)
- Kernel: $(uname -r)
- Architecture: $(uname -m)

Resource Usage:
- CPU: $(top -bn1 | grep "Cpu(s)" | awk '{print $2}')
- Memory: $(free -h | awk 'NR==2{printf "%s/%s (%.0f%%)", $3,$2,$3*100/$2}')
- Disk (/): $(df -h / | awk 'NR==2{print $3"/"$2" ("$5")"}')
- Load: $(uptime | awk -F'load average:' '{print $2}')

Service Status:
- Nginx: $(systemctl is-active nginx)
- PHP-FPM: $(systemctl is-active php8.2-fpm)
- MySQL: $(systemctl is-active mysql)
- Redis: $(systemctl is-active redis-server)

Network:
- Website Response: $(curl -o /dev/null -s -w '%{time_total}s' https://$DOMAIN)
- SSL Certificate: $(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)

Last Backup: $(find /var/backups/sonali -name "*.tar.gz" -type f -printf '%T@ %p\n' | sort -n | tail -1 | awk '{print $2}' | xargs ls -lh 2>/dev/null || echo "No backups found")

Generated: $(date)
EOF

    print_status "Monitoring report generated: $report_file"
}

# Main execution
if [ "$1" = "--report" ]; then
    generate_report
elif [ "$1" = "--quiet" ]; then
    run_monitoring >/dev/null 2>&1
else
    run_monitoring
    generate_report
fi

echo ""
print_status "📊 System monitoring completed"
