#!/bin/bash

# Automated Deployment Script for Sonali Microfinance
# Handles zero-downtime deployment with rollback capability

set -e

echo "🚀 Starting deployment for Sonali Microfinance..."

# Configuration
PROJECT_NAME="sonali"
DOMAIN="www.sonalibd.org"
PROJECT_ROOT="/var/www/sonali"
BACKUP_DIR="/var/backups/sonali"
DEPLOY_USER="www-data"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
BACKUP_NAME="backup_${TIMESTAMP}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Function to handle errors
handle_error() {
    print_error "Deployment failed at step: $1"
    print_warning "Rolling back to previous version..."
    rollback
    exit 1
}

# Function to rollback
rollback() {
    if [ -d "${BACKUP_DIR}/current" ]; then
        print_status "Restoring from backup..."
        
        # Stop services
        systemctl stop php8.2-fpm
        
        # Restore files
        rsync -av --delete "${BACKUP_DIR}/current/" "${PROJECT_ROOT}/"
        
        # Restore database
        if [ -f "${BACKUP_DIR}/database_${TIMESTAMP}.sql" ]; then
            mysql sonali_db < "${BACKUP_DIR}/database_${TIMESTAMP}.sql"
        fi
        
        # Restart services
        systemctl start php8.2-fpm
        systemctl reload nginx
        
        print_status "Rollback completed"
    else
        print_error "No backup found for rollback"
    fi
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

# Trap errors
trap 'handle_error "Unknown error"' ERR

# Step 1: Pre-deployment checks
print_step "1. Running pre-deployment checks..."

# Check if project directory exists
if [ ! -d "$PROJECT_ROOT" ]; then
    print_error "Project directory $PROJECT_ROOT does not exist"
    exit 1
fi

# Check if services are running
if ! systemctl is-active --quiet nginx; then
    print_error "Nginx is not running"
    exit 1
fi

if ! systemctl is-active --quiet php8.2-fpm; then
    print_error "PHP-FPM is not running"
    exit 1
fi

if ! systemctl is-active --quiet mysql; then
    print_error "MySQL is not running"
    exit 1
fi

print_status "Pre-deployment checks passed"

# Step 2: Create backup
print_step "2. Creating backup..."

mkdir -p "${BACKUP_DIR}"

# Backup current application
print_status "Backing up application files..."
rsync -av --delete "${PROJECT_ROOT}/" "${BACKUP_DIR}/current/"

# Backup database
print_status "Backing up database..."
mysqldump --single-transaction --routines --triggers sonali_db > "${BACKUP_DIR}/database_${TIMESTAMP}.sql"

print_status "Backup created successfully"

# Step 3: Put application in maintenance mode
print_step "3. Enabling maintenance mode..."

cd "$PROJECT_ROOT"
sudo -u $DEPLOY_USER php artisan down --message="System maintenance in progress. Please try again in a few minutes." --retry=60

print_status "Maintenance mode enabled"

# Step 4: Update application code
print_step "4. Updating application code..."

# Pull latest changes (if using Git)
if [ -d ".git" ]; then
    print_status "Pulling latest changes from Git..."
    sudo -u $DEPLOY_USER git pull origin main
else
    print_warning "Not a Git repository - skipping Git pull"
fi

print_status "Application code updated"

# Step 5: Install/Update dependencies
print_step "5. Installing/updating dependencies..."

# Update Composer dependencies
print_status "Updating Composer dependencies..."
sudo -u $DEPLOY_USER composer install --no-dev --optimize-autoloader --no-interaction

# Update NPM dependencies and build assets
print_status "Building frontend assets..."
sudo -u $DEPLOY_USER npm ci --production
sudo -u $DEPLOY_USER npm run build

print_status "Dependencies updated successfully"

# Step 6: Run database migrations
print_step "6. Running database migrations..."

sudo -u $DEPLOY_USER php artisan migrate --force

print_status "Database migrations completed"

# Step 7: Clear and optimize caches
print_step "7. Optimizing application..."

# Clear all caches
sudo -u $DEPLOY_USER php artisan cache:clear
sudo -u $DEPLOY_USER php artisan config:clear
sudo -u $DEPLOY_USER php artisan route:clear
sudo -u $DEPLOY_USER php artisan view:clear

# Optimize for production
sudo -u $DEPLOY_USER php artisan config:cache
sudo -u $DEPLOY_USER php artisan route:cache
sudo -u $DEPLOY_USER php artisan view:cache

# Optimize Composer autoloader
sudo -u $DEPLOY_USER composer dump-autoload --optimize

print_status "Application optimized"

# Step 8: Update file permissions
print_step "8. Setting file permissions..."

# Set ownership
chown -R $DEPLOY_USER:$DEPLOY_USER "$PROJECT_ROOT"

# Set directory permissions
find "$PROJECT_ROOT" -type d -exec chmod 755 {} \;

# Set file permissions
find "$PROJECT_ROOT" -type f -exec chmod 644 {} \;

# Set executable permissions
chmod +x "$PROJECT_ROOT/artisan"

# Set storage and cache permissions
chmod -R 775 "$PROJECT_ROOT/storage"
chmod -R 775 "$PROJECT_ROOT/bootstrap/cache"

# Secure sensitive files
chmod 600 "$PROJECT_ROOT/.env"*

print_status "File permissions updated"

# Step 9: Restart services
print_step "9. Restarting services..."

# Restart PHP-FPM
systemctl restart php8.2-fpm

# Restart queue workers (if using)
if systemctl is-active --quiet laravel-worker; then
    systemctl restart laravel-worker
fi

# Reload Nginx
systemctl reload nginx

print_status "Services restarted"

# Step 10: Run health checks
print_step "10. Running health checks..."

# Wait for services to start
sleep 5

# Check if application is responding
if curl -s -f -L "https://$DOMAIN/up" > /dev/null; then
    print_status "Health check passed"
else
    handle_error "Health check failed"
fi

# Step 11: Disable maintenance mode
print_step "11. Disabling maintenance mode..."

sudo -u $DEPLOY_USER php artisan up

print_status "Maintenance mode disabled"

# Step 12: Final verification
print_step "12. Final verification..."

# Test main pages
if curl -s -f -L "https://$DOMAIN" > /dev/null; then
    print_status "Main page accessible"
else
    handle_error "Main page not accessible"
fi

# Check database connection
if sudo -u $DEPLOY_USER php artisan tinker --execute="DB::connection()->getPdo(); echo 'Database connected';" | grep -q "Database connected"; then
    print_status "Database connection verified"
else
    handle_error "Database connection failed"
fi

print_status "Final verification completed"

# Step 13: Cleanup old backups
print_step "13. Cleaning up old backups..."

# Keep only last 5 database backups
cd "$BACKUP_DIR"
ls -t database_*.sql | tail -n +6 | xargs -r rm

print_status "Old backups cleaned up"

# Step 14: Send deployment notification
print_step "14. Sending deployment notification..."

# Log deployment
echo "$(date): Deployment completed successfully - Version: $TIMESTAMP" >> /var/log/deployments.log

# Send notification (if mail is configured)
if command -v mail &> /dev/null; then
    echo "Deployment completed successfully at $(date)" | mail -s "Deployment Success - Sonali Microfinance" <EMAIL>
fi

print_status "Deployment notification sent"

echo ""
print_status "🎉 Deployment completed successfully!"
echo ""
print_status "Deployment Summary:"
echo "  Timestamp: $TIMESTAMP"
echo "  Domain: https://$DOMAIN"
echo "  Backup: $BACKUP_NAME"
echo "  Status: ✅ Success"
echo ""
print_warning "Post-deployment checklist:"
echo "  1. Verify all functionality works correctly"
echo "  2. Check error logs for any issues"
echo "  3. Monitor application performance"
echo "  4. Test critical user workflows"
echo ""
