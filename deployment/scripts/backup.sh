#!/bin/bash

# Automated Backup Script for Sonali Microfinance
# Creates comprehensive backups of database, files, and configurations

set -e

echo "💾 Starting backup for Sonali Microfinance..."

# Configuration
PROJECT_NAME="sonali"
PROJECT_ROOT="/var/www/sonali"
BACKUP_ROOT="/var/backups/sonali"
DB_NAME="sonali_db"
DB_USER="sonali_user"
DB_PASS="Map13579@"
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
DATE=$(date +"%Y-%m-%d")

# Retention settings (days)
DAILY_RETENTION=7
WEEKLY_RETENTION=30
MONTHLY_RETENTION=365

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# Function to calculate size
get_size() {
    du -sh "$1" 2>/dev/null | cut -f1 || echo "Unknown"
}

# Function to compress and encrypt backup
compress_encrypt() {
    local source="$1"
    local target="$2"
    
    print_status "Compressing and encrypting: $(basename "$source")"
    
    # Create compressed archive
    tar -czf "${target}.tar.gz" -C "$(dirname "$source")" "$(basename "$source")"
    
    # Optional: Encrypt with GPG (uncomment if needed)
    # gpg --cipher-algo AES256 --compress-algo 1 --s2k-mode 3 --s2k-digest-algo SHA512 --s2k-count 65536 --symmetric --output "${target}.tar.gz.gpg" "${target}.tar.gz"
    # rm "${target}.tar.gz"
    
    print_status "Compressed: $(get_size "${target}.tar.gz")"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

# Create backup directories
print_step "1. Preparing backup directories..."

mkdir -p "${BACKUP_ROOT}/daily"
mkdir -p "${BACKUP_ROOT}/weekly"
mkdir -p "${BACKUP_ROOT}/monthly"
mkdir -p "${BACKUP_ROOT}/temp"

print_status "Backup directories prepared"

# Step 2: Database backup
print_step "2. Creating database backup..."

DB_BACKUP_FILE="${BACKUP_ROOT}/temp/database_${TIMESTAMP}.sql"

# Create database dump with all necessary options
mysqldump \
    --single-transaction \
    --routines \
    --triggers \
    --events \
    --hex-blob \
    --opt \
    --user="$DB_USER" \
    --password="$DB_PASS" \
    "$DB_NAME" > "$DB_BACKUP_FILE"

# Verify backup
if [ -s "$DB_BACKUP_FILE" ]; then
    print_status "Database backup created: $(get_size "$DB_BACKUP_FILE")"
else
    print_error "Database backup failed or is empty"
    exit 1
fi

# Step 3: Application files backup
print_step "3. Creating application files backup..."

APP_BACKUP_DIR="${BACKUP_ROOT}/temp/application_${TIMESTAMP}"
mkdir -p "$APP_BACKUP_DIR"

# Copy application files (excluding unnecessary directories)
rsync -av \
    --exclude='node_modules' \
    --exclude='vendor' \
    --exclude='storage/logs/*' \
    --exclude='storage/framework/cache/*' \
    --exclude='storage/framework/sessions/*' \
    --exclude='storage/framework/views/*' \
    --exclude='.git' \
    --exclude='tests' \
    "$PROJECT_ROOT/" "$APP_BACKUP_DIR/"

print_status "Application files backup created: $(get_size "$APP_BACKUP_DIR")"

# Step 4: Storage files backup
print_step "4. Creating storage files backup..."

STORAGE_BACKUP_DIR="${BACKUP_ROOT}/temp/storage_${TIMESTAMP}"
mkdir -p "$STORAGE_BACKUP_DIR"

# Copy storage files (uploaded files, documents, etc.)
if [ -d "${PROJECT_ROOT}/storage/app" ]; then
    rsync -av "${PROJECT_ROOT}/storage/app/" "$STORAGE_BACKUP_DIR/"
    print_status "Storage files backup created: $(get_size "$STORAGE_BACKUP_DIR")"
else
    print_warning "Storage directory not found"
fi

# Step 5: Configuration files backup
print_step "5. Creating configuration backup..."

CONFIG_BACKUP_DIR="${BACKUP_ROOT}/temp/config_${TIMESTAMP}"
mkdir -p "$CONFIG_BACKUP_DIR"

# Backup important configuration files
cp -r /etc/nginx/sites-available "$CONFIG_BACKUP_DIR/nginx-sites" 2>/dev/null || true
cp -r /etc/php/8.2/fpm "$CONFIG_BACKUP_DIR/php-fpm" 2>/dev/null || true
cp /etc/mysql/mysql.conf.d/*.cnf "$CONFIG_BACKUP_DIR/" 2>/dev/null || true
cp "${PROJECT_ROOT}/.env" "$CONFIG_BACKUP_DIR/env-production" 2>/dev/null || true

print_status "Configuration backup created: $(get_size "$CONFIG_BACKUP_DIR")"

# Step 6: Create compressed archives
print_step "6. Creating compressed archives..."

# Determine backup type based on day
DAY_OF_WEEK=$(date +%u)  # 1=Monday, 7=Sunday
DAY_OF_MONTH=$(date +%d)

if [ "$DAY_OF_MONTH" = "01" ]; then
    BACKUP_TYPE="monthly"
    BACKUP_DIR="${BACKUP_ROOT}/monthly"
elif [ "$DAY_OF_WEEK" = "7" ]; then
    BACKUP_TYPE="weekly"
    BACKUP_DIR="${BACKUP_ROOT}/weekly"
else
    BACKUP_TYPE="daily"
    BACKUP_DIR="${BACKUP_ROOT}/daily"
fi

print_status "Creating $BACKUP_TYPE backup..."

# Compress individual components
compress_encrypt "$DB_BACKUP_FILE" "${BACKUP_DIR}/database_${TIMESTAMP}"
compress_encrypt "$APP_BACKUP_DIR" "${BACKUP_DIR}/application_${TIMESTAMP}"
compress_encrypt "$STORAGE_BACKUP_DIR" "${BACKUP_DIR}/storage_${TIMESTAMP}"
compress_encrypt "$CONFIG_BACKUP_DIR" "${BACKUP_DIR}/config_${TIMESTAMP}"

# Create full backup archive
FULL_BACKUP_DIR="${BACKUP_ROOT}/temp/full_backup_${TIMESTAMP}"
mkdir -p "$FULL_BACKUP_DIR"

cp "${DB_BACKUP_FILE}" "$FULL_BACKUP_DIR/"
cp -r "$APP_BACKUP_DIR" "$FULL_BACKUP_DIR/"
cp -r "$STORAGE_BACKUP_DIR" "$FULL_BACKUP_DIR/"
cp -r "$CONFIG_BACKUP_DIR" "$FULL_BACKUP_DIR/"

compress_encrypt "$FULL_BACKUP_DIR" "${BACKUP_DIR}/full_backup_${TIMESTAMP}"

print_status "Compressed archives created"

# Step 7: Cleanup temporary files
print_step "7. Cleaning up temporary files..."

rm -rf "${BACKUP_ROOT}/temp"

print_status "Temporary files cleaned up"

# Step 8: Cleanup old backups
print_step "8. Cleaning up old backups..."

# Clean daily backups
find "${BACKUP_ROOT}/daily" -name "*.tar.gz" -mtime +$DAILY_RETENTION -delete 2>/dev/null || true

# Clean weekly backups
find "${BACKUP_ROOT}/weekly" -name "*.tar.gz" -mtime +$WEEKLY_RETENTION -delete 2>/dev/null || true

# Clean monthly backups
find "${BACKUP_ROOT}/monthly" -name "*.tar.gz" -mtime +$MONTHLY_RETENTION -delete 2>/dev/null || true

print_status "Old backups cleaned up"

# Step 9: Verify backup integrity
print_step "9. Verifying backup integrity..."

BACKUP_COUNT=0
TOTAL_SIZE=0

for backup_file in "${BACKUP_DIR}"/*_${TIMESTAMP}.tar.gz; do
    if [ -f "$backup_file" ]; then
        # Test archive integrity
        if tar -tzf "$backup_file" >/dev/null 2>&1; then
            BACKUP_COUNT=$((BACKUP_COUNT + 1))
            SIZE=$(stat -f%z "$backup_file" 2>/dev/null || stat -c%s "$backup_file" 2>/dev/null || echo 0)
            TOTAL_SIZE=$((TOTAL_SIZE + SIZE))
        else
            print_error "Backup file corrupted: $(basename "$backup_file")"
        fi
    fi
done

print_status "Backup integrity verified: $BACKUP_COUNT files"

# Step 10: Generate backup report
print_step "10. Generating backup report..."

REPORT_FILE="${BACKUP_ROOT}/backup_report_${DATE}.log"

cat > "$REPORT_FILE" << EOF
Sonali Microfinance Backup Report
================================
Date: $(date)
Backup Type: $BACKUP_TYPE
Timestamp: $TIMESTAMP

Backup Summary:
- Database: ✅ $([ -f "${BACKUP_DIR}/database_${TIMESTAMP}.tar.gz" ] && echo "Success" || echo "Failed")
- Application: ✅ $([ -f "${BACKUP_DIR}/application_${TIMESTAMP}.tar.gz" ] && echo "Success" || echo "Failed")
- Storage: ✅ $([ -f "${BACKUP_DIR}/storage_${TIMESTAMP}.tar.gz" ] && echo "Success" || echo "Failed")
- Configuration: ✅ $([ -f "${BACKUP_DIR}/config_${TIMESTAMP}.tar.gz" ] && echo "Success" || echo "Failed")
- Full Backup: ✅ $([ -f "${BACKUP_DIR}/full_backup_${TIMESTAMP}.tar.gz" ] && echo "Success" || echo "Failed")

Statistics:
- Total Files: $BACKUP_COUNT
- Total Size: $(echo "$TOTAL_SIZE" | awk '{printf "%.2f MB", $1/1024/1024}')
- Backup Location: $BACKUP_DIR

Retention Policy:
- Daily: $DAILY_RETENTION days
- Weekly: $WEEKLY_RETENTION days
- Monthly: $MONTHLY_RETENTION days

Next Backup: $(date -d "tomorrow" +"%Y-%m-%d %H:%M")
EOF

print_status "Backup report generated: $REPORT_FILE"

# Step 11: Send notification
print_step "11. Sending backup notification..."

# Log backup completion
echo "$(date): $BACKUP_TYPE backup completed successfully - Files: $BACKUP_COUNT, Size: $(echo "$TOTAL_SIZE" | awk '{printf "%.2f MB", $1/1024/1024}')" >> /var/log/backups.log

# Send email notification (if mail is configured)
if command -v mail &> /dev/null; then
    mail -s "Backup Completed - Sonali Microfinance ($BACKUP_TYPE)" <EMAIL> < "$REPORT_FILE"
fi

print_status "Backup notification sent"

echo ""
print_status "🎉 Backup completed successfully!"
echo ""
print_status "Backup Summary:"
echo "  Type: $BACKUP_TYPE"
echo "  Timestamp: $TIMESTAMP"
echo "  Files: $BACKUP_COUNT"
echo "  Total Size: $(echo "$TOTAL_SIZE" | awk '{printf "%.2f MB", $1/1024/1024}')"
echo "  Location: $BACKUP_DIR"
echo ""
print_warning "Backup files:"
ls -lh "${BACKUP_DIR}"/*_${TIMESTAMP}.tar.gz 2>/dev/null || echo "  No backup files found"
echo ""
