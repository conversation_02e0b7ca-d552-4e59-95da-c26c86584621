#!/bin/bash

# SSL Certificate Setup Script for Sonali Microfinance
# Sets up Let's Encrypt SSL certificates for www.sonalibd.org

set -e

echo "🔐 Setting up SSL certificates for Sonali Microfinance..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -ne 0 ]]; then
   print_error "This script must be run as root"
   exit 1
fi

# Domain configuration
DOMAIN="www.sonalibd.org"
ALT_DOMAIN="sonalibd.org"
EMAIL="<EMAIL>"
WEBROOT="/var/www/sonali/public"

# 1. Install Certbot
print_status "Installing Certbot..."

apt update
apt install -y snapd
snap install core; snap refresh core
snap install --classic certbot

# Create symlink
ln -sf /snap/bin/certbot /usr/bin/certbot

print_status "Certbot installed successfully"

# 2. Create temporary Nginx configuration for domain verification
print_status "Creating temporary Nginx configuration..."

cat > /etc/nginx/sites-available/temp-sonalibd.org << EOF
server {
    listen 80;
    listen [::]:80;
    server_name $DOMAIN $ALT_DOMAIN;
    
    root $WEBROOT;
    index index.php index.html index.htm;
    
    location /.well-known/acme-challenge/ {
        root $WEBROOT;
        allow all;
    }
    
    location / {
        return 301 https://\$server_name\$request_uri;
    }
}
EOF

# Enable temporary site
ln -sf /etc/nginx/sites-available/temp-sonalibd.org /etc/nginx/sites-enabled/
rm -f /etc/nginx/sites-enabled/default

# Test and reload Nginx
nginx -t && systemctl reload nginx

print_status "Temporary Nginx configuration created"

# 3. Obtain SSL certificate
print_status "Obtaining SSL certificate from Let's Encrypt..."

# Create webroot directory if it doesn't exist
mkdir -p $WEBROOT/.well-known/acme-challenge

# Set proper permissions
chown -R www-data:www-data $WEBROOT/.well-known

# Obtain certificate
certbot certonly \
    --webroot \
    --webroot-path=$WEBROOT \
    --email $EMAIL \
    --agree-tos \
    --no-eff-email \
    --domains $DOMAIN,$ALT_DOMAIN

if [ $? -eq 0 ]; then
    print_status "SSL certificate obtained successfully"
else
    print_error "Failed to obtain SSL certificate"
    exit 1
fi

# 4. Install production Nginx configuration
print_status "Installing production Nginx configuration..."

# Remove temporary configuration
rm -f /etc/nginx/sites-enabled/temp-sonalibd.org

# Copy production configuration
cp /var/www/sonali/deployment/nginx/sonalibd.org.conf /etc/nginx/sites-available/
ln -sf /etc/nginx/sites-available/sonalibd.org.conf /etc/nginx/sites-enabled/

# Test configuration
nginx -t

if [ $? -eq 0 ]; then
    systemctl reload nginx
    print_status "Production Nginx configuration installed successfully"
else
    print_error "Nginx configuration test failed"
    exit 1
fi

# 5. Set up automatic certificate renewal
print_status "Setting up automatic certificate renewal..."

# Create renewal script
cat > /usr/local/bin/certbot-renew.sh << 'EOF'
#!/bin/bash

# Certbot renewal script for Sonali Microfinance
# This script is run by cron to automatically renew SSL certificates

LOG_FILE="/var/log/certbot-renewal.log"

echo "$(date): Starting certificate renewal check" >> $LOG_FILE

# Attempt renewal
/usr/bin/certbot renew --quiet --no-self-upgrade >> $LOG_FILE 2>&1

# Check if renewal was successful
if [ $? -eq 0 ]; then
    echo "$(date): Certificate renewal check completed successfully" >> $LOG_FILE
    
    # Reload Nginx to use new certificates
    /usr/bin/systemctl reload nginx >> $LOG_FILE 2>&1
    
    if [ $? -eq 0 ]; then
        echo "$(date): Nginx reloaded successfully" >> $LOG_FILE
    else
        echo "$(date): Failed to reload Nginx" >> $LOG_FILE
    fi
else
    echo "$(date): Certificate renewal failed" >> $LOG_FILE
fi

echo "$(date): Certificate renewal process completed" >> $LOG_FILE
echo "----------------------------------------" >> $LOG_FILE
EOF

# Make script executable
chmod +x /usr/local/bin/certbot-renew.sh

# Add cron job for automatic renewal (runs twice daily)
cat > /etc/cron.d/certbot-renewal << 'EOF'
# Automatic SSL certificate renewal for Sonali Microfinance
# Runs twice daily at random times to avoid load spikes

SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

# Run renewal check twice daily
0 */12 * * * root /usr/local/bin/certbot-renew.sh
EOF

print_status "Automatic certificate renewal configured"

# 6. Create SSL monitoring script
print_status "Creating SSL monitoring script..."

cat > /usr/local/bin/ssl-monitor.sh << 'EOF'
#!/bin/bash

# SSL Certificate Monitoring Script for Sonali Microfinance
# Checks certificate expiration and sends alerts

DOMAIN="www.sonalibd.org"
ALERT_DAYS=30
LOG_FILE="/var/log/ssl-monitor.log"
ALERT_EMAIL="<EMAIL>"

# Get certificate expiration date
EXPIRY_DATE=$(echo | openssl s_client -servername $DOMAIN -connect $DOMAIN:443 2>/dev/null | openssl x509 -noout -dates | grep notAfter | cut -d= -f2)

# Convert to epoch time
EXPIRY_EPOCH=$(date -d "$EXPIRY_DATE" +%s)
CURRENT_EPOCH=$(date +%s)

# Calculate days until expiration
DAYS_UNTIL_EXPIRY=$(( ($EXPIRY_EPOCH - $CURRENT_EPOCH) / 86400 ))

echo "$(date): SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days" >> $LOG_FILE

# Send alert if certificate expires soon
if [ $DAYS_UNTIL_EXPIRY -le $ALERT_DAYS ]; then
    echo "$(date): SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days - ALERT!" >> $LOG_FILE
    
    # Send email alert (requires mail command to be configured)
    if command -v mail &> /dev/null; then
        echo "SSL certificate for $DOMAIN expires in $DAYS_UNTIL_EXPIRY days. Please renew immediately." | mail -s "SSL Certificate Expiration Alert - $DOMAIN" $ALERT_EMAIL
    fi
fi
EOF

chmod +x /usr/local/bin/ssl-monitor.sh

# Add cron job for SSL monitoring (runs daily)
cat > /etc/cron.d/ssl-monitoring << 'EOF'
# SSL certificate monitoring for Sonali Microfinance
# Runs daily to check certificate expiration

SHELL=/bin/bash
PATH=/usr/local/sbin:/usr/local/bin:/sbin:/bin:/usr/sbin:/usr/bin

# Check SSL certificate daily at 6 AM
0 6 * * * root /usr/local/bin/ssl-monitor.sh
EOF

print_status "SSL monitoring configured"

# 7. Test SSL configuration
print_status "Testing SSL configuration..."

# Wait a moment for Nginx to fully reload
sleep 5

# Test HTTPS connection
if curl -s -I https://$DOMAIN | grep -q "HTTP/2 200"; then
    print_status "HTTPS connection test successful"
else
    print_warning "HTTPS connection test failed - please check manually"
fi

# 8. Display certificate information
print_status "Certificate information:"
certbot certificates

echo ""
print_status "🎉 SSL setup completed successfully!"
echo ""
print_status "Certificate details:"
echo "  Domain: $DOMAIN, $ALT_DOMAIN"
echo "  Certificate path: /etc/letsencrypt/live/$DOMAIN/"
echo "  Renewal: Automatic (twice daily)"
echo "  Monitoring: Daily checks at 6 AM"
echo ""
print_warning "Please verify:"
echo "  1. Visit https://$DOMAIN to test SSL"
echo "  2. Check SSL rating at https://www.ssllabs.com/ssltest/"
echo "  3. Verify automatic renewal: certbot renew --dry-run"
echo ""
