# Sonali Microfinance Laravel Eloquent Models Documentation

## Overview
This document provides comprehensive documentation for all Laravel Eloquent models created for the Sonali Microfinance System. Each model includes proper relationships, business logic methods, scopes, accessors, and validation rules.

## Models Summary

### 1. User Model (`app/Models/User.php`)
**Purpose**: Extended Laravel User model with role-based access control for microfinance system users.

**Key Features**:
- **Roles**: admin, manager, field_officer, member
- **Role-based methods**: `isAdmin()`, `isManager()`, `isFieldOfficer()`, `isMember()`
- **Relationships**: branch, member, managedBranches, createdMembers, reviewedLoanApplications, collectedInstallments, branchTransactions, createdSavingAccounts
- **Scopes**: admins, managers, fieldOfficers, members, active, inactive
- **Business Logic**: `canManageBranch()`, `canAccessBranch()`, `getDisplayRoleAttribute()`

**Fillable Fields**: name, email, password, role, member_id, branch_id, is_active

### 2. Branch Model (`app/Models/Branch.php`)
**Purpose**: Manage microfinance branch locations and operations.

**Key Features**:
- **Relationships**: manager, users, members, loanApplications, loans, savingAccounts, branchTransactions
- **Business Logic**: 
  - `getTotalActiveLoans()`: Count of active loans
  - `getTotalCollections()`: Total collected amount
  - `getFieldOfficers()`: Get field officers in branch
  - `getMonthlyLoanDisbursement()`: Monthly loan disbursement amount
  - `getMonthlyCollection()`: Monthly collection amount
  - `getOverdueLoansCount()`: Count of overdue loans
  - `getTotalSavingsBalance()`: Total savings balance
- **Scopes**: withManager, active

**Fillable Fields**: name, address, manager_id

### 3. Member Model (`app/Models/Member.php`)
**Purpose**: Customer/member profiles and personal information management.

**Key Features**:
- **Relationships**: branch, createdBy, reference, referencedMembers, user, loanApplications, loans, savingAccounts
- **Business Logic**:
  - `getTotalLoans()`: Count of member's loans
  - `getCurrentLoanStatus()`: Current loan status (No Active Loan, Overdue, Current)
  - `getSavingsBalance()`: Total savings balance
  - `getTotalLoanAmount()`: Total loan amount
  - `getTotalPaidAmount()`: Total paid amount
  - `getTotalOutstandingAmount()`: Total outstanding amount
  - `getNextLoanCycleNumber()`: Next loan cycle number
  - `canApplyForLoan()`: Check if member can apply for new loan
- **Scopes**: byBranch, withActiveLoans, withOverdueLoans
- **Accessors**: age, formattedPhone

**Fillable Fields**: member_id, name, father_or_husband_name, mother_name, present_address, permanent_address, nid_number, date_of_birth, religion, phone_number, blood_group, photo, occupation, reference_id, branch_id, created_by

### 4. LoanApplication Model (`app/Models/LoanApplication.php`)
**Purpose**: Handle loan application processing and approval workflow.

**Key Features**:
- **Relationships**: member, reviewedBy, loan
- **Business Logic**:
  - `approve()`: Approve application and create loan
  - `reject()`: Reject application with reason
  - `calculateRepaymentAmount()`: Calculate monthly repayment
  - `calculateTotalRepaymentAmount()`: Calculate total repayment
  - `calculateTotalInterest()`: Calculate total interest
- **Scopes**: pending, approved, rejected, byMember, byBranch, recentApplications
- **Accessors**: statusBadge, formattedAppliedAmount, formattedAdvancePayment, daysFromApplication

**Fillable Fields**: member_id, applied_amount, reason, loan_cycle_number, recommender, status, advance_payment, reviewed_by, reviewed_at, applied_at

### 5. Loan Model (`app/Models/Loan.php`)
**Purpose**: Manage approved loans with repayment terms and installment tracking.

**Key Features**:
- **Relationships**: loanApplication, member, installments
- **Business Logic**:
  - `calculateRemainingAmount()`: Calculate remaining loan amount
  - `getOverdueInstallments()`: Get overdue installments
  - `getNextInstallmentDate()`: Get next installment date
  - `getTotalPaidAmount()`: Total paid amount
  - `getTotalPendingAmount()`: Total pending amount
  - `getCompletionPercentage()`: Loan completion percentage
  - `isFullyPaid()`: Check if loan is fully paid
  - `isOverdue()`: Check if loan is overdue
  - `getDaysOverdue()`: Days overdue count
  - `generateInstallments()`: Generate installment schedule
  - `calculateTotalRepaymentAmount()`: Calculate total repayment
- **Scopes**: active, completed, overdue, byMember, byBranch, byRepaymentMethod
- **Accessors**: status, formattedLoanAmount, formattedRemainingAmount, formattedAdvancePayment, repaymentMethodDisplay

**Fillable Fields**: loan_application_id, loan_amount, interest_rate, loan_duration_months, repayment_method, loan_date, advance_payment, first_installment_date, last_installment_date

### 6. Installment Model (`app/Models/Installment.php`)
**Purpose**: Track individual loan installment payments and collections.

**Key Features**:
- **Relationships**: loan, collectedBy
- **Business Logic**:
  - `markAsPaid()`: Mark installment as paid
  - `calculateDueAmount()`: Calculate due amount including late fees
  - `calculateLateFee()`: Calculate late fee
  - `isOverdue()`: Check if installment is overdue
  - `getDaysOverdue()`: Days overdue count
  - `canBeCollected()`: Check if can be collected
  - `getGracePeriodEndDate()`: Get grace period end date
  - `isInGracePeriod()`: Check if in grace period
- **Scopes**: pending, paid, overdue, dueToday, dueThisWeek, dueThisMonth, byLoan, byCollector, collectedBetween
- **Accessors**: statusBadge, formattedAmount, formattedDueAmount, formattedLateFee, installmentTitle, daysUntilDue

**Fillable Fields**: loan_id, installment_no, amount, installment_date, status, collection_date, collected_by, late_fee, notes

### 7. BranchTransaction Model (`app/Models/BranchTransaction.php`)
**Purpose**: Manage branch-level financial transactions (income/expense tracking).

**Key Features**:
- **Relationships**: branch, enteredBy
- **Business Logic**:
  - `getMonthlyTotal()`: Get monthly total for specific type
  - `isIncome()`: Check if transaction is income
  - `isExpense()`: Check if transaction is expense
- **Static Methods**:
  - `getTotalIncomeForBranch()`: Get total income for branch
  - `getTotalExpenseForBranch()`: Get total expense for branch
  - `getNetIncomeForBranch()`: Get net income for branch
  - `getCategoryTotalsForBranch()`: Get category-wise totals
- **Scopes**: income, expense, byBranch, byCategory, byDateRange, thisMonth, thisYear, today
- **Accessors**: transactionTypeBadge, formattedAmount, formattedAmountPlain, categoryDisplay

**Fillable Fields**: branch_id, transaction_type, amount, description, transaction_date, entered_by, reference_number, category

### 8. SavingAccount Model (`app/Models/SavingAccount.php`)
**Purpose**: Manage member savings accounts with deposit/withdrawal functionality.

**Key Features**:
- **Relationships**: member, createdBy, transactions
- **Business Logic**:
  - `calculateCurrentBalance()`: Calculate current balance
  - `addDeposit()`: Add deposit transaction
  - `addWithdrawal()`: Add withdrawal transaction
  - `getTransactionHistory()`: Get transaction history
  - `calculateMonthlyInterest()`: Calculate monthly interest
  - `applyMonthlyInterest()`: Apply monthly interest
  - `canWithdraw()`: Check if withdrawal is allowed
  - `getAvailableBalance()`: Get available balance
- **Scopes**: active, inactive, closed, byMember, byAccountType, withMinimumBalance
- **Accessors**: statusBadge, formattedCurrentBalance, formattedOpeningBalance, formattedMinimumBalance, formattedAvailableBalance, accountTypeDisplay

**Fillable Fields**: member_id, account_number, account_type, opening_balance, current_balance, interest_rate, minimum_balance, status, opened_date, created_by

### 9. SavingTransaction Model (`app/Models/SavingTransaction.php`)
**Purpose**: Track individual savings account transactions.

**Key Features**:
- **Relationships**: savingAccount, enteredBy
- **Scopes**: deposits, withdrawals, byAccount, byDateRange, today, thisMonth
- **Accessors**: transactionTypeBadge, formattedAmount, formattedBalanceAfter

**Fillable Fields**: saving_account_id, transaction_type, amount, description, transaction_date, entered_by, balance_after, reference_number

### 10. Advertisement Model (`app/Models/Advertisement.php`)
**Purpose**: Manage system advertisements and promotional content.

**Key Features**:
- **Business Logic**:
  - `activate()`: Activate advertisement
  - `deactivate()`: Deactivate advertisement
  - `incrementViewCount()`: Increment view count
  - `incrementClickCount()`: Increment click count
  - `isCurrentlyActive()`: Check if currently active
  - `isExpired()`: Check if expired
  - `isScheduled()`: Check if scheduled
  - `getDaysRemaining()`: Get days remaining
  - `getClickThroughRate()`: Calculate click-through rate
- **Scopes**: active, inactive, expired, scheduled, byPosition, byTargetAudience, orderByPriority, popular, highPerforming
- **Accessors**: status, statusBadge, positionDisplay, targetAudienceDisplay, formattedClickThroughRate, truncatedContent

**Fillable Fields**: title, content, image_path, link_url, position, priority, is_active, start_date, end_date, target_audience, click_count, view_count

## Key Relationships Summary

1. **Users ↔ Branches**: Many-to-many (users assigned to branches, branches have managers)
2. **Branches → Members**: One-to-many (each member belongs to one branch)
3. **Members → Loan Applications**: One-to-many (members can have multiple applications)
4. **Loan Applications → Loans**: One-to-one (approved applications become loans)
5. **Loans → Installments**: One-to-many (loans have multiple installments)
6. **Members → Saving Accounts**: One-to-many (members can have multiple savings accounts)
7. **Saving Accounts → Saving Transactions**: One-to-many (accounts have multiple transactions)
8. **Branches → Branch Transactions**: One-to-many (branches have multiple transactions)
9. **Members → Members**: Self-referencing (reference relationships)

## Usage Examples

### Creating a Loan Application
```php
$member = Member::find(1);
$application = $member->loanApplications()->create([
    'applied_amount' => 50000,
    'reason' => 'Business expansion',
    'loan_cycle_number' => $member->getNextLoanCycleNumber(),
]);
```

### Approving a Loan Application
```php
$application = LoanApplication::find(1);
$reviewer = User::find(1);
$loan = $application->approve($reviewer, [
    'loan_amount' => 50000,
    'interest_rate' => 12.0,
    'loan_duration_months' => 12,
    'repayment_method' => 'weekly',
]);
```

### Collecting an Installment
```php
$installment = Installment::find(1);
$collector = User::find(1);
$installment->markAsPaid($collector, 50, 'Collected on time');
```

### Adding Savings Deposit
```php
$savingAccount = SavingAccount::find(1);
$user = User::find(1);
$transaction = $savingAccount->addDeposit(1000, $user, 'Monthly savings');
```

## Validation Rules
Each model includes comprehensive validation rules accessible via `ModelName::validationRules()` method.

## Testing Recommendations
1. Test all relationship methods
2. Test business logic methods with various scenarios
3. Test scopes with different data sets
4. Test validation rules
5. Test accessor methods
6. Test model events and boot methods
