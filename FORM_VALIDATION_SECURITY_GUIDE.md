# Form Validation & Security Implementation Guide

This guide explains the comprehensive form validation and security measures implemented in the microfinance application.

## 📋 Overview

The implementation includes:
- **Form Request Classes** for comprehensive validation
- **Custom Validation Rules** for Bangladesh-specific data
- **Security Middleware** for protection against attacks
- **Business Rule Validation** for domain-specific logic
- **Error Handling** with user-friendly pages
- **Security Logging** for audit trails

## 🔐 Form Request Classes

### 1. MemberRegistrationRequest
Handles member registration with comprehensive validation:

```php
use App\Http\Requests\MemberRegistrationRequest;

public function store(MemberRegistrationRequest $request)
{
    $validatedData = $request->validated();
    // Data is already validated and sanitized
}
```

**Features:**
- Bangladesh NID validation
- Phone number format validation
- Age restrictions (18-80 years)
- Photo upload security
- Income validation
- Address validation

### 2. LoanApplicationRequest
Validates loan applications with business rules:

```php
use App\Http\Requests\LoanApplicationRequest;

public function store(LoanApplicationRequest $request)
{
    $validatedData = $request->validated();
    // Includes loan eligibility checks
}
```

**Features:**
- Member eligibility validation
- Loan amount limits based on income
- Advance payment restrictions
- Guarantor information validation
- Business plan validation

### 3. UserCreationRequest
Handles user creation with role-based validation:

```php
use App\Http\Requests\UserCreationRequest;

public function store(UserCreationRequest $request)
{
    $validatedData = $request->validated();
    // Role-based validation applied
}
```

**Features:**
- Role-based authorization
- Branch assignment validation
- Password strength requirements
- Employee ID validation
- Member linking validation

### 4. TransactionRequest
Validates financial transactions:

```php
use App\Http\Requests\TransactionRequest;

public function store(TransactionRequest $request)
{
    $validatedData = $request->validated();
    // Financial validation applied
}
```

**Features:**
- Transaction type validation
- Amount limits and validation
- Account balance checks
- Daily transaction limits
- Payment method validation

### 5. ProfileUpdateRequest
Handles profile updates with different validation types:

```php
use App\Http\Requests\ProfileUpdateRequest;

public function update(ProfileUpdateRequest $request)
{
    $validatedData = $request->validated();
    // Profile-specific validation
}
```

**Features:**
- Multi-type validation (profile, password, member_profile)
- Current password verification
- Avatar upload validation
- Role-specific field validation

## 🛡️ Custom Validation Rules

### 1. BangladeshNidRule
Validates Bangladesh National ID numbers:

```php
use App\Rules\BangladeshNidRule;

'nid_number' => ['required', new BangladeshNidRule()]
```

**Validates:**
- 10, 13, or 17 digit formats
- Invalid patterns (all same digits, sequential)
- Proper format structure

### 2. BangladeshPhoneRule
Validates Bangladesh phone numbers:

```php
use App\Rules\BangladeshPhoneRule;

'phone_number' => ['required', new BangladeshPhoneRule()]
```

**Validates:**
- Operator-specific patterns
- Country code handling
- Invalid number patterns
- Mobile number format

### 3. FinancialAmountRule
Validates financial amounts:

```php
use App\Rules\FinancialAmountRule;

'amount' => ['required', new FinancialAmountRule(1000, 500000)]
```

**Features:**
- Min/max amount validation
- Decimal places control
- Currency formatting
- Negative value prevention

### 4. AgeRestrictionRule
Validates age based on date of birth:

```php
use App\Rules\AgeRestrictionRule;

'date_of_birth' => ['required', new AgeRestrictionRule(18, 65)]
```

## 🔒 Security Middleware

### 1. SecurityHeadersMiddleware
Adds security headers to all responses:

**Headers Added:**
- Content Security Policy (CSP)
- X-Frame-Options: DENY
- X-Content-Type-Options: nosniff
- X-XSS-Protection
- Strict Transport Security (HTTPS)
- Permissions Policy

### 2. RateLimitMiddleware
Implements rate limiting for different actions:

```php
Route::post('/login')->middleware('rate.limit:login');
Route::post('/transactions')->middleware('rate.limit:transaction');
```

**Rate Limits:**
- Login: 5 attempts per 15 minutes
- API: 100 requests per minute
- Transactions: 50 per 5 minutes
- File uploads: 10 per 10 minutes

### 3. FileUploadSecurityMiddleware
Secures file uploads:

**Security Checks:**
- File type validation
- MIME type verification
- Malicious content scanning
- File size limits
- Image dimension validation
- EXIF data scanning

## 📊 Business Rule Validation Service

### Usage Example:

```php
use App\Services\BusinessRuleValidationService;

$service = new BusinessRuleValidationService();

// Validate loan eligibility
$result = $service->validateLoanEligibility($member, $amount);
if (!$result['eligible']) {
    return back()->withErrors($result['errors']);
}

// Validate branch assignment
$result = $service->validateBranchAssignment($user, $branchId);
if (!$result['valid']) {
    return back()->withErrors($result['errors']);
}
```

### Available Validations:
- `validateLoanEligibility()` - Member loan eligibility
- `validateBranchAssignment()` - User branch assignment
- `validateRoleChange()` - User role modifications
- `validateTransactionLimits()` - Transaction limits
- `validateMemberRegistration()` - Member registration rules
- `validateLoanApprovalAuthority()` - Loan approval permissions
- `validateDataAccess()` - Data access permissions

## 🔍 Security Service

### Usage Example:

```php
use App\Services\SecurityService;

$security = new SecurityService();

// Log security activities
$security->logActivity('member_created', ['member_id' => $member->id]);

// Validate password strength
$result = $security->validatePasswordStrength($password);

// Check for suspicious login
$result = $security->checkSuspiciousLogin($request);

// Sanitize input data
$clean = $security->sanitizeInput($request->all());
```

### Features:
- Activity logging
- Password strength validation
- Suspicious activity detection
- Input sanitization
- File upload validation
- IP blocking
- Audit trail generation

## 🚨 Error Handling

### Custom Error Pages:
- **403.blade.php** - Access forbidden
- **404.blade.php** - Page not found
- **500.blade.php** - Server error
- **429.blade.php** - Rate limit exceeded

### Exception Handling:
- Authentication exceptions
- Authorization exceptions
- Validation exceptions
- Rate limit exceptions
- Custom error responses for API

## 🔧 Implementation Examples

### 1. Using Form Requests in Controllers:

```php
class MemberController extends Controller
{
    public function store(MemberRegistrationRequest $request)
    {
        $user = auth()->user();
        $businessRuleService = new BusinessRuleValidationService();
        $securityService = new SecurityService();

        // Additional business validation
        $validation = $businessRuleService->validateMemberRegistration(
            $request->validated(), 
            $user
        );
        
        if (!$validation['valid']) {
            return back()->withErrors($validation['errors']);
        }

        // Create member
        $member = Member::create($request->validated());

        // Log activity
        $securityService->logActivity('member_created', [
            'member_id' => $member->id
        ]);

        return redirect()->route('members.show', $member);
    }
}
```

### 2. Adding Custom Validation Rules:

```php
// In your form request
public function rules()
{
    return [
        'nid_number' => [
            'required',
            new BangladeshNidRule(),
            Rule::unique('members')->ignore($this->member)
        ],
        'phone_number' => [
            'required',
            new BangladeshPhoneRule(),
            Rule::unique('members')->ignore($this->member)
        ],
        'loan_amount' => [
            'required',
            new FinancialAmountRule(1000, 500000)
        ]
    ];
}
```

### 3. Applying Security Middleware:

```php
// In routes/web.php
Route::middleware(['rate.limit:login'])->group(function () {
    Auth::routes();
});

Route::middleware(['file.security'])->group(function () {
    Route::post('/upload', 'FileController@upload');
});
```

## 📝 Best Practices

1. **Always use Form Requests** for complex validation
2. **Implement business rule validation** for domain logic
3. **Log security activities** for audit trails
4. **Use rate limiting** on sensitive endpoints
5. **Validate file uploads** thoroughly
6. **Sanitize user input** before processing
7. **Implement proper error handling** with user-friendly messages
8. **Use custom validation rules** for reusable validation logic

## 🔄 Testing

### Testing Form Requests:
```php
public function test_member_registration_validation()
{
    $response = $this->post('/members', [
        'name' => '', // Invalid
        'nid_number' => '123', // Invalid format
    ]);

    $response->assertSessionHasErrors(['name', 'nid_number']);
}
```

### Testing Security Features:
```php
public function test_rate_limiting()
{
    for ($i = 0; $i < 6; $i++) {
        $this->post('/login', ['email' => '<EMAIL>']);
    }
    
    $response = $this->post('/login', ['email' => '<EMAIL>']);
    $response->assertStatus(429);
}
```

This comprehensive validation and security system ensures data integrity, prevents attacks, and provides a secure foundation for the microfinance application.
