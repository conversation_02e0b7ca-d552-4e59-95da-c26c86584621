<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Models\Advertisement;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "api" middleware group. Make something great!
|
*/

Route::middleware('auth:sanctum')->get('/user', function (Request $request) {
    return $request->user();
});

// Advertisement tracking routes (no auth required for tracking)
Route::post('/advertisements/{advertisement}/view', function (Advertisement $advertisement) {
    $advertisement->incrementViewCount();
    return response()->json(['success' => true]);
});

Route::post('/advertisements/{advertisement}/click', function (Advertisement $advertisement) {
    $advertisement->incrementClickCount();
    return response()->json(['success' => true]);
});
