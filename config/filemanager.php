<?php

return [

    /*
    |--------------------------------------------------------------------------
    | File Manager Configuration
    |--------------------------------------------------------------------------
    |
    | This file contains configuration options for the file management system
    | including upload limits, image processing settings, and security options.
    |
    */

    /*
    |--------------------------------------------------------------------------
    | Upload Limits
    |--------------------------------------------------------------------------
    |
    | Define maximum file sizes for different types of uploads.
    | Sizes are in bytes unless otherwise specified.
    |
    */
    'upload_limits' => [
        'member_photo' => 2 * 1024 * 1024, // 2MB
        'advertisement_image' => 5 * 1024 * 1024, // 5MB
        'document' => 10 * 1024 * 1024, // 10MB
        'report' => 20 * 1024 * 1024, // 20MB
        'backup' => 100 * 1024 * 1024, // 100MB
    ],

    /*
    |--------------------------------------------------------------------------
    | Allowed File Types
    |--------------------------------------------------------------------------
    |
    | Define which file types are allowed for different categories.
    |
    */
    'allowed_types' => [
        'images' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
        'documents' => ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv'],
        'archives' => ['zip', 'rar', '7z'],
        'member_photos' => ['jpg', 'jpeg', 'png', 'webp'],
        'advertisements' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
    ],

    /*
    |--------------------------------------------------------------------------
    | Image Processing Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for image processing including sizes, quality, and optimization.
    |
    */
    'image_processing' => [
        'sizes' => [
            'thumbnail' => ['width' => 150, 'height' => 150],
            'small' => ['width' => 300, 'height' => 300],
            'medium' => ['width' => 600, 'height' => 600],
            'large' => ['width' => 1200, 'height' => 1200],
        ],
        
        'quality' => [
            'thumbnail' => 70,
            'small' => 75,
            'medium' => 80,
            'large' => 85,
            'original' => 90,
        ],
        
        'member_photo_sizes' => [
            'thumbnail' => ['width' => 100, 'height' => 100],
            'profile' => ['width' => 200, 'height' => 200],
            'full' => ['width' => 400, 'height' => 400],
        ],
        
        'advertisement_sizes' => [
            'banner' => ['width' => 1200, 'height' => 400],
            'sidebar' => ['width' => 300, 'height' => 250],
            'thumbnail' => ['width' => 150, 'height' => 150],
        ],
        
        'optimization' => [
            'auto_orient' => true,
            'strip_exif' => true,
            'progressive_jpeg' => true,
            'optimize_png' => true,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Watermark Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for image watermarking.
    |
    */
    'watermark' => [
        'enabled' => env('WATERMARK_ENABLED', false),
        'text' => env('WATERMARK_TEXT', config('app.name')),
        'opacity' => 0.7,
        'position' => 'bottom-right',
        'margin' => 20,
        'font_size' => 16,
        'color' => 'rgba(255, 255, 255, 0.7)',
    ],

    /*
    |--------------------------------------------------------------------------
    | Security Settings
    |--------------------------------------------------------------------------
    |
    | File security and validation settings.
    |
    */
    'security' => [
        'scan_uploads' => true,
        'quarantine_suspicious' => true,
        'max_filename_length' => 255,
        'blocked_extensions' => [
            'php', 'php3', 'php4', 'php5', 'phtml', 'exe', 'bat', 'cmd', 'com', 'scr',
            'vbs', 'vbe', 'js', 'jar', 'app', 'deb', 'rpm', 'dmg', 'iso', 'img'
        ],
        'malicious_patterns' => [
            '/<\?php/i', '/<script/i', '/javascript:/i', '/vbscript:/i',
            '/onload=/i', '/onerror=/i', '/eval\(/i', '/base64_decode/i',
            '/shell_exec/i', '/system\(/i', '/exec\(/i', '/passthru/i'
        ],
        'image_validation' => [
            'max_width' => 4000,
            'max_height' => 4000,
            'min_width' => 50,
            'min_height' => 50,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Storage Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for file storage and organization.
    |
    */
    'storage' => [
        'default_disk' => 'public',
        'image_disk' => 'images',
        'document_disk' => 'documents',
        'temp_disk' => 'temp',
        'backup_disk' => 'backups',
        'quarantine_disk' => 'quarantine',
        
        'directory_structure' => [
            'use_date_folders' => true,
            'date_format' => 'Y/m',
            'category_folders' => true,
        ],
        
        'cleanup' => [
            'temp_files_hours' => 24,
            'quarantine_files_days' => 30,
            'backup_retention_days' => 90,
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Document Management
    |--------------------------------------------------------------------------
    |
    | Settings for document management and versioning.
    |
    */
    'documents' => [
        'versioning' => [
            'enabled' => true,
            'max_versions' => 10,
            'auto_cleanup' => true,
        ],
        
        'encryption' => [
            'enabled' => env('DOCUMENT_ENCRYPTION', false),
            'algorithm' => 'AES-256-CBC',
        ],
        
        'categories' => [
            'reports' => [
                'max_size' => 10 * 1024 * 1024,
                'allowed_types' => ['pdf', 'doc', 'docx'],
                'versioning' => true,
                'encryption' => false,
            ],
            'member_documents' => [
                'max_size' => 5 * 1024 * 1024,
                'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
                'versioning' => true,
                'encryption' => true,
            ],
            'financial_reports' => [
                'max_size' => 20 * 1024 * 1024,
                'allowed_types' => ['pdf', 'xls', 'xlsx', 'csv'],
                'versioning' => true,
                'encryption' => true,
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Access Control
    |--------------------------------------------------------------------------
    |
    | File access control settings.
    |
    */
    'access_control' => [
        'secure_downloads' => true,
        'download_expiration' => 60, // minutes
        'max_downloads_per_file' => 10,
        'log_downloads' => true,
        
        'permissions' => [
            'admin' => ['*'],
            'manager' => ['members', 'reports', 'advertisements'],
            'field_officer' => ['members'],
            'member' => ['own_files'],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Performance Settings
    |--------------------------------------------------------------------------
    |
    | Settings for performance optimization.
    |
    */
    'performance' => [
        'thumbnail_cache' => true,
        'cache_duration' => 3600, // seconds
        'lazy_loading' => true,
        'progressive_upload' => true,
        'chunk_size' => 1024 * 1024, // 1MB chunks
    ],

    /*
    |--------------------------------------------------------------------------
    | Backup Settings
    |--------------------------------------------------------------------------
    |
    | Configuration for file backup and recovery.
    |
    */
    'backup' => [
        'enabled' => env('FILE_BACKUP_ENABLED', true),
        'cloud_backup' => env('CLOUD_BACKUP_ENABLED', false),
        'backup_schedule' => 'daily',
        'retention_days' => 30,
        'compress_backups' => true,
    ],

    /*
    |--------------------------------------------------------------------------
    | Monitoring and Logging
    |--------------------------------------------------------------------------
    |
    | File system monitoring and logging settings.
    |
    */
    'monitoring' => [
        'log_uploads' => true,
        'log_downloads' => true,
        'log_deletions' => true,
        'monitor_storage_usage' => true,
        'alert_threshold' => 80, // percentage
        'log_channel' => 'files',
    ],

    /*
    |--------------------------------------------------------------------------
    | CDN Settings
    |--------------------------------------------------------------------------
    |
    | Content Delivery Network configuration.
    |
    */
    'cdn' => [
        'enabled' => env('CDN_ENABLED', false),
        'url' => env('CDN_URL'),
        'public_files_only' => true,
        'cache_control' => 'public, max-age=31536000', // 1 year
    ],

];
