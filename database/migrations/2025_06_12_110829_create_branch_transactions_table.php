<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branch_transactions', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('branch_id');
            $table->enum('entry_type', ['income', 'expense']);
            $table->unsignedInteger('serial_no');
            $table->date('date');
            $table->text('description');
            $table->string('account_no', 50)->nullable();
            $table->string('category', 100);
            $table->string('voucher_no', 50)->nullable();
            $table->decimal('amount', 12, 2);
            $table->unsignedBigInteger('entered_by');
            $table->timestamps();

            // Add indexes
            $table->index('branch_id');
            $table->index('entry_type');
            $table->index('date');
            $table->index('category');
            $table->index('entered_by');
            $table->index('voucher_no');

            // Composite index for branch and serial number
            $table->unique(['branch_id', 'serial_no']);

            // Foreign key constraints
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('cascade');
            $table->foreign('entered_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_transactions');
    }
};
