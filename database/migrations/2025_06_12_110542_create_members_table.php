<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('members', function (Blueprint $table) {
            $table->id();
            $table->string('member_id', 20)->unique();
            $table->string('name', 100);
            $table->string('father_or_husband_name', 100);
            $table->string('mother_name', 100);
            $table->text('present_address');
            $table->text('permanent_address');
            $table->string('nid_number', 20)->unique();
            $table->date('date_of_birth');
            $table->enum('religion', ['islam', 'hinduism', 'christianity', 'buddhism', 'other'])->default('islam');
            $table->string('phone_number', 15);
            $table->enum('blood_group', ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'])->nullable();
            $table->string('photo')->nullable();
            $table->string('occupation', 100);
            $table->unsignedBigInteger('reference_id')->nullable();
            $table->unsignedBigInteger('branch_id');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            // Add indexes
            $table->index('member_id');
            $table->index('nid_number');
            $table->index('phone_number');
            $table->index('branch_id');
            $table->index('created_by');
            $table->index('reference_id');

            // Foreign key constraints
            $table->foreign('reference_id')->references('id')->on('members')->onDelete('set null');
            $table->foreign('branch_id')->references('id')->on('branches')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('members');
    }
};
