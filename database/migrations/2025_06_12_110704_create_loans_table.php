<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('loans', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('loan_application_id')->unique();
            $table->date('loan_date');
            $table->decimal('loan_amount', 12, 2);
            $table->decimal('total_repayment_amount', 12, 2);
            $table->integer('repayment_duration'); // in weeks or months
            $table->enum('repayment_method', ['weekly', 'monthly'])->default('weekly');
            $table->integer('installment_count');
            $table->decimal('installment_amount', 10, 2);
            $table->decimal('advance_payment', 10, 2)->default(0);
            $table->date('first_installment_date');
            $table->date('last_installment_date');
            $table->timestamps();

            // Add indexes
            $table->index('loan_application_id');
            $table->index('loan_date');
            $table->index('repayment_method');
            $table->index('first_installment_date');
            $table->index('last_installment_date');

            // Foreign key constraints
            $table->foreign('loan_application_id')->references('id')->on('loan_applications')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('loans');
    }
};
