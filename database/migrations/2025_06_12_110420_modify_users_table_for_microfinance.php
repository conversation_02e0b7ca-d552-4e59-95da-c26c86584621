<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // Add role enum column
            $table->enum('role', ['admin', 'manager', 'field_officer', 'member'])
                  ->default('member')
                  ->after('email_verified_at');

            // Add member_id for linking users to members table
            $table->string('member_id')->nullable()->unique()->after('role');

            // Add branch_id for user's assigned branch
            $table->unsignedBigInteger('branch_id')->nullable()->after('member_id');

            // Add is_active status
            $table->boolean('is_active')->default(true)->after('branch_id');

            // Add indexes for better performance
            $table->index('role');
            $table->index('member_id');
            $table->index('branch_id');
            $table->index('is_active');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropIndex(['role']);
            $table->dropIndex(['member_id']);
            $table->dropIndex(['branch_id']);
            $table->dropIndex(['is_active']);

            $table->dropColumn(['role', 'member_id', 'branch_id', 'is_active']);
        });
    }
};
