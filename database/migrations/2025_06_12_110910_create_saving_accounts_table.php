<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('saving_accounts', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('member_id');
            $table->enum('saving_type', ['general', 'dps', 'fdr'])->default('general');
            $table->string('joint_photo')->nullable();
            $table->string('nominee_name', 100)->nullable();
            $table->string('nominee_relation', 50)->nullable();
            $table->enum('saving_method', ['daily', 'monthly'])->default('monthly');
            $table->decimal('monthly_amount', 10, 2)->default(0);
            $table->decimal('fdr_amount', 12, 2)->nullable();
            $table->date('start_date');
            $table->unsignedBigInteger('created_by');
            $table->timestamps();

            // Add indexes
            $table->index('member_id');
            $table->index('saving_type');
            $table->index('saving_method');
            $table->index('start_date');
            $table->index('created_by');

            // Foreign key constraints
            $table->foreign('member_id')->references('id')->on('members')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('saving_accounts');
    }
};
