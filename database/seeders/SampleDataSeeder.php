<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Savings;
use App\Models\Transaction;
use App\Models\User;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample branches
        $dhakaBranch = Branch::create([
            'name' => 'Dhaka Main Branch',
            'code' => 'DHK001',
            'address' => 'House 123, Road 15, Dhanmondi, Dhaka-1205',
            'phone' => '+880-2-9876543',
            'email' => '<EMAIL>',
            'manager_id' => User::where('email', '<EMAIL>')->first()->id,
            'status' => 'active',
        ]);

        $chittagongBranch = Branch::create([
            'name' => 'Chittagong Branch',
            'code' => 'CTG001',
            'address' => 'GEC Circle, Chittagong-4000',
            'phone' => '+880-31-123456',
            'email' => '<EMAIL>',
            'status' => 'active',
        ]);

        $sylhetBranch = Branch::create([
            'name' => 'Sylhet Branch',
            'code' => 'SYL001',
            'address' => 'Zindabazar, Sylhet-3100',
            'phone' => '+880-821-123456',
            'email' => '<EMAIL>',
            'status' => 'active',
        ]);

        // Get field officer
        $fieldOfficer = User::where('email', '<EMAIL>')->first();

        // Create sample members
        $members = [
            [
                'member_id' => 'MEM001',
                'first_name' => 'Fatima',
                'last_name' => 'Begum',
                'father_name' => 'Abdul Rahman',
                'mother_name' => 'Rashida Begum',
                'date_of_birth' => '1985-03-15',
                'gender' => 'female',
                'nid_number' => '1234567890123',
                'phone' => '+880-1712345678',
                'email' => '<EMAIL>',
                'present_address' => 'House 45, Road 7, Mohammadpur, Dhaka',
                'permanent_address' => 'Village: Rampur, Upazila: Savar, District: Dhaka',
                'occupation' => 'Small Business Owner',
                'monthly_income' => 25000.00,
                'branch_id' => $dhakaBranch->id,
                'field_officer_id' => $fieldOfficer->id,
                'joining_date' => '2024-01-15',
            ],
            [
                'member_id' => 'MEM002',
                'first_name' => 'Mohammad',
                'last_name' => 'Karim',
                'father_name' => 'Abdul Karim',
                'mother_name' => 'Salma Begum',
                'date_of_birth' => '1978-08-22',
                'gender' => 'male',
                'nid_number' => '2345678901234',
                'phone' => '+880-1812345679',
                'present_address' => 'House 78, Road 12, Uttara, Dhaka',
                'permanent_address' => 'Village: Goalanda, Upazila: Rajbari, District: Rajbari',
                'occupation' => 'Farmer',
                'monthly_income' => 18000.00,
                'branch_id' => $dhakaBranch->id,
                'field_officer_id' => $fieldOfficer->id,
                'joining_date' => '2024-02-01',
            ],
            [
                'member_id' => 'MEM003',
                'first_name' => 'Rashida',
                'last_name' => 'Khatun',
                'father_name' => 'Abdur Rahman',
                'mother_name' => 'Amina Begum',
                'date_of_birth' => '1990-12-10',
                'gender' => 'female',
                'nid_number' => '3456789012345',
                'phone' => '+880-1912345680',
                'present_address' => 'Agrabad, Chittagong',
                'permanent_address' => 'Village: Hathazari, Upazila: Hathazari, District: Chittagong',
                'occupation' => 'Tailor',
                'monthly_income' => 15000.00,
                'branch_id' => $chittagongBranch->id,
                'field_officer_id' => $fieldOfficer->id,
                'joining_date' => '2024-03-01',
            ],
        ];

        foreach ($members as $memberData) {
            $member = Member::create($memberData);

            // Create savings account for each member
            $savings = Savings::create([
                'account_number' => 'SAV' . str_pad($member->id, 6, '0', STR_PAD_LEFT),
                'member_id' => $member->id,
                'branch_id' => $member->branch_id,
                'account_type' => 'general',
                'balance' => rand(5000, 50000),
                'interest_rate' => 5.5,
                'opening_date' => $member->joining_date,
                'status' => 'active',
            ]);

            // Create a loan for some members
            if (in_array($member->member_id, ['MEM001', 'MEM002'])) {
                $loan = Loan::create([
                    'loan_id' => 'LOAN' . str_pad($member->id, 6, '0', STR_PAD_LEFT),
                    'member_id' => $member->id,
                    'branch_id' => $member->branch_id,
                    'loan_type' => $member->member_id === 'MEM001' ? 'business' : 'agriculture',
                    'amount' => $member->member_id === 'MEM001' ? 100000 : 75000,
                    'interest_rate' => 12.0,
                    'duration_months' => 12,
                    'monthly_installment' => $member->member_id === 'MEM001' ? 9330 : 6998,
                    'status' => 'active',
                    'application_date' => Carbon::parse($member->joining_date)->addDays(30),
                    'approval_date' => Carbon::parse($member->joining_date)->addDays(35),
                    'disbursement_date' => Carbon::parse($member->joining_date)->addDays(40),
                    'approved_by' => User::where('email', '<EMAIL>')->first()->id,
                    'purpose' => $member->member_id === 'MEM001' ? 'Expand tailoring business' : 'Purchase farming equipment',
                ]);

                // Create loan disbursement transaction
                Transaction::create([
                    'transaction_id' => 'TXN' . time() . rand(100, 999),
                    'member_id' => $member->id,
                    'branch_id' => $member->branch_id,
                    'type' => 'loan_disbursement',
                    'amount' => $loan->amount,
                    'balance_before' => $savings->balance,
                    'balance_after' => $savings->balance + $loan->amount,
                    'reference_type' => 'loan',
                    'reference_id' => $loan->id,
                    'description' => 'Loan disbursement for ' . $loan->purpose,
                    'processed_by' => $fieldOfficer->id,
                    'transaction_date' => $loan->disbursement_date,
                ]);

                // Update savings balance
                $savings->update(['balance' => $savings->balance + $loan->amount]);
            }

            // Create some deposit transactions
            for ($i = 0; $i < 3; $i++) {
                $depositAmount = rand(1000, 5000);
                $balanceBefore = $savings->balance;
                $balanceAfter = $balanceBefore + $depositAmount;

                Transaction::create([
                    'transaction_id' => 'TXN' . time() . rand(100, 999) . $i,
                    'member_id' => $member->id,
                    'branch_id' => $member->branch_id,
                    'type' => 'deposit',
                    'amount' => $depositAmount,
                    'balance_before' => $balanceBefore,
                    'balance_after' => $balanceAfter,
                    'reference_type' => 'savings',
                    'reference_id' => $savings->id,
                    'description' => 'Regular savings deposit',
                    'processed_by' => $fieldOfficer->id,
                    'transaction_date' => Carbon::parse($member->joining_date)->addDays(rand(10, 90)),
                ]);

                $savings->update(['balance' => $balanceAfter]);
            }
        }
    }
}
