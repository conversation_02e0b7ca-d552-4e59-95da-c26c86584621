<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolePermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Permissions
        $permissions = [
            // User Management
            'view-users',
            'create-users',
            'edit-users',
            'delete-users',

            // Member Management
            'view-members',
            'create-members',
            'edit-members',
            'delete-members',

            // Loan Management
            'view-loans',
            'create-loans',
            'edit-loans',
            'delete-loans',
            'approve-loans',
            'disburse-loans',

            // Savings Management
            'view-savings',
            'create-savings',
            'edit-savings',
            'delete-savings',

            // Transaction Management
            'view-transactions',
            'create-transactions',
            'edit-transactions',
            'delete-transactions',

            // Branch Management
            'view-branches',
            'create-branches',
            'edit-branches',
            'delete-branches',

            // Reports
            'view-reports',
            'generate-reports',

            // System Settings
            'manage-settings',
            'manage-roles',
        ];

        foreach ($permissions as $permission) {
            Permission::create(['name' => $permission]);
        }

        // Create Roles and assign permissions

        // Admin Role - Full access
        $admin = Role::create(['name' => 'admin']);
        $admin->givePermissionTo(Permission::all());

        // Manager Role - Branch management and oversight
        $manager = Role::create(['name' => 'manager']);
        $manager->givePermissionTo([
            'view-members', 'create-members', 'edit-members',
            'view-loans', 'create-loans', 'edit-loans', 'approve-loans', 'disburse-loans',
            'view-savings', 'create-savings', 'edit-savings',
            'view-transactions', 'create-transactions',
            'view-reports', 'generate-reports',
            'view-branches',
        ]);

        // Field Officer Role - Direct member interaction
        $fieldOfficer = Role::create(['name' => 'field-officer']);
        $fieldOfficer->givePermissionTo([
            'view-members', 'create-members', 'edit-members',
            'view-loans', 'create-loans', 'edit-loans',
            'view-savings', 'create-savings', 'edit-savings',
            'view-transactions', 'create-transactions',
        ]);

        // Member Role - Limited access to own data
        $member = Role::create(['name' => 'member']);
        $member->givePermissionTo([
            'view-loans',
            'view-savings',
            'view-transactions',
        ]);
    }
}
