<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Branch;
use App\Models\User;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Branch>
 */
class BranchFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->company() . ' Branch',
            'address' => fake()->address(),
            'manager_id' => null, // Will be set when needed
        ];
    }

    /**
     * Indicate that the branch has a manager.
     */
    public function withManager(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'manager_id' => User::factory()->create(['role' => 'manager'])->id,
            ];
        });
    }
}
