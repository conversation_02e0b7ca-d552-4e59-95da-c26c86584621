<?php

namespace Database\Factories;

use Illuminate\Database\Eloquent\Factories\Factory;
use App\Models\Advertisement;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Advertisement>
 */
class AdvertisementFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'title' => fake()->sentence(4),
            'content' => fake()->paragraph(3),
            'image_path' => null,
            'link_url' => fake()->optional()->url(),
            'position' => fake()->randomElement(['header', 'sidebar', 'footer', 'popup', 'inline', 'login']),
            'priority' => fake()->numberBetween(1, 10),
            'is_active' => fake()->boolean(80), // 80% chance of being active
            'start_date' => fake()->optional()->dateTimeBetween('-1 month', 'now'),
            'end_date' => fake()->optional()->dateTimeBetween('now', '+3 months'),
            'target_audience' => fake()->randomElement(['all', 'members', 'staff', 'managers', 'admins']),
            'click_count' => fake()->numberBetween(0, 1000),
            'view_count' => fake()->numberBetween(0, 5000),
        ];
    }

    /**
     * Indicate that the advertisement is active.
     */
    public function active(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
                'start_date' => now()->subDays(1),
                'end_date' => now()->addDays(30),
            ];
        });
    }

    /**
     * Indicate that the advertisement is for login page.
     */
    public function forLogin(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'position' => 'login',
                'is_active' => true,
                'priority' => fake()->numberBetween(7, 10),
            ];
        });
    }

    /**
     * Indicate that the advertisement is expired.
     */
    public function expired(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
                'start_date' => now()->subDays(30),
                'end_date' => now()->subDays(1),
            ];
        });
    }

    /**
     * Indicate that the advertisement is scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
                'start_date' => now()->addDays(1),
                'end_date' => now()->addDays(30),
            ];
        });
    }
}
