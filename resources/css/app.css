@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Modern CSS Variables for Enhanced Styling */
:root {
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

    --gradient-primary: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --gradient-accent: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* Glassmorphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: none;
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* Modern Button Styles - Commented out for now due to custom color issues */
/*
.btn-primary {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95 bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white shadow-lg hover:shadow-xl focus:ring-primary-500;
}
*/

/* Modern Form Styles - Simplified */
.form-group {
    margin-bottom: 1.5rem;
}

.form-input {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.75rem;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.form-input:focus {
    outline: none;
    border-color: #22c55e;
    box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
    background-color: white;
    transform: scale(1.02);
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

/* Modern Card Styles - Simplified */
.card-modern {
    background: linear-gradient(135deg, #ffffff 0%, #f9fafb 100%);
    border-radius: 1rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(229, 231, 235, 0.5);
    overflow: hidden;
    transition: all 0.3s ease;
}

.card-modern:hover {
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.card-header {
    padding: 1.25rem 1.5rem;
    border-bottom: 1px solid rgba(229, 231, 235, 0.5);
    background: linear-gradient(90deg, rgba(249, 250, 251, 0.8) 0%, rgba(255, 255, 255, 0.8) 100%);
}

.card-body {
    padding: 1.25rem 1.5rem;
}

/* Simplified Modern Styles */
.dashboard-card {
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(4px);
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(229, 231, 235, 0.5);
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.dashboard-card:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
    background: white;
}

.dashboard-card-gradient {
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    padding: 1.5rem;
    color: white;
    transition: all 0.3s ease;
}

.dashboard-card-gradient:hover {
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    transform: scale(1.02);
}

.stat-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.25rem;
    transition: all 0.3s ease;
}

.nav-modern {
    background: linear-gradient(180deg, #ffffff 0%, rgba(249, 250, 251, 0.5) 100%);
    backdrop-filter: blur(4px);
    border-right: 1px solid rgba(229, 231, 235, 0.5);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.nav-link-modern {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    margin: 0 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.75rem;
    color: #6b7280;
    transition: all 0.3s ease;
}

.nav-link-modern:hover {
    color: #111827;
    background: linear-gradient(90deg, #f0fdf4 0%, #dcfce7 100%);
}

.nav-link-modern.active {
    background: linear-gradient(90deg, #22c55e 0%, #16a34a 100%);
    color: white;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Modern Utility Classes */
.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-secondary {
    background: var(--gradient-secondary);
}

.gradient-accent {
    background: var(--gradient-accent);
}

.text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
}

.shadow-glow {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.shadow-glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-orange {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.3);
}

/* Modern Dashboard Cards */
.dashboard-card {
    @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-6 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:bg-white;
}

.dashboard-card-gradient {
    @apply rounded-2xl shadow-lg p-6 text-white transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
}

.stat-icon {
    @apply w-12 h-12 rounded-xl flex items-center justify-center text-xl transition-all duration-300 group-hover:scale-110;
}

/* Modern Alert Styles */
.alert {
    @apply p-4 rounded-xl border-l-4 transition-all duration-300 hover:shadow-md;
}

.alert-success {
    @apply bg-gradient-to-r from-success-50 to-success-100 border-success-500 text-success-800;
}

.alert-warning {
    @apply bg-gradient-to-r from-warning-50 to-warning-100 border-warning-500 text-warning-800;
}

.alert-danger {
    @apply bg-gradient-to-r from-danger-50 to-danger-100 border-danger-500 text-danger-800;
}

.alert-info {
    @apply bg-gradient-to-r from-secondary-50 to-secondary-100 border-secondary-500 text-secondary-800;
}

/* Responsive Navigation */
@media (max-width: 768px) {
    .mobile-menu-open {
        @apply block;
    }

    .mobile-menu-closed {
        @apply hidden;
    }
}
