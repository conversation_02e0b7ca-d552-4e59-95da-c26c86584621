@import 'tailwindcss';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';
@source '../**/*.blade.php';
@source '../**/*.js';

@theme {
    --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji',
        'Segoe UI Symbol', 'Noto Color Emoji';
}

/* Modern CSS Variables for Enhanced Styling */
:root {
    --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
    --shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
    --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
    --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
    --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
    --shadow-2xl: 0 25px 50px -12px rgb(0 0 0 / 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgb(0 0 0 / 0.05);

    --gradient-primary: linear-gradient(135deg, #22c55e 0%, #16a34a 100%);
    --gradient-secondary: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    --gradient-accent: linear-gradient(135deg, #f97316 0%, #ea580c 100%);
    --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
}

/* Glassmorphism Effects */
.glass {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-dark {
    background: rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Modern Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

@keyframes bounce {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: none;
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

.animate-fadeInUp {
    animation: fadeInUp 0.6s ease-out;
}

.animate-fadeInLeft {
    animation: fadeInLeft 0.6s ease-out;
}

.animate-fadeInRight {
    animation: fadeInRight 0.6s ease-out;
}

.animate-pulse {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
    animation: bounce 1s infinite;
}

/* Modern Button Styles */
.btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-xl transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform hover:scale-105 active:scale-95;
}

.btn-primary {
    @apply btn bg-gradient-to-r from-primary-600 to-primary-700 hover:from-primary-700 hover:to-primary-800 text-white shadow-lg hover:shadow-xl focus:ring-primary-500;
}

.btn-secondary {
    @apply btn bg-gradient-to-r from-secondary-600 to-secondary-700 hover:from-secondary-700 hover:to-secondary-800 text-white shadow-lg hover:shadow-xl focus:ring-secondary-500;
}

.btn-accent {
    @apply btn bg-gradient-to-r from-accent-600 to-accent-700 hover:from-accent-700 hover:to-accent-800 text-white shadow-lg hover:shadow-xl focus:ring-accent-500;
}

.btn-success {
    @apply btn bg-gradient-to-r from-success-600 to-success-700 hover:from-success-700 hover:to-success-800 text-white shadow-lg hover:shadow-xl focus:ring-success-500;
}

.btn-warning {
    @apply btn bg-gradient-to-r from-warning-600 to-warning-700 hover:from-warning-700 hover:to-warning-800 text-white shadow-lg hover:shadow-xl focus:ring-warning-500;
}

.btn-danger {
    @apply btn bg-gradient-to-r from-danger-600 to-danger-700 hover:from-danger-700 hover:to-danger-800 text-white shadow-lg hover:shadow-xl focus:ring-danger-500;
}

.btn-outline {
    @apply btn border-2 border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 shadow-md hover:shadow-lg focus:ring-gray-500;
}

.btn-ghost {
    @apply btn text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-gray-500;
}

.btn-lg {
    @apply px-6 py-3 text-base rounded-2xl;
}

.btn-sm {
    @apply px-3 py-1.5 text-xs rounded-lg;
}

/* Modern Form Styles */
.form-group {
    @apply space-y-2;
}

.form-input {
    @apply block w-full px-4 py-3 border border-gray-300 rounded-xl bg-white/50 backdrop-blur-sm focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 transition-all duration-300 placeholder-gray-400;
}

.form-input:focus {
    @apply bg-white shadow-lg transform scale-[1.02];
}

.form-input.error {
    @apply border-danger-300 focus:ring-danger-500 focus:border-danger-500 bg-danger-50/50;
}

.form-label {
    @apply block text-sm font-semibold text-gray-700 mb-2;
}

.form-error {
    @apply text-sm text-danger-600 mt-1 flex items-center;
}

.form-select {
    @apply form-input appearance-none bg-white cursor-pointer;
}

/* Modern Card Styles */
.card {
    @apply bg-white/80 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
}

.card-modern {
    @apply bg-gradient-to-br from-white to-gray-50/50 rounded-2xl shadow-xl border border-gray-200/50 overflow-hidden transition-all duration-300 hover:shadow-2xl;
}

.card-glass {
    @apply glass rounded-2xl overflow-hidden transition-all duration-300 hover:shadow-xl;
}

.card-header {
    @apply px-6 py-5 border-b border-gray-200/50 bg-gradient-to-r from-gray-50/80 to-white/80;
}

.card-body {
    @apply px-6 py-5;
}

.card-footer {
    @apply px-6 py-4 border-t border-gray-200/50 bg-gradient-to-r from-gray-50/80 to-white/80;
}

/* Modern Badge Styles */
.badge {
    @apply inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold transition-all duration-300 hover:scale-105;
}

.badge-primary {
    @apply bg-gradient-to-r from-primary-100 to-primary-200 text-primary-800 shadow-sm;
}

.badge-secondary {
    @apply bg-gradient-to-r from-secondary-100 to-secondary-200 text-secondary-800 shadow-sm;
}

.badge-success {
    @apply bg-gradient-to-r from-success-100 to-success-200 text-success-800 shadow-sm;
}

.badge-warning {
    @apply bg-gradient-to-r from-warning-100 to-warning-200 text-warning-800 shadow-sm;
}

.badge-danger {
    @apply bg-gradient-to-r from-danger-100 to-danger-200 text-danger-800 shadow-sm;
}

/* Modern Table Styles */
.table {
    @apply min-w-full divide-y divide-gray-200/50;
}

.table th {
    @apply px-6 py-4 bg-gradient-to-r from-gray-50 to-gray-100 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider;
}

.table td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
}

.table tbody tr {
    @apply transition-all duration-200 hover:bg-gray-50/50 hover:shadow-sm;
}

.table tbody tr:nth-child(even) {
    @apply bg-gray-50/30;
}

/* Modern Loading Spinner */
.spinner {
    @apply inline-block w-5 h-5 border-2 border-gray-300 border-t-primary-600 rounded-full animate-spin;
}

.spinner-lg {
    @apply w-8 h-8 border-4;
}

/* Modern Navigation Styles */
.nav-modern {
    @apply bg-gradient-to-b from-white to-gray-50/50 backdrop-blur-sm border-r border-gray-200/50 shadow-xl;
}

.nav-link-modern {
    @apply flex items-center px-4 py-3 mx-2 text-sm font-medium rounded-xl text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-primary-50 hover:to-primary-100 transition-all duration-300 group;
}

.nav-link-modern.active {
    @apply bg-gradient-to-r from-primary-500 to-primary-600 text-white shadow-lg;
}

.nav-link-modern i {
    @apply mr-3 text-gray-400 w-5 h-5 flex-shrink-0 transition-all duration-300 group-hover:text-primary-500;
}

.nav-link-modern.active i {
    @apply text-white;
}

/* Modern Utility Classes */
.gradient-primary {
    background: var(--gradient-primary);
}

.gradient-secondary {
    background: var(--gradient-secondary);
}

.gradient-accent {
    background: var(--gradient-accent);
}

.text-gradient {
    @apply bg-gradient-to-r from-primary-600 to-secondary-600 bg-clip-text text-transparent;
}

.shadow-glow {
    box-shadow: 0 0 20px rgba(34, 197, 94, 0.3);
}

.shadow-glow-blue {
    box-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
}

.shadow-glow-orange {
    box-shadow: 0 0 20px rgba(249, 115, 22, 0.3);
}

/* Modern Dashboard Cards */
.dashboard-card {
    @apply bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg border border-gray-200/50 p-6 transition-all duration-300 hover:shadow-xl hover:scale-[1.02] hover:bg-white;
}

.dashboard-card-gradient {
    @apply rounded-2xl shadow-lg p-6 text-white transition-all duration-300 hover:shadow-xl hover:scale-[1.02];
}

.stat-icon {
    @apply w-12 h-12 rounded-xl flex items-center justify-center text-xl transition-all duration-300 group-hover:scale-110;
}

/* Modern Alert Styles */
.alert {
    @apply p-4 rounded-xl border-l-4 transition-all duration-300 hover:shadow-md;
}

.alert-success {
    @apply bg-gradient-to-r from-success-50 to-success-100 border-success-500 text-success-800;
}

.alert-warning {
    @apply bg-gradient-to-r from-warning-50 to-warning-100 border-warning-500 text-warning-800;
}

.alert-danger {
    @apply bg-gradient-to-r from-danger-50 to-danger-100 border-danger-500 text-danger-800;
}

.alert-info {
    @apply bg-gradient-to-r from-secondary-50 to-secondary-100 border-secondary-500 text-secondary-800;
}

/* Responsive Navigation */
@media (max-width: 768px) {
    .mobile-menu-open {
        @apply block;
    }

    .mobile-menu-closed {
        @apply hidden;
    }
}
