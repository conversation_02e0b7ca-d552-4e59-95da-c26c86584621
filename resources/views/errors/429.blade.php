@extends('layouts.app')

@section('title', 'Too Many Requests')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <div class="mx-auto h-24 w-24 text-yellow-500">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Too Many Requests
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                {{ $message ?? 'You have made too many requests. Please slow down.' }}
            </p>
            
            <div class="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-md">
                <p class="text-sm text-yellow-700">
                    <strong>Rate Limit Exceeded:</strong> Please wait before trying again.
                </p>
                @if(isset($retryAfter))
                    <p class="text-sm text-yellow-600 mt-1">
                        You can try again in <span id="countdown">{{ $retryAfter }}</span> seconds.
                    </p>
                @endif
            </div>
        </div>

        <div class="mt-8 space-y-4">
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    Rate limiting helps protect our system:
                </p>
                <ul class="mt-2 text-sm text-gray-600 list-disc list-inside space-y-1">
                    <li>Prevents system overload</li>
                    <li>Ensures fair usage for all users</li>
                    <li>Protects against automated attacks</li>
                    <li>Maintains system stability</li>
                </ul>
            </div>

            <div class="flex flex-col space-y-3">
                <button onclick="location.reload()" 
                        id="retryButton"
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-gray-400 cursor-not-allowed"
                        disabled>
                    <span id="retryText">Wait to Retry</span>
                </button>
                
                @auth
                    <a href="{{ route('dashboard') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Go to Dashboard
                    </a>
                @else
                    <a href="{{ route('login') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Go to Login
                    </a>
                @endauth
            </div>
        </div>

        <div class="mt-8 text-center">
            <p class="text-xs text-gray-400">
                @if(isset($type))
                    Rate limit type: {{ ucfirst(str_replace('_', ' ', $type)) }}
                @endif
            </p>
            <p class="text-xs text-gray-400 mt-1">
                If you need higher limits, please contact your administrator.
            </p>
        </div>
    </div>
</div>

@if(isset($retryAfter))
<script>
document.addEventListener('DOMContentLoaded', function() {
    let timeLeft = {{ $retryAfter }};
    const countdownElement = document.getElementById('countdown');
    const retryButton = document.getElementById('retryButton');
    const retryText = document.getElementById('retryText');
    
    const timer = setInterval(function() {
        timeLeft--;
        
        if (countdownElement) {
            countdownElement.textContent = timeLeft;
        }
        
        if (timeLeft <= 0) {
            clearInterval(timer);
            
            // Enable retry button
            retryButton.disabled = false;
            retryButton.classList.remove('bg-gray-400', 'cursor-not-allowed');
            retryButton.classList.add('bg-indigo-600', 'hover:bg-indigo-700', 'focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2', 'focus:ring-indigo-500');
            retryText.textContent = 'Try Again';
            
            if (countdownElement) {
                countdownElement.textContent = '0';
            }
        }
    }, 1000);
});
</script>
@endif
@endsection
