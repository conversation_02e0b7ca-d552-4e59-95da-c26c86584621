@extends('layouts.app')

@section('title', 'Access Forbidden')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <div class="mx-auto h-24 w-24 text-red-500">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m0 0v2m0-2h2m-2 0H10m2-5V9m0 0V7m0 2h2m-2 0H10m8-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Access Forbidden
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                You don't have permission to access this resource.
            </p>
            
            @if(isset($message))
                <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                    <p class="text-sm text-red-700">{{ $message }}</p>
                </div>
            @endif
        </div>

        <div class="mt-8 space-y-4">
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    This could be because:
                </p>
                <ul class="mt-2 text-sm text-gray-600 list-disc list-inside space-y-1">
                    <li>You don't have the required role or permissions</li>
                    <li>Your account has been deactivated</li>
                    <li>You're trying to access data from another branch</li>
                    <li>The resource requires higher authorization level</li>
                </ul>
            </div>

            <div class="flex flex-col space-y-3">
                @auth
                    <a href="{{ route('dashboard') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Go to Dashboard
                    </a>
                @else
                    <a href="{{ route('login') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Login
                    </a>
                @endauth
                
                <button onclick="history.back()" 
                        class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Go Back
                </button>
            </div>
        </div>

        <div class="mt-8 text-center">
            <p class="text-xs text-gray-400">
                If you believe this is an error, please contact your system administrator.
            </p>
        </div>
    </div>
</div>
@endsection
