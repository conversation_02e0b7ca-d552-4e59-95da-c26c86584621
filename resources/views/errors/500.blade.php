@extends('layouts.app')

@section('title', 'Server Error')

@section('content')
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8">
        <div class="text-center">
            <div class="mx-auto h-24 w-24 text-red-500">
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                </svg>
            </div>
            <h2 class="mt-6 text-3xl font-extrabold text-gray-900">
                Server Error
            </h2>
            <p class="mt-2 text-sm text-gray-600">
                Something went wrong on our end. We're working to fix it.
            </p>
            
            <div class="mt-4 p-4 bg-red-50 border border-red-200 rounded-md">
                <p class="text-sm text-red-700">
                    <strong>Error 500:</strong> Internal Server Error
                </p>
                @if(config('app.debug') && isset($exception))
                    <p class="text-xs text-red-600 mt-2">
                        {{ $exception->getMessage() }}
                    </p>
                @endif
            </div>
        </div>

        <div class="mt-8 space-y-4">
            <div class="text-center">
                <p class="text-sm text-gray-500">
                    What you can do:
                </p>
                <ul class="mt-2 text-sm text-gray-600 list-disc list-inside space-y-1">
                    <li>Try refreshing the page</li>
                    <li>Wait a few minutes and try again</li>
                    <li>Check if the issue persists</li>
                    <li>Contact support if the problem continues</li>
                </ul>
            </div>

            <div class="flex flex-col space-y-3">
                <button onclick="location.reload()" 
                        class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Try Again
                </button>
                
                @auth
                    <a href="{{ route('dashboard') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Go to Dashboard
                    </a>
                @else
                    <a href="{{ route('login') }}" 
                       class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                        Go to Login
                    </a>
                @endauth
                
                <button onclick="history.back()" 
                        class="w-full flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Go Back
                </button>
            </div>
        </div>

        <div class="mt-8 text-center">
            <p class="text-xs text-gray-400">
                Error ID: {{ uniqid() }} | Time: {{ now()->format('Y-m-d H:i:s') }}
            </p>
            <p class="text-xs text-gray-400 mt-1">
                Please include this information when contacting support.
            </p>
        </div>
    </div>
</div>
@endsection
