<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Branch Financial Report</title>
    <style>
        body {
            font-family: 'DejaVu Sans', sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 2px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .company-name {
            font-size: 24px;
            font-weight: bold;
            color: #2563eb;
            margin-bottom: 5px;
        }
        
        .report-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .report-info {
            font-size: 11px;
            color: #666;
        }
        
        .section {
            margin-bottom: 25px;
        }
        
        .section-title {
            font-size: 16px;
            font-weight: bold;
            color: #2563eb;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 5px;
            margin-bottom: 15px;
        }
        
        .summary-grid {
            display: table;
            width: 100%;
            margin-bottom: 20px;
        }
        
        .summary-row {
            display: table-row;
        }
        
        .summary-cell {
            display: table-cell;
            padding: 8px;
            border: 1px solid #e5e7eb;
            vertical-align: top;
        }
        
        .summary-cell.label {
            background-color: #f9fafb;
            font-weight: bold;
            width: 40%;
        }
        
        .summary-cell.value {
            text-align: right;
            width: 60%;
        }
        
        .amount {
            font-weight: bold;
        }
        
        .positive {
            color: #059669;
        }
        
        .negative {
            color: #dc2626;
        }
        
        .table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        .table th,
        .table td {
            border: 1px solid #e5e7eb;
            padding: 8px;
            text-align: left;
        }
        
        .table th {
            background-color: #f9fafb;
            font-weight: bold;
            font-size: 11px;
        }
        
        .table td {
            font-size: 10px;
        }
        
        .text-right {
            text-align: right;
        }
        
        .text-center {
            text-align: center;
        }
        
        .footer {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #e5e7eb;
            padding-top: 10px;
        }
        
        .page-break {
            page-break-before: always;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="company-name">Sonali Microfinance</div>
        <div class="report-title">Branch Financial Report</div>
        <div class="report-info">
            <strong>Branch:</strong> {{ $branch ? $branch->name : 'All Branches' }}<br>
            <strong>Period:</strong> {{ $period['start']->format('F j, Y') }} to {{ $period['end']->format('F j, Y') }}<br>
            <strong>Generated:</strong> {{ $report_date->format('F j, Y \a\t g:i A') }}
        </div>
    </div>

    <!-- Financial Summary Section -->
    <div class="section">
        <div class="section-title">Financial Summary</div>
        <div class="summary-grid">
            <div class="summary-row">
                <div class="summary-cell label">Loan Disbursements</div>
                <div class="summary-cell value amount">৳{{ number_format($financial_summary['disbursements'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Collections</div>
                <div class="summary-cell value amount positive">৳{{ number_format($financial_summary['collections'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Interest Income</div>
                <div class="summary-cell value amount positive">৳{{ number_format($financial_summary['interest_income'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Outstanding Loans</div>
                <div class="summary-cell value amount">৳{{ number_format($financial_summary['outstanding_loans'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Savings Deposits</div>
                <div class="summary-cell value amount positive">৳{{ number_format($financial_summary['savings_deposits'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Savings Withdrawals</div>
                <div class="summary-cell value amount negative">৳{{ number_format($financial_summary['savings_withdrawals'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Net Savings Flow</div>
                <div class="summary-cell value amount {{ $financial_summary['net_savings_flow'] >= 0 ? 'positive' : 'negative' }}">
                    ৳{{ number_format($financial_summary['net_savings_flow'], 2) }}
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label"><strong>Net Income</strong></div>
                <div class="summary-cell value amount {{ $financial_summary['net_income'] >= 0 ? 'positive' : 'negative' }}">
                    <strong>৳{{ number_format($financial_summary['net_income'], 2) }}</strong>
                </div>
            </div>
        </div>
    </div>

    <!-- Portfolio Analysis Section -->
    <div class="section">
        <div class="section-title">Loan Portfolio Analysis</div>
        <div class="summary-grid">
            <div class="summary-row">
                <div class="summary-cell label">Total Loans</div>
                <div class="summary-cell value">{{ number_format($portfolio_analysis['total_loans']) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Active Loans</div>
                <div class="summary-cell value">{{ number_format($portfolio_analysis['active_loans']) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Completed Loans</div>
                <div class="summary-cell value">{{ number_format($portfolio_analysis['completed_loans']) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Defaulted Loans</div>
                <div class="summary-cell value">{{ number_format($portfolio_analysis['defaulted_loans']) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Total Disbursed</div>
                <div class="summary-cell value amount">৳{{ number_format($portfolio_analysis['total_disbursed'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Total Outstanding</div>
                <div class="summary-cell value amount">৳{{ number_format($portfolio_analysis['total_outstanding'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Portfolio at Risk</div>
                <div class="summary-cell value amount negative">৳{{ number_format($portfolio_analysis['portfolio_at_risk'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">PAR Percentage</div>
                <div class="summary-cell value {{ $portfolio_analysis['par_percentage'] > 5 ? 'negative' : '' }}">
                    {{ number_format($portfolio_analysis['par_percentage'], 2) }}%
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Average Loan Size</div>
                <div class="summary-cell value amount">৳{{ number_format($portfolio_analysis['average_loan_size'], 2) }}</div>
            </div>
        </div>

        @if(count($portfolio_analysis['by_status']) > 0)
        <div style="margin-top: 20px;">
            <strong>Loans by Status:</strong>
            <table class="table">
                <thead>
                    <tr>
                        <th>Status</th>
                        <th class="text-right">Count</th>
                        <th class="text-right">Amount (৳)</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach($portfolio_analysis['by_status'] as $status => $data)
                    <tr>
                        <td>{{ ucfirst($status) }}</td>
                        <td class="text-right">{{ number_format($data['count']) }}</td>
                        <td class="text-right">{{ number_format($data['amount'], 2) }}</td>
                    </tr>
                    @endforeach
                </tbody>
            </table>
        </div>
        @endif
    </div>

    <!-- Collection Efficiency Section -->
    <div class="section">
        <div class="section-title">Collection Efficiency</div>
        <div class="summary-grid">
            <div class="summary-row">
                <div class="summary-cell label">Total Due</div>
                <div class="summary-cell value amount">৳{{ number_format($collection_efficiency['total_due'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Total Collected</div>
                <div class="summary-cell value amount positive">৳{{ number_format($collection_efficiency['total_collected'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Collection Rate</div>
                <div class="summary-cell value {{ $collection_efficiency['collection_rate'] >= 90 ? 'positive' : 'negative' }}">
                    {{ number_format($collection_efficiency['collection_rate'], 2) }}%
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Outstanding Amount</div>
                <div class="summary-cell value amount">৳{{ number_format($collection_efficiency['outstanding_amount'], 2) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">On-time Collections</div>
                <div class="summary-cell value">{{ number_format($collection_efficiency['on_time_collections']) }}</div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">On-time Rate</div>
                <div class="summary-cell value {{ $collection_efficiency['on_time_rate'] >= 80 ? 'positive' : 'negative' }}">
                    {{ number_format($collection_efficiency['on_time_rate'], 2) }}%
                </div>
            </div>
            <div class="summary-row">
                <div class="summary-cell label">Overdue Installments</div>
                <div class="summary-cell value {{ $collection_efficiency['overdue_installments'] > 0 ? 'negative' : '' }}">
                    {{ number_format($collection_efficiency['overdue_installments']) }}
                </div>
            </div>
        </div>
    </div>

    <div class="footer">
        <div>
            This report is confidential and intended for internal use only.<br>
            Generated by Sonali Microfinance Management System
        </div>
    </div>
</body>
</html>
