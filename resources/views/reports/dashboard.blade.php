@extends('layouts.app')

@section('title', 'Analytics Dashboard')

@section('content')
<div class="container-fluid px-4">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1 class="h3 mb-0 text-gray-800">Analytics Dashboard</h1>
        <div class="d-flex gap-2">
            @if(auth()->user()->role === 'admin')
            <select id="branchFilter" class="form-select form-select-sm">
                <option value="">All Branches</option>
                @foreach($branches as $branch)
                <option value="{{ $branch->id }}">{{ $branch->name }}</option>
                @endforeach
            </select>
            @endif
            <button id="refreshData" class="btn btn-primary btn-sm">
                <i class="fas fa-sync-alt"></i> Refresh
            </button>
        </div>
    </div>

    <!-- Summary Cards -->
    <div class="row mb-4" id="summaryCards">
        <!-- Cards will be populated by JavaScript -->
    </div>

    <!-- Charts Row 1 -->
    <div class="row mb-4">
        <!-- Loan Disbursement Trend -->
        <div class="col-xl-8 col-lg-7">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Loan Disbursement Trend</h6>
                    <div class="dropdown no-arrow">
                        <select id="disbursementPeriod" class="form-select form-select-sm">
                            <option value="6">Last 6 Months</option>
                            <option value="12" selected>Last 12 Months</option>
                            <option value="24">Last 24 Months</option>
                        </select>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="loanDisbursementChart" height="320"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Loan Status Distribution -->
        <div class="col-xl-4 col-lg-5">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Loan Status Distribution</h6>
                </div>
                <div class="card-body">
                    <div class="chart-pie pt-4 pb-2">
                        <canvas id="loanStatusChart"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 2 -->
    <div class="row mb-4">
        <!-- Collection Efficiency -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Collection Efficiency Trend</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="collectionEfficiencyChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Member Growth -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Member Growth</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="memberGrowthChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Charts Row 3 -->
    <div class="row mb-4">
        <!-- Portfolio at Risk -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Portfolio at Risk Trend</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="portfolioAtRiskChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- Savings Trend -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Savings Trend</h6>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="savingsTrendChart" height="300"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @if(auth()->user()->role === 'admin')
    <!-- Branch Comparison (Admin Only) -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">Branch Comparison</h6>
                    <div class="d-flex gap-2">
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="compareLoans" value="loans" checked>
                            <label class="form-check-label" for="compareLoans">Loans</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="compareMembers" value="members" checked>
                            <label class="form-check-label" for="compareMembers">Members</label>
                        </div>
                        <div class="form-check form-check-inline">
                            <input class="form-check-input" type="checkbox" id="compareCollections" value="collections" checked>
                            <label class="form-check-label" for="compareCollections">Collections</label>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="chart-area">
                        <canvas id="branchComparisonChart" height="400"></canvas>
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <!-- Recent Activities and Alerts -->
    <div class="row">
        <!-- Recent Activities -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">Recent Activities</h6>
                </div>
                <div class="card-body">
                    <div id="recentActivities">
                        <!-- Activities will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>

        <!-- System Alerts -->
        <div class="col-xl-6 col-lg-6">
            <div class="card shadow mb-4">
                <div class="card-header py-3">
                    <h6 class="m-0 font-weight-bold text-primary">System Alerts</h6>
                </div>
                <div class="card-body">
                    <div id="systemAlerts">
                        <!-- Alerts will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Loading Overlay -->
<div id="loadingOverlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.8); z-index: 9999; display: none !important;">
    <div class="text-center">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <div class="mt-2">Loading analytics data...</div>
    </div>
</div>
@endsection

@push('styles')
<style>
    .summary-card {
        border-left: 4px solid;
        transition: transform 0.2s;
    }
    
    .summary-card:hover {
        transform: translateY(-2px);
    }
    
    .summary-card.blue { border-left-color: #4e73df; }
    .summary-card.green { border-left-color: #1cc88a; }
    .summary-card.purple { border-left-color: #6f42c1; }
    .summary-card.orange { border-left-color: #f6c23e; }
    .summary-card.red { border-left-color: #e74a3b; }
    .summary-card.yellow { border-left-color: #f6c23e; }
    
    .activity-item {
        border-left: 3px solid #e3e6f0;
        padding-left: 15px;
        margin-bottom: 15px;
    }
    
    .activity-item.loan_disbursed { border-left-color: #1cc88a; }
    .activity-item.member_registered { border-left-color: #4e73df; }
    
    .alert-item {
        border-radius: 8px;
        margin-bottom: 10px;
    }
</style>
@endpush

@push('scripts')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    let charts = {};
    let currentBranch = null;
    
    // Initialize dashboard
    loadDashboardData();
    
    // Event listeners
    document.getElementById('branchFilter')?.addEventListener('change', function() {
        currentBranch = this.value || null;
        loadDashboardData();
    });
    
    document.getElementById('refreshData').addEventListener('click', function() {
        loadDashboardData();
    });
    
    document.getElementById('disbursementPeriod').addEventListener('change', function() {
        loadLoanDisbursementChart();
    });
    
    // Branch comparison checkboxes (admin only)
    @if(auth()->user()->role === 'admin')
    ['compareLoans', 'compareMembers', 'compareCollections'].forEach(id => {
        document.getElementById(id)?.addEventListener('change', function() {
            loadBranchComparisonChart();
        });
    });
    @endif
    
    // Auto-refresh every 5 minutes
    setInterval(function() {
        loadRealTimeUpdates();
    }, 300000);
    
    function showLoading() {
        document.getElementById('loadingOverlay').style.display = 'flex';
    }
    
    function hideLoading() {
        document.getElementById('loadingOverlay').style.display = 'none';
    }
    
    function loadDashboardData() {
        showLoading();
        
        const params = new URLSearchParams();
        if (currentBranch) {
            params.append('branch_id', currentBranch);
        }
        
        fetch(`{{ route('api.analytics.dashboard') }}?${params}`)
            .then(response => response.json())
            .then(data => {
                updateSummaryCards(data.summary_cards);
                updateCharts(data.charts);
                updateRecentActivities(data.recent_activities);
                updateSystemAlerts(data.alerts);
                hideLoading();
            })
            .catch(error => {
                console.error('Error loading dashboard data:', error);
                hideLoading();
            });
    }
    
    function loadRealTimeUpdates() {
        const params = new URLSearchParams();
        if (currentBranch) {
            params.append('branch_id', currentBranch);
        }
        
        fetch(`{{ route('api.analytics.real-time-updates') }}?${params}`)
            .then(response => response.json())
            .then(data => {
                updateSummaryCards(data.summary_cards);
                updateRecentActivities(data.recent_activities);
                updateSystemAlerts(data.alerts);
            })
            .catch(error => {
                console.error('Error loading real-time updates:', error);
            });
    }
    
    function updateSummaryCards(cards) {
        const container = document.getElementById('summaryCards');
        container.innerHTML = '';
        
        Object.entries(cards).forEach(([key, card]) => {
            const changeIcon = card.change_type === 'increase' ? 'fa-arrow-up' : 'fa-arrow-down';
            const changeColor = card.change_type === 'increase' ? 'text-success' : 'text-danger';
            
            container.innerHTML += `
                <div class="col-xl-2 col-md-4 col-sm-6 mb-4">
                    <div class="card summary-card ${card.color} shadow h-100 py-2">
                        <div class="card-body">
                            <div class="row no-gutters align-items-center">
                                <div class="col mr-2">
                                    <div class="text-xs font-weight-bold text-${card.color} text-uppercase mb-1">
                                        ${key.replace(/_/g, ' ')}
                                    </div>
                                    <div class="h5 mb-0 font-weight-bold text-gray-800">
                                        ${typeof card.value === 'number' ? card.value.toLocaleString() : card.value}
                                    </div>
                                    ${card.change ? `
                                    <div class="text-xs ${changeColor}">
                                        <i class="fas ${changeIcon}"></i> ${card.change}
                                    </div>
                                    ` : ''}
                                </div>
                                <div class="col-auto">
                                    <i class="fas fa-${card.icon} fa-2x text-gray-300"></i>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        });
    }
    
    function updateCharts(chartData) {
        // Destroy existing charts
        Object.values(charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        charts = {};
        
        // Create new charts
        createLoanDisbursementChart(chartData.loan_disbursement);
        createLoanStatusChart(chartData.loan_status_distribution);
        createCollectionEfficiencyChart(chartData.collection_efficiency);
        createMemberGrowthChart(chartData.member_growth);
        createPortfolioAtRiskChart(chartData.portfolio_at_risk);
        createSavingsTrendChart(chartData.savings_trend);
        
        @if(auth()->user()->role === 'admin')
        loadBranchComparisonChart();
        @endif
    }
    
    // Chart creation functions would go here...
    // (Implementation continues with specific chart creation functions)
});
</script>
@endpush
