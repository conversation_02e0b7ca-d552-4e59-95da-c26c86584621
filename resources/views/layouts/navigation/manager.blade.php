<!-- Manager Navigation -->
<div class="space-y-1">
    <!-- Dashboard -->
    <a href="{{ route('manager.dashboard') }}" class="nav-link {{ request()->routeIs('manager.dashboard') ? 'active' : '' }}">
        <i class="fas fa-tachometer-alt"></i>
        <span>Dashboard</span>
    </a>

    <!-- Branch Overview -->
    <a href="#" class="nav-link">
        <i class="fas fa-building"></i>
        <span>Branch Overview</span>
    </a>

    <!-- Member Management -->
    <div class="nav-group">
        <div class="nav-group-header">
            <i class="fas fa-users"></i>
            <span>Member Management</span>
        </div>
        <div class="nav-group-items">
            <a href="#" class="nav-sublink">
                <i class="fas fa-user-plus"></i>
                <span>Add Member</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-list"></i>
                <span>All Members</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-user-check"></i>
                <span>Active Members</span>
            </a>
        </div>
    </div>

    <!-- Loan Management -->
    <div class="nav-group">
        <div class="nav-group-header">
            <i class="fas fa-hand-holding-usd"></i>
            <span>Loan Management</span>
        </div>
        <div class="nav-group-items">
            <a href="#" class="nav-sublink">
                <i class="fas fa-file-alt"></i>
                <span>Pending Applications</span>
                <span class="ml-auto bg-warning-100 text-warning-800 text-xs px-2 py-1 rounded-full">5</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-check-circle"></i>
                <span>Approve/Reject</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-money-check-alt"></i>
                <span>Active Loans</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Overdue Loans</span>
                <span class="ml-auto bg-danger-100 text-danger-800 text-xs px-2 py-1 rounded-full">3</span>
            </a>
        </div>
    </div>

    <!-- Collections -->
    <div class="nav-group">
        <div class="nav-group-header">
            <i class="fas fa-coins"></i>
            <span>Collections</span>
        </div>
        <div class="nav-group-items">
            <a href="#" class="nav-sublink">
                <i class="fas fa-calendar-day"></i>
                <span>Today's Collections</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-calendar-week"></i>
                <span>Weekly Collections</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-calendar-alt"></i>
                <span>Monthly Collections</span>
            </a>
        </div>
    </div>

    <!-- Savings Management -->
    <div class="nav-group">
        <div class="nav-group-header">
            <i class="fas fa-piggy-bank"></i>
            <span>Savings Management</span>
        </div>
        <div class="nav-group-items">
            <a href="#" class="nav-sublink">
                <i class="fas fa-plus-circle"></i>
                <span>New Savings Account</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-list"></i>
                <span>All Accounts</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-exchange-alt"></i>
                <span>Transactions</span>
            </a>
        </div>
    </div>

    <!-- Staff Management -->
    <div class="nav-group">
        <div class="nav-group-header">
            <i class="fas fa-user-friends"></i>
            <span>Staff Management</span>
        </div>
        <div class="nav-group-items">
            <a href="#" class="nav-sublink">
                <i class="fas fa-user-plus"></i>
                <span>Add Field Officer</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-users"></i>
                <span>Field Officers</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-chart-line"></i>
                <span>Performance</span>
            </a>
        </div>
    </div>

    <!-- Branch Transactions -->
    <a href="#" class="nav-link">
        <i class="fas fa-receipt"></i>
        <span>Branch Transactions</span>
    </a>

    <!-- Reports -->
    <div class="nav-group">
        <div class="nav-group-header">
            <i class="fas fa-chart-bar"></i>
            <span>Reports</span>
        </div>
        <div class="nav-group-items">
            <a href="#" class="nav-sublink">
                <i class="fas fa-file-alt"></i>
                <span>Daily Report</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-calendar-week"></i>
                <span>Weekly Report</span>
            </a>
            <a href="#" class="nav-sublink">
                <i class="fas fa-calendar-alt"></i>
                <span>Monthly Report</span>
            </a>
        </div>
    </div>
</div>

<style>
.nav-link {
    @apply flex items-center px-3 py-2 text-sm font-medium rounded-md text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200;
}

.nav-link.active {
    @apply bg-primary-50 text-primary-700 border-r-2 border-primary-500;
}

.nav-link i {
    @apply mr-3 text-gray-400 w-5 h-5 flex-shrink-0;
}

.nav-link.active i {
    @apply text-primary-500;
}

.nav-group {
    @apply space-y-1;
}

.nav-group-header {
    @apply flex items-center px-3 py-2 text-sm font-medium text-gray-500 cursor-pointer hover:text-gray-700 transition-colors duration-200;
}

.nav-group-header i {
    @apply mr-3 text-gray-400 w-5 h-5 flex-shrink-0;
}

.nav-group-items {
    @apply ml-6 space-y-1;
}

.nav-sublink {
    @apply flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md transition-colors duration-200;
}

.nav-sublink i {
    @apply mr-3 text-gray-400 w-4 h-4 flex-shrink-0;
}
</style>
