<!-- Modern Admin Navigation -->
<div class="space-y-2">
    <!-- Dashboard -->
    <a href="{{ route('admin.dashboard') }}" class="nav-link-modern {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
        <i class="fas fa-tachometer-alt"></i>
        <span>Dashboard</span>
    </a>

    <!-- User Management -->
    <div class="nav-group-modern">
        <div class="nav-group-header-modern">
            <i class="fas fa-users"></i>
            <span>User Management</span>
            <i class="fas fa-chevron-down ml-auto transform transition-transform duration-200"></i>
        </div>
        <div class="nav-group-items-modern">
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-user-shield"></i>
                <span>Administrators</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-user-tie"></i>
                <span>Branch Managers</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-user-friends"></i>
                <span>Field Officers</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-users"></i>
                <span>Members</span>
            </a>
        </div>
    </div>

    <!-- Branch Management -->
    <div class="nav-group-modern">
        <div class="nav-group-header-modern">
            <i class="fas fa-building"></i>
            <span>Branch Management</span>
            <i class="fas fa-chevron-down ml-auto transform transition-transform duration-200"></i>
        </div>
        <div class="nav-group-items-modern">
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-plus-circle"></i>
                <span>Add Branch</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-list"></i>
                <span>All Branches</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-chart-line"></i>
                <span>Branch Performance</span>
            </a>
        </div>
    </div>

    <!-- Loan Management -->
    <div class="nav-group-modern">
        <div class="nav-group-header-modern">
            <i class="fas fa-hand-holding-usd"></i>
            <span>Loan Management</span>
            <i class="fas fa-chevron-down ml-auto transform transition-transform duration-200"></i>
        </div>
        <div class="nav-group-items-modern">
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-file-alt"></i>
                <span>Loan Applications</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-money-check-alt"></i>
                <span>Active Loans</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-exclamation-triangle"></i>
                <span>Overdue Loans</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-calendar-alt"></i>
                <span>Installment Schedule</span>
            </a>
        </div>
    </div>

    <!-- Financial Management -->
    <div class="nav-group-modern">
        <div class="nav-group-header-modern">
            <i class="fas fa-chart-pie"></i>
            <span>Financial Management</span>
            <i class="fas fa-chevron-down ml-auto transform transition-transform duration-200"></i>
        </div>
        <div class="nav-group-items-modern">
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-piggy-bank"></i>
                <span>Savings Accounts</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-exchange-alt"></i>
                <span>Transactions</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-file-invoice-dollar"></i>
                <span>Financial Reports</span>
            </a>
        </div>
    </div>

    <!-- System Management -->
    <div class="nav-group-modern">
        <div class="nav-group-header-modern">
            <i class="fas fa-cogs"></i>
            <span>System Management</span>
            <i class="fas fa-chevron-down ml-auto transform transition-transform duration-200"></i>
        </div>
        <div class="nav-group-items-modern">
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-ad"></i>
                <span>Advertisements</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-database"></i>
                <span>System Backup</span>
            </a>
            <a href="#" class="nav-sublink-modern">
                <i class="fas fa-shield-alt"></i>
                <span>Security Settings</span>
            </a>
        </div>
    </div>

    <!-- Reports -->
    <a href="#" class="nav-link-modern">
        <i class="fas fa-chart-bar"></i>
        <span>Reports & Analytics</span>
    </a>

    <!-- Settings -->
    <a href="#" class="nav-link-modern">
        <i class="fas fa-cog"></i>
        <span>System Settings</span>
    </a>
</div>

<style>
/* Modern Navigation Group Styles */
.nav-group-modern {
    @apply space-y-1 mb-4;
}

.nav-group-header-modern {
    @apply flex items-center px-4 py-3 text-sm font-semibold text-gray-600 cursor-pointer hover:text-gray-800 hover:bg-gray-50 rounded-xl transition-all duration-300 group;
}

.nav-group-header-modern i:first-child {
    @apply mr-3 text-gray-400 w-5 h-5 flex-shrink-0 group-hover:text-primary-500 transition-colors duration-300;
}

.nav-group-items-modern {
    @apply ml-4 space-y-1 border-l-2 border-gray-100 pl-4;
}

.nav-sublink-modern {
    @apply flex items-center px-3 py-2 text-sm text-gray-600 hover:text-gray-900 hover:bg-gradient-to-r hover:from-gray-50 hover:to-gray-100 rounded-lg transition-all duration-300 group;
}

.nav-sublink-modern i {
    @apply mr-3 text-gray-400 w-4 h-4 flex-shrink-0 group-hover:text-primary-500 transition-colors duration-300;
}

.nav-sublink-modern:hover {
    @apply transform translate-x-1;
}

/* Add collapsible functionality */
.nav-group-modern .nav-group-items-modern {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.nav-group-modern.expanded .nav-group-items-modern {
    max-height: 500px;
}

.nav-group-modern.expanded .nav-group-header-modern i:last-child {
    transform: rotate(180deg);
}
</style>

<script>
// Add collapsible navigation functionality
document.addEventListener('DOMContentLoaded', function() {
    const navGroups = document.querySelectorAll('.nav-group-modern');

    navGroups.forEach(group => {
        const header = group.querySelector('.nav-group-header-modern');

        // Expand first group by default
        if (group === navGroups[0]) {
            group.classList.add('expanded');
        }

        header.addEventListener('click', function() {
            // Close other groups
            navGroups.forEach(otherGroup => {
                if (otherGroup !== group) {
                    otherGroup.classList.remove('expanded');
                }
            });

            // Toggle current group
            group.classList.toggle('expanded');
        });
    });
});
</script>
