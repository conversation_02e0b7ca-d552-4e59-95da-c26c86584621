<!-- Mobile Navigation -->
<div class="space-y-1">
    @if(auth()->user()->isAdmin())
        <!-- Admin Mobile Menu -->
        <a href="{{ route('admin.dashboard') }}" class="mobile-nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-users"></i>
            <span>User Management</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-building"></i>
            <span>Branches</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-hand-holding-usd"></i>
            <span>Loans</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-chart-bar"></i>
            <span>Reports</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-cog"></i>
            <span>Settings</span>
        </a>

    @elseif(auth()->user()->isManager())
        <!-- Manager Mobile Menu -->
        <a href="{{ route('manager.dashboard') }}" class="mobile-nav-link {{ request()->routeIs('manager.dashboard') ? 'active' : '' }}">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-building"></i>
            <span>Branch Overview</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-users"></i>
            <span>Members</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-file-alt"></i>
            <span>Loan Applications</span>
            <span class="ml-auto bg-warning-100 text-warning-800 text-xs px-2 py-1 rounded-full">5</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-coins"></i>
            <span>Collections</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-piggy-bank"></i>
            <span>Savings</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-user-friends"></i>
            <span>Staff</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-chart-bar"></i>
            <span>Reports</span>
        </a>

    @elseif(auth()->user()->isFieldOfficer())
        <!-- Field Officer Mobile Menu -->
        <a href="{{ route('field-officer.dashboard') }}" class="mobile-nav-link {{ request()->routeIs('field-officer.dashboard') ? 'active' : '' }}">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-users"></i>
            <span>My Members</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-file-alt"></i>
            <span>Loan Applications</span>
            <span class="ml-auto bg-warning-100 text-warning-800 text-xs px-2 py-1 rounded-full">3</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-calendar-day"></i>
            <span>Today's Due</span>
            <span class="ml-auto bg-accent-100 text-accent-800 text-xs px-2 py-1 rounded-full">12</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-exclamation-triangle"></i>
            <span>Overdue</span>
            <span class="ml-auto bg-danger-100 text-danger-800 text-xs px-2 py-1 rounded-full">5</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-piggy-bank"></i>
            <span>Savings</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-route"></i>
            <span>Today's Route</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-chart-line"></i>
            <span>Performance</span>
        </a>

    @elseif(auth()->user()->isMember())
        <!-- Member Mobile Menu -->
        <a href="{{ route('member.dashboard') }}" class="mobile-nav-link {{ request()->routeIs('member.dashboard') ? 'active' : '' }}">
            <i class="fas fa-tachometer-alt"></i>
            <span>Dashboard</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-user"></i>
            <span>My Profile</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-plus-circle"></i>
            <span>Apply for Loan</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-file-alt"></i>
            <span>My Applications</span>
            <span class="ml-auto bg-secondary-100 text-secondary-800 text-xs px-2 py-1 rounded-full">2</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-calendar-day"></i>
            <span>Next Due</span>
            <span class="ml-auto bg-warning-100 text-warning-800 text-xs px-2 py-1 rounded-full">৳5,000</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-wallet"></i>
            <span>My Savings</span>
            <span class="ml-auto bg-success-100 text-success-800 text-xs px-2 py-1 rounded-full">৳25,000</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-graduation-cap"></i>
            <span>Financial Education</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-headset"></i>
            <span>Support</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-bell"></i>
            <span>Notifications</span>
            <span class="ml-auto bg-danger-100 text-danger-800 text-xs px-2 py-1 rounded-full">3</span>
        </a>
    @endif

    <!-- Common Mobile Menu Items -->
    <div class="border-t border-gray-200 pt-3 mt-3">
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-user-cog"></i>
            <span>Profile Settings</span>
        </a>
        <a href="#" class="mobile-nav-link">
            <i class="fas fa-question-circle"></i>
            <span>Help & Support</span>
        </a>
        <form method="POST" action="{{ route('logout') }}" class="block">
            @csrf
            <button type="submit" class="mobile-nav-link w-full text-left">
                <i class="fas fa-sign-out-alt"></i>
                <span>Sign Out</span>
            </button>
        </form>
    </div>
</div>

<style>
.mobile-nav-link {
    @apply flex items-center px-3 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 transition-colors duration-200 border-b border-gray-100;
}

.mobile-nav-link.active {
    @apply bg-primary-50 text-primary-700;
}

.mobile-nav-link i {
    @apply mr-3 text-gray-400 w-5 h-5 flex-shrink-0;
}

.mobile-nav-link.active i {
    @apply text-primary-500;
}
</style>
