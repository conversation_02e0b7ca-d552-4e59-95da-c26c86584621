<!doctype html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- CSRF <PERSON>ken -->
    <meta name="csrf-token" content="{{ csrf_token() }}">

    <title>@yield('title', 'Sonali Microfinance - Empowering Communities')</title>

    <!-- Fonts -->
    <link rel="dns-prefetch" href="//fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=Inter:400,500,600,700" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Scripts -->
    @vite(['resources/css/app.css', 'resources/js/app.js'])

    @stack('styles')
</head>
<body class="font-sans antialiased bg-gray-50">
    <div id="app" class="min-h-screen flex flex-col">
        @auth
            @include('layouts.header')

            <div class="flex flex-1">
                @include('layouts.navigation')

                <!-- Main Content -->
                <main class="flex-1 p-6 lg:p-8 overflow-y-auto">
                    <!-- Flash Messages -->
                    @if(session('success'))
                        <div class="mb-6 bg-success-50 border border-success-200 text-success-800 px-4 py-3 rounded-lg shadow-sm">
                            <div class="flex items-center">
                                <i class="fas fa-check-circle mr-2"></i>
                                {{ session('success') }}
                            </div>
                        </div>
                    @endif

                    @if(session('error'))
                        <div class="mb-6 bg-danger-50 border border-danger-200 text-danger-800 px-4 py-3 rounded-lg shadow-sm">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-circle mr-2"></i>
                                {{ session('error') }}
                            </div>
                        </div>
                    @endif

                    @if(session('warning'))
                        <div class="mb-6 bg-warning-50 border border-warning-200 text-warning-800 px-4 py-3 rounded-lg shadow-sm">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle mr-2"></i>
                                {{ session('warning') }}
                            </div>
                        </div>
                    @endif

                    @if(session('info'))
                        <div class="mb-6 bg-secondary-50 border border-secondary-200 text-secondary-800 px-4 py-3 rounded-lg shadow-sm">
                            <div class="flex items-center">
                                <i class="fas fa-info-circle mr-2"></i>
                                {{ session('info') }}
                            </div>
                        </div>
                    @endif

                    @yield('content')
                </main>
            </div>

            @include('layouts.footer')
        @else
            <!-- Guest Layout -->
            <main class="flex-1">
                @yield('content')
            </main>
        @endauth
    </div>

    @stack('scripts')
</body>
</html>
