@extends('layouts.app')

@section('content')
<div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Loan Calculator</h1>
                    <p class="text-gray-600">Calculate loan repayment details and compare scenarios</p>
                </div>
                <a href="{{ route('field-officer.dashboard') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Back to Dashboard
                </a>
            </div>
        </div>

        <!-- Calculator Tabs -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button class="calc-tab border-indigo-500 text-indigo-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="basic">
                        Basic Calculator
                    </button>
                    <button class="calc-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="compare">
                        Compare Scenarios
                    </button>
                    <button class="calc-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="eligibility">
                        Eligibility Calculator
                    </button>
                </nav>
            </div>

            <!-- Basic Calculator -->
            <div id="basic-tab" class="calc-content">
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Input Form -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Loan Parameters</h3>
                            <form id="basic-calculator-form" class="space-y-4">
                                <div>
                                    <label for="loan_amount" class="block text-sm font-medium text-gray-700">Loan Amount (৳) *</label>
                                    <input type="number" id="loan_amount" min="1000" max="1000000" step="100" value="50000" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label for="interest_rate" class="block text-sm font-medium text-gray-700">Interest Rate (%) *</label>
                                    <input type="number" id="interest_rate" min="0" max="50" step="0.1" value="15" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label for="loan_duration_months" class="block text-sm font-medium text-gray-700">Duration (Months) *</label>
                                    <input type="number" id="loan_duration_months" min="1" max="60" value="12" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label for="repayment_method" class="block text-sm font-medium text-gray-700">Repayment Method *</label>
                                    <select id="repayment_method" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="monthly">Monthly</option>
                                        <option value="weekly">Weekly</option>
                                    </select>
                                </div>

                                <div>
                                    <label for="advance_payment" class="block text-sm font-medium text-gray-700">Advance Payment (৳)</label>
                                    <input type="number" id="advance_payment" min="0" step="100" value="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <p class="mt-1 text-xs text-gray-500">Maximum 20% of loan amount</p>
                                </div>

                                <div class="flex space-x-3">
                                    <button type="button" id="calculate-basic" class="flex-1 inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                            <path d="M9 12a1 1 0 102 0V8a1 1 0 10-2 0v4zm0-6a1 1 0 102 0 1 1 0 00-2 0z"></path>
                                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"></path>
                                        </svg>
                                        Calculate
                                    </button>
                                    <button type="button" id="load-preset" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                        Load Preset
                                    </button>
                                </div>
                            </form>

                            <!-- Preset Options -->
                            <div id="preset-options" class="hidden mt-4 p-4 bg-gray-50 rounded-md">
                                <h4 class="text-sm font-medium text-gray-900 mb-2">Quick Presets</h4>
                                <div class="grid grid-cols-2 gap-2">
                                    <button class="preset-btn text-xs px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50" data-preset="micro_loan">
                                        Micro Loan
                                    </button>
                                    <button class="preset-btn text-xs px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50" data-preset="small_business">
                                        Small Business
                                    </button>
                                    <button class="preset-btn text-xs px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50" data-preset="agriculture">
                                        Agriculture
                                    </button>
                                    <button class="preset-btn text-xs px-3 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50" data-preset="emergency">
                                        Emergency
                                    </button>
                                </div>
                            </div>
                        </div>

                        <!-- Results -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Calculation Results</h3>
                            <div id="basic-results" class="hidden">
                                <!-- Summary Cards -->
                                <div class="grid grid-cols-2 gap-4 mb-6">
                                    <div class="bg-blue-50 p-4 rounded-md">
                                        <div class="text-sm text-blue-600">Loan Amount</div>
                                        <div id="result-loan-amount" class="text-xl font-bold text-blue-900">-</div>
                                    </div>
                                    <div class="bg-green-50 p-4 rounded-md">
                                        <div class="text-sm text-green-600">Total Interest</div>
                                        <div id="result-total-interest" class="text-xl font-bold text-green-900">-</div>
                                    </div>
                                    <div class="bg-purple-50 p-4 rounded-md">
                                        <div class="text-sm text-purple-600">Total Repayment</div>
                                        <div id="result-total-repayment" class="text-xl font-bold text-purple-900">-</div>
                                    </div>
                                    <div class="bg-orange-50 p-4 rounded-md">
                                        <div class="text-sm text-orange-600">Per Installment</div>
                                        <div id="result-installment-amount" class="text-xl font-bold text-orange-900">-</div>
                                    </div>
                                </div>

                                <!-- Detailed Breakdown -->
                                <div class="bg-gray-50 p-4 rounded-md mb-4">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Detailed Breakdown</h4>
                                    <div class="space-y-2 text-sm">
                                        <div class="flex justify-between">
                                            <span>Principal Amount:</span>
                                            <span id="breakdown-principal" class="font-medium">-</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Total Interest:</span>
                                            <span id="breakdown-interest" class="font-medium">-</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Advance Payment:</span>
                                            <span id="breakdown-advance" class="font-medium">-</span>
                                        </div>
                                        <hr class="border-gray-300">
                                        <div class="flex justify-between font-medium">
                                            <span>Net Payable:</span>
                                            <span id="breakdown-net-payable">-</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Number of Installments:</span>
                                            <span id="breakdown-installments">-</span>
                                        </div>
                                        <div class="flex justify-between">
                                            <span>Effective Interest Rate:</span>
                                            <span id="breakdown-effective-rate">-</span>
                                        </div>
                                    </div>
                                </div>

                                <!-- Installment Schedule Preview -->
                                <div id="installment-schedule" class="hidden">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Installment Schedule (First 12)</h4>
                                    <div class="max-h-64 overflow-y-auto">
                                        <table class="min-w-full text-xs">
                                            <thead class="bg-gray-100">
                                                <tr>
                                                    <th class="px-2 py-1 text-left">#</th>
                                                    <th class="px-2 py-1 text-left">Due Date</th>
                                                    <th class="px-2 py-1 text-right">Amount</th>
                                                </tr>
                                            </thead>
                                            <tbody id="schedule-body">
                                                <!-- Schedule rows will be populated by JavaScript -->
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>

                            <div id="basic-loading" class="hidden text-center py-8">
                                <div class="inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-indigo-600"></div>
                                <p class="mt-2 text-sm text-gray-500">Calculating...</p>
                            </div>

                            <div id="basic-placeholder" class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
                                </svg>
                                <p class="mt-2 text-sm">Enter loan parameters and click Calculate to see results</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Compare Scenarios -->
            <div id="compare-tab" class="calc-content hidden">
                <div class="px-6 py-6">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Compare Loan Scenarios</h3>
                    <p class="text-sm text-gray-600 mb-6">Compare up to 4 different loan scenarios to find the best option.</p>

                    <div id="scenarios-container" class="space-y-6">
                        <!-- Scenario forms will be added dynamically -->
                    </div>

                    <div class="flex justify-between items-center mt-6">
                        <button type="button" id="add-scenario" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"></path>
                            </svg>
                            Add Scenario
                        </button>
                        <button type="button" id="compare-scenarios" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                            Compare All
                        </button>
                    </div>

                    <div id="comparison-results" class="hidden mt-8">
                        <!-- Comparison results will be populated by JavaScript -->
                    </div>
                </div>
            </div>

            <!-- Eligibility Calculator -->
            <div id="eligibility-tab" class="calc-content hidden">
                <div class="px-6 py-6">
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                        <!-- Input Form -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Member Financial Information</h3>
                            <form id="eligibility-form" class="space-y-4">
                                <div>
                                    <label for="monthly_income" class="block text-sm font-medium text-gray-700">Monthly Income (৳) *</label>
                                    <input type="number" id="monthly_income" min="1000" step="100" value="25000" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label for="existing_obligations" class="block text-sm font-medium text-gray-700">Existing Monthly Obligations (৳)</label>
                                    <input type="number" id="existing_obligations" min="0" step="100" value="0" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                    <p class="mt-1 text-xs text-gray-500">Other loan payments, rent, etc.</p>
                                </div>

                                <div>
                                    <label for="eligibility_interest_rate" class="block text-sm font-medium text-gray-700">Interest Rate (%) *</label>
                                    <input type="number" id="eligibility_interest_rate" min="0" max="50" step="0.1" value="15" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label for="eligibility_duration" class="block text-sm font-medium text-gray-700">Loan Duration (Months) *</label>
                                    <input type="number" id="eligibility_duration" min="1" max="60" value="12" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                </div>

                                <div>
                                    <label for="eligibility_repayment_method" class="block text-sm font-medium text-gray-700">Repayment Method *</label>
                                    <select id="eligibility_repayment_method" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                        <option value="monthly">Monthly</option>
                                        <option value="weekly">Weekly</option>
                                    </select>
                                </div>

                                <button type="button" id="calculate-eligibility" class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    Calculate Maximum Loan
                                </button>
                            </form>
                        </div>

                        <!-- Results -->
                        <div>
                            <h3 class="text-lg font-medium text-gray-900 mb-4">Eligibility Results</h3>
                            <div id="eligibility-results" class="hidden">
                                <div class="space-y-4">
                                    <div class="bg-green-50 p-4 rounded-md">
                                        <div class="text-sm text-green-600">Maximum Loan Amount</div>
                                        <div id="max-loan-amount" class="text-2xl font-bold text-green-900">-</div>
                                    </div>

                                    <div class="bg-blue-50 p-4 rounded-md">
                                        <div class="text-sm text-blue-600">Recommended Loan Amount</div>
                                        <div id="recommended-loan-amount" class="text-xl font-bold text-blue-900">-</div>
                                        <div class="text-xs text-blue-600 mt-1">80% of maximum for safety</div>
                                    </div>

                                    <div class="bg-gray-50 p-4 rounded-md">
                                        <h4 class="text-sm font-medium text-gray-900 mb-2">Financial Analysis</h4>
                                        <div class="space-y-2 text-sm">
                                            <div class="flex justify-between">
                                                <span>Monthly Income:</span>
                                                <span id="analysis-income" class="font-medium">-</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>Existing Obligations:</span>
                                                <span id="analysis-obligations" class="font-medium">-</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>Net Income:</span>
                                                <span id="analysis-net-income" class="font-medium">-</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>Max Repayment Capacity:</span>
                                                <span id="analysis-max-repayment" class="font-medium">-</span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span>Debt-to-Income Ratio:</span>
                                                <span id="analysis-debt-ratio" class="font-medium">-</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div id="eligibility-placeholder" class="text-center py-8 text-gray-500">
                                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                                <p class="mt-2 text-sm">Enter member's financial information to calculate loan eligibility</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabs = document.querySelectorAll('.calc-tab');
    const contents = document.querySelectorAll('.calc-content');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // Remove active classes from all tabs
            tabs.forEach(t => {
                t.classList.remove('border-indigo-500', 'text-indigo-600');
                t.classList.add('border-transparent', 'text-gray-500');
            });

            // Add active classes to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-indigo-500', 'text-indigo-600');

            // Hide all content
            contents.forEach(content => {
                content.classList.add('hidden');
            });

            // Show target content
            document.getElementById(targetTab + '-tab').classList.remove('hidden');
        });
    });

    // Basic calculator
    document.getElementById('calculate-basic').addEventListener('click', calculateBasic);
    document.getElementById('load-preset').addEventListener('click', togglePresets);

    // Preset buttons
    document.querySelectorAll('.preset-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            loadPreset(this.getAttribute('data-preset'));
        });
    });

    // Advance payment validation
    document.getElementById('loan_amount').addEventListener('input', function() {
        const loanAmount = parseFloat(this.value) || 0;
        const maxAdvance = loanAmount * 0.2;
        const advanceInput = document.getElementById('advance_payment');
        advanceInput.max = maxAdvance;

        if (parseFloat(advanceInput.value) > maxAdvance) {
            advanceInput.value = maxAdvance;
        }
    });

    // Eligibility calculator
    document.getElementById('calculate-eligibility').addEventListener('click', calculateEligibility);

    // Comparison functionality
    document.getElementById('add-scenario').addEventListener('click', addScenario);
    document.getElementById('compare-scenarios').addEventListener('click', compareScenarios);

    // Initialize with 2 scenarios
    addScenario();
    addScenario();
});

function calculateBasic() {
    const form = document.getElementById('basic-calculator-form');
    const formData = new FormData(form);

    const data = {
        loan_amount: parseFloat(formData.get('loan_amount')) || 0,
        interest_rate: parseFloat(formData.get('interest_rate')) || 0,
        loan_duration_months: parseInt(formData.get('loan_duration_months')) || 0,
        repayment_method: formData.get('repayment_method'),
        advance_payment: parseFloat(formData.get('advance_payment')) || 0
    };

    if (data.loan_amount === 0 || data.interest_rate === 0 || data.loan_duration_months === 0) {
        alert('Please fill in all required fields.');
        return;
    }

    showLoading('basic');

    fetch('/field-officer/loan-calculator/calculate', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        hideLoading('basic');
        if (data.error) {
            alert(data.error);
            return;
        }
        displayBasicResults(data);
    })
    .catch(error => {
        hideLoading('basic');
        console.error('Error:', error);
        alert('Error calculating loan. Please try again.');
    });
}

function displayBasicResults(data) {
    // Update summary cards
    document.getElementById('result-loan-amount').textContent = '৳' + data.loan_amount;
    document.getElementById('result-total-interest').textContent = '৳' + data.total_interest;
    document.getElementById('result-total-repayment').textContent = '৳' + data.total_repayment;
    document.getElementById('result-installment-amount').textContent = '৳' + data.installment_amount;

    // Update detailed breakdown
    document.getElementById('breakdown-principal').textContent = '৳' + data.loan_amount;
    document.getElementById('breakdown-interest').textContent = '৳' + data.total_interest;
    document.getElementById('breakdown-advance').textContent = '৳' + data.advance_payment;
    document.getElementById('breakdown-net-payable').textContent = '৳' + data.net_repayment;
    document.getElementById('breakdown-installments').textContent = data.total_installments;
    document.getElementById('breakdown-effective-rate').textContent = data.effective_interest_rate + '%';

    // Update installment schedule
    if (data.installment_schedule) {
        const tbody = document.getElementById('schedule-body');
        tbody.innerHTML = '';

        data.installment_schedule.forEach(installment => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td class="px-2 py-1">${installment.installment_no}</td>
                <td class="px-2 py-1">${installment.formatted_date}</td>
                <td class="px-2 py-1 text-right">৳${installment.amount}</td>
            `;
            tbody.appendChild(row);
        });

        document.getElementById('installment-schedule').classList.remove('hidden');
    }

    // Show results
    document.getElementById('basic-placeholder').classList.add('hidden');
    document.getElementById('basic-results').classList.remove('hidden');
}

function togglePresets() {
    const presetOptions = document.getElementById('preset-options');
    presetOptions.classList.toggle('hidden');
}

function loadPreset(presetName) {
    fetch('/field-officer/loan-calculator/presets')
        .then(response => response.json())
        .then(presets => {
            const preset = presets[presetName];
            if (preset) {
                document.getElementById('loan_amount').value = preset.loan_amount;
                document.getElementById('interest_rate').value = preset.interest_rate;
                document.getElementById('loan_duration_months').value = preset.loan_duration_months;
                document.getElementById('repayment_method').value = preset.repayment_method;
                document.getElementById('advance_payment').value = preset.advance_payment;

                // Hide preset options
                document.getElementById('preset-options').classList.add('hidden');
            }
        })
        .catch(error => console.error('Error loading presets:', error));
}

function calculateEligibility() {
    const data = {
        monthly_income: parseFloat(document.getElementById('monthly_income').value) || 0,
        existing_obligations: parseFloat(document.getElementById('existing_obligations').value) || 0,
        interest_rate: parseFloat(document.getElementById('eligibility_interest_rate').value) || 0,
        loan_duration_months: parseInt(document.getElementById('eligibility_duration').value) || 0,
        repayment_method: document.getElementById('eligibility_repayment_method').value
    };

    if (data.monthly_income === 0 || data.interest_rate === 0 || data.loan_duration_months === 0) {
        alert('Please fill in all required fields.');
        return;
    }

    fetch('/field-officer/loan-calculator/max-loan', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        displayEligibilityResults(data);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error calculating eligibility. Please try again.');
    });
}

function displayEligibilityResults(data) {
    document.getElementById('max-loan-amount').textContent = '৳' + data.max_loan_amount;
    document.getElementById('recommended-loan-amount').textContent = '৳' + data.recommended_loan_amount;

    document.getElementById('analysis-income').textContent = '৳' + data.monthly_income;
    document.getElementById('analysis-obligations').textContent = '৳' + data.existing_obligations;
    document.getElementById('analysis-net-income').textContent = '৳' + data.net_income;
    document.getElementById('analysis-max-repayment').textContent = '৳' + data.max_repayment_capacity;
    document.getElementById('analysis-debt-ratio').textContent = data.debt_to_income_ratio + '%';

    document.getElementById('eligibility-placeholder').classList.add('hidden');
    document.getElementById('eligibility-results').classList.remove('hidden');
}

let scenarioCount = 0;

function addScenario() {
    if (scenarioCount >= 4) {
        alert('Maximum 4 scenarios allowed');
        return;
    }

    scenarioCount++;
    const container = document.getElementById('scenarios-container');

    const scenarioDiv = document.createElement('div');
    scenarioDiv.className = 'border border-gray-200 rounded-lg p-4';
    scenarioDiv.innerHTML = `
        <div class="flex justify-between items-center mb-4">
            <h4 class="text-md font-medium text-gray-900">Scenario ${scenarioCount}</h4>
            <button type="button" onclick="removeScenario(this)" class="text-red-600 hover:text-red-800">
                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
            </button>
        </div>
        <div class="grid grid-cols-2 md:grid-cols-5 gap-4">
            <div>
                <label class="block text-xs font-medium text-gray-700">Loan Amount (৳)</label>
                <input type="number" class="scenario-loan-amount mt-1 block w-full text-sm rounded-md border-gray-300" min="1000" value="50000">
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700">Interest Rate (%)</label>
                <input type="number" class="scenario-interest-rate mt-1 block w-full text-sm rounded-md border-gray-300" min="0" max="50" step="0.1" value="15">
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700">Duration (Months)</label>
                <input type="number" class="scenario-duration mt-1 block w-full text-sm rounded-md border-gray-300" min="1" max="60" value="12">
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700">Method</label>
                <select class="scenario-method mt-1 block w-full text-sm rounded-md border-gray-300">
                    <option value="monthly">Monthly</option>
                    <option value="weekly">Weekly</option>
                </select>
            </div>
            <div>
                <label class="block text-xs font-medium text-gray-700">Advance (৳)</label>
                <input type="number" class="scenario-advance mt-1 block w-full text-sm rounded-md border-gray-300" min="0" value="0">
            </div>
        </div>
    `;

    container.appendChild(scenarioDiv);
}

function removeScenario(button) {
    button.closest('.border').remove();
    scenarioCount--;

    // Renumber scenarios
    const scenarios = document.querySelectorAll('#scenarios-container .border');
    scenarios.forEach((scenario, index) => {
        scenario.querySelector('h4').textContent = `Scenario ${index + 1}`;
    });
    scenarioCount = scenarios.length;
}

function compareScenarios() {
    const scenarios = [];
    const scenarioElements = document.querySelectorAll('#scenarios-container .border');

    scenarioElements.forEach(element => {
        scenarios.push({
            loan_amount: parseFloat(element.querySelector('.scenario-loan-amount').value) || 0,
            interest_rate: parseFloat(element.querySelector('.scenario-interest-rate').value) || 0,
            loan_duration_months: parseInt(element.querySelector('.scenario-duration').value) || 0,
            repayment_method: element.querySelector('.scenario-method').value,
            advance_payment: parseFloat(element.querySelector('.scenario-advance').value) || 0
        });
    });

    if (scenarios.length < 2) {
        alert('Please add at least 2 scenarios to compare');
        return;
    }

    fetch('/field-officer/loan-calculator/compare', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({ scenarios })
    })
    .then(response => response.json())
    .then(data => {
        displayComparisonResults(data);
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Error comparing scenarios. Please try again.');
    });
}

function displayComparisonResults(data) {
    const resultsDiv = document.getElementById('comparison-results');
    let html = '<h4 class="text-lg font-medium text-gray-900 mb-4">Comparison Results</h4>';

    html += '<div class="overflow-x-auto">';
    html += '<table class="min-w-full text-sm">';
    html += '<thead class="bg-gray-100"><tr>';
    html += '<th class="px-4 py-2 text-left">Metric</th>';

    Object.keys(data.comparisons).forEach((key, index) => {
        const isBest = data.best_scenario === key;
        html += `<th class="px-4 py-2 text-center ${isBest ? 'bg-green-100 text-green-800' : ''}">Scenario ${index + 1}${isBest ? ' (Best)' : ''}</th>`;
    });

    html += '</tr></thead><tbody>';

    const metrics = [
        { key: 'loan_amount', label: 'Loan Amount' },
        { key: 'total_interest', label: 'Total Interest' },
        { key: 'total_repayment', label: 'Total Repayment' },
        { key: 'installment_amount', label: 'Per Installment' },
        { key: 'total_installments', label: 'Total Installments' }
    ];

    metrics.forEach(metric => {
        html += '<tr class="border-t">';
        html += `<td class="px-4 py-2 font-medium">${metric.label}</td>`;

        Object.keys(data.comparisons).forEach(key => {
            const value = data.comparisons[key][metric.key];
            const isBest = data.best_scenario === key;
            html += `<td class="px-4 py-2 text-center ${isBest ? 'bg-green-50' : ''}">৳${value}</td>`;
        });

        html += '</tr>';
    });

    html += '</tbody></table></div>';

    resultsDiv.innerHTML = html;
    resultsDiv.classList.remove('hidden');
}

function showLoading(type) {
    document.getElementById(`${type}-placeholder`).classList.add('hidden');
    document.getElementById(`${type}-results`).classList.add('hidden');
    document.getElementById(`${type}-loading`).classList.remove('hidden');
}

function hideLoading(type) {
    document.getElementById(`${type}-loading`).classList.add('hidden');
}
</script>
@endpush