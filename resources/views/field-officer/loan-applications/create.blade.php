@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">New Loan Application</h1>
                    <p class="text-gray-600">Submit a loan application for your members</p>
                </div>
                <a href="{{ route('field-officer.loan-applications.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Back to Applications
                </a>
            </div>
        </div>

        <!-- Application Form -->
        <form method="POST" action="{{ route('field-officer.loan-applications.store') }}" id="loanApplicationForm">
            @csrf
            
            <!-- Member Selection -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Member Selection</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="member_id" class="block text-sm font-medium text-gray-700">Select Member *</label>
                            <select name="member_id" id="member_id" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Choose a member</option>
                                @foreach($members as $member)
                                    <option value="{{ $member->id }}" {{ old('member_id') == $member->id ? 'selected' : '' }}>
                                        {{ $member->member_id }} - {{ $member->name }}
                                    </option>
                                @endforeach
                            </select>
                            @error('member_id')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div id="member-info" class="hidden">
                            <label class="block text-sm font-medium text-gray-700">Member Information</label>
                            <div class="mt-1 p-3 bg-gray-50 rounded-md">
                                <div id="member-details" class="text-sm text-gray-600">
                                    <!-- Member details will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Loan Details -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Loan Details</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="applied_amount" class="block text-sm font-medium text-gray-700">Applied Amount (৳) *</label>
                            <input type="number" name="applied_amount" id="applied_amount" required min="1000" max="500000" step="100" value="{{ old('applied_amount') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('applied_amount')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="advance_payment" class="block text-sm font-medium text-gray-700">Advance Payment (৳)</label>
                            <input type="number" name="advance_payment" id="advance_payment" min="0" step="100" value="{{ old('advance_payment', 0) }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <p class="mt-1 text-xs text-gray-500">Maximum 20% of loan amount</p>
                            @error('advance_payment')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="md:col-span-2">
                            <label for="reason" class="block text-sm font-medium text-gray-700">Loan Purpose *</label>
                            <textarea name="reason" id="reason" rows="4" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Describe the purpose of the loan...">{{ old('reason') }}</textarea>
                            @error('reason')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="recommender" class="block text-sm font-medium text-gray-700">Recommender</label>
                            <input type="text" name="recommender" id="recommender" value="{{ old('recommender') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500" placeholder="Name of recommender (optional)">
                            @error('recommender')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div id="loan-cycle-info" class="hidden">
                            <label class="block text-sm font-medium text-gray-700">Loan Cycle Information</label>
                            <div class="mt-1 p-3 bg-blue-50 rounded-md">
                                <div id="cycle-details" class="text-sm text-blue-600">
                                    <!-- Cycle details will be populated by JavaScript -->
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Integrated Loan Calculator -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Loan Calculator</h3>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div>
                            <label for="calc_interest_rate" class="block text-sm font-medium text-gray-700">Interest Rate (%) *</label>
                            <input type="number" id="calc_interest_rate" min="0" max="50" step="0.1" value="15" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label for="calc_duration" class="block text-sm font-medium text-gray-700">Duration (Months) *</label>
                            <input type="number" id="calc_duration" min="1" max="60" value="12" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                        </div>

                        <div>
                            <label for="calc_repayment_method" class="block text-sm font-medium text-gray-700">Repayment Method *</label>
                            <select id="calc_repayment_method" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="monthly">Monthly</option>
                                <option value="weekly">Weekly</option>
                            </select>
                        </div>
                    </div>

                    <div class="mt-4">
                        <button type="button" id="calculate-btn" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 12a1 1 0 102 0V8a1 1 0 10-2 0v4zm0-6a1 1 0 102 0 1 1 0 00-2 0z"></path>
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm0-2a6 6 0 100-12 6 6 0 000 12z" clip-rule="evenodd"></path>
                            </svg>
                            Calculate Repayment
                        </button>
                    </div>

                    <!-- Calculation Results -->
                    <div id="calculation-results" class="hidden mt-6">
                        <h4 class="text-md font-medium text-gray-900 mb-3">Calculation Results</h4>
                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                            <div class="bg-gray-50 p-3 rounded-md">
                                <div class="text-sm text-gray-500">Loan Amount</div>
                                <div id="result-loan-amount" class="text-lg font-medium text-gray-900">-</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <div class="text-sm text-gray-500">Total Interest</div>
                                <div id="result-total-interest" class="text-lg font-medium text-gray-900">-</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <div class="text-sm text-gray-500">Total Repayment</div>
                                <div id="result-total-repayment" class="text-lg font-medium text-gray-900">-</div>
                            </div>
                            <div class="bg-gray-50 p-3 rounded-md">
                                <div class="text-sm text-gray-500">Installment Amount</div>
                                <div id="result-installment-amount" class="text-lg font-medium text-gray-900">-</div>
                            </div>
                        </div>
                        <div class="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div class="bg-blue-50 p-3 rounded-md">
                                <div class="text-sm text-blue-600">Net Repayment (After Advance)</div>
                                <div id="result-net-repayment" class="text-lg font-medium text-blue-900">-</div>
                            </div>
                            <div class="bg-green-50 p-3 rounded-md">
                                <div class="text-sm text-green-600">Total Installments</div>
                                <div id="result-total-installments" class="text-lg font-medium text-green-900">-</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Application Status -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Application Status</h3>
                    <div class="bg-yellow-50 border border-yellow-200 rounded-md p-4">
                        <div class="flex">
                            <div class="flex-shrink-0">
                                <svg class="h-5 w-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            <div class="ml-3">
                                <h3 class="text-sm font-medium text-yellow-800">Application Review Process</h3>
                                <div class="mt-2 text-sm text-yellow-700">
                                    <p>Once submitted, this application will be sent to the branch manager for review and approval. You will be notified of the decision.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between">
                        <button type="button" id="preview-application" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                            </svg>
                            Preview Application
                        </button>
                        <div class="space-x-3">
                            <a href="{{ route('field-officer.loan-applications.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Submit Application
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Preview Modal -->
<div id="preview-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Loan Application Preview</h3>
            <div id="preview-content" class="space-y-4">
                <!-- Preview content will be populated by JavaScript -->
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closePreview()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    Close
                </button>
                <button type="button" onclick="submitApplication()" class="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700">
                    Confirm & Submit
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    const memberSelect = document.getElementById('member_id');
    const appliedAmountInput = document.getElementById('applied_amount');
    const advancePaymentInput = document.getElementById('advance_payment');
    const calculateBtn = document.getElementById('calculate-btn');

    // Member selection change
    memberSelect.addEventListener('change', function() {
        const memberId = this.value;
        if (memberId) {
            fetchMemberDetails(memberId);
            checkEligibility(memberId);
        } else {
            document.getElementById('member-info').classList.add('hidden');
            document.getElementById('loan-cycle-info').classList.add('hidden');
        }
    });

    // Applied amount change - update advance payment max
    appliedAmountInput.addEventListener('input', function() {
        const loanAmount = parseFloat(this.value) || 0;
        const maxAdvance = loanAmount * 0.2;
        advancePaymentInput.max = maxAdvance;
        
        if (parseFloat(advancePaymentInput.value) > maxAdvance) {
            advancePaymentInput.value = maxAdvance;
        }
    });

    // Calculate button
    calculateBtn.addEventListener('click', calculateRepayment);

    // Preview button
    document.getElementById('preview-application').addEventListener('click', showPreview);
});

function fetchMemberDetails(memberId) {
    fetch(`/field-officer/members/${memberId}/details`)
        .then(response => response.json())
        .then(data => {
            if (data.member) {
                const member = data.member;
                let detailsHtml = `
                    <p><strong>Member ID:</strong> ${member.member_id}</p>
                    <p><strong>Name:</strong> ${member.name}</p>
                    <p><strong>Phone:</strong> ${member.phone_number}</p>
                `;
                
                if (member.current_loan) {
                    detailsHtml += `<p class="text-red-600"><strong>Current Loan:</strong> ${member.current_loan.loan_id} (৳${member.current_loan.outstanding_amount})</p>`;
                }
                
                document.getElementById('member-details').innerHTML = detailsHtml;
                document.getElementById('member-info').classList.remove('hidden');
            }
        })
        .catch(error => console.error('Error fetching member details:', error));
}

function checkEligibility(memberId) {
    fetch(`/field-officer/members/${memberId}/eligibility`)
        .then(response => response.json())
        .then(data => {
            let cycleHtml = `<p><strong>Loan Cycle:</strong> ${data.loan_cycle_number}</p>`;
            
            if (!data.eligible) {
                cycleHtml += `<p class="text-red-600"><strong>Status:</strong> ${data.reason}</p>`;
            } else {
                cycleHtml += `<p class="text-green-600"><strong>Status:</strong> Eligible for loan</p>`;
            }
            
            if (data.loan_history_count > 0) {
                cycleHtml += `<p><strong>Previous Loans:</strong> ${data.loan_history_count}</p>`;
            }
            
            document.getElementById('cycle-details').innerHTML = cycleHtml;
            document.getElementById('loan-cycle-info').classList.remove('hidden');
        })
        .catch(error => console.error('Error checking eligibility:', error));
}

function calculateRepayment() {
    const loanAmount = parseFloat(document.getElementById('applied_amount').value) || 0;
    const advancePayment = parseFloat(document.getElementById('advance_payment').value) || 0;
    const interestRate = parseFloat(document.getElementById('calc_interest_rate').value) || 0;
    const duration = parseInt(document.getElementById('calc_duration').value) || 0;
    const repaymentMethod = document.getElementById('calc_repayment_method').value;

    if (loanAmount === 0 || interestRate === 0 || duration === 0) {
        alert('Please fill in all required fields for calculation.');
        return;
    }

    const data = {
        loan_amount: loanAmount,
        advance_payment: advancePayment,
        interest_rate: interestRate,
        loan_duration_months: duration,
        repayment_method: repaymentMethod
    };

    fetch('/field-officer/calculate-repayment', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(data.error);
            return;
        }

        document.getElementById('result-loan-amount').textContent = '৳' + data.loan_amount;
        document.getElementById('result-total-interest').textContent = '৳' + data.total_interest;
        document.getElementById('result-total-repayment').textContent = '৳' + data.total_repayment;
        document.getElementById('result-net-repayment').textContent = '৳' + data.net_repayment;
        document.getElementById('result-installment-amount').textContent = '৳' + data.installment_amount;
        document.getElementById('result-total-installments').textContent = data.total_installments;

        document.getElementById('calculation-results').classList.remove('hidden');
    })
    .catch(error => {
        console.error('Error calculating repayment:', error);
        alert('Error calculating repayment. Please try again.');
    });
}

function showPreview() {
    const form = document.getElementById('loanApplicationForm');
    const formData = new FormData(form);
    
    const memberSelect = document.getElementById('member_id');
    const selectedMember = memberSelect.options[memberSelect.selectedIndex].text;
    
    let previewHtml = '<div class="space-y-4">';
    previewHtml += '<div><h4 class="font-medium text-gray-900">Member Information</h4>';
    previewHtml += `<p class="text-sm text-gray-600">${selectedMember}</p></div>`;
    
    previewHtml += '<div><h4 class="font-medium text-gray-900">Loan Details</h4>';
    previewHtml += '<div class="grid grid-cols-2 gap-4 text-sm">';
    previewHtml += `<p><span class="font-medium">Applied Amount:</span> ৳${formData.get('applied_amount')}</p>`;
    previewHtml += `<p><span class="font-medium">Advance Payment:</span> ৳${formData.get('advance_payment') || '0'}</p>`;
    previewHtml += `<p class="col-span-2"><span class="font-medium">Purpose:</span> ${formData.get('reason')}</p>`;
    if (formData.get('recommender')) {
        previewHtml += `<p class="col-span-2"><span class="font-medium">Recommender:</span> ${formData.get('recommender')}</p>`;
    }
    previewHtml += '</div></div>';
    previewHtml += '</div>';
    
    document.getElementById('preview-content').innerHTML = previewHtml;
    document.getElementById('preview-modal').classList.remove('hidden');
}

function closePreview() {
    document.getElementById('preview-modal').classList.add('hidden');
}

function submitApplication() {
    document.getElementById('loanApplicationForm').submit();
}
</script>
@endpush
