@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Collection Dashboard</h1>
                    <p class="text-gray-600">Manage daily collections and track payments</p>
                </div>
                <div class="flex space-x-3">
                    <a href="{{ route('field-officer.collections.collect') }}" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-green-600 hover:bg-green-700">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"></path>
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.51-1.31c-.562-.649-1.413-1.076-2.353-1.253V5z" clip-rule="evenodd"></path>
                        </svg>
                        Collect Payment
                    </a>
                    <a href="{{ route('field-officer.collections.history') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                        <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        Collection History
                    </a>
                </div>
            </div>
        </div>

        <!-- Summary Cards -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Today's Due</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $summary['todays_due_count'] ?? 0 }}</dd>
                                <dd class="text-sm text-gray-500">৳{{ number_format($summary['todays_due_amount'] ?? 0, 0) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Collected Today</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $summary['collected_today_count'] ?? 0 }}</dd>
                                <dd class="text-sm text-gray-500">৳{{ number_format($summary['collected_today_amount'] ?? 0, 0) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Overdue</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $summary['overdue_count'] ?? 0 }}</dd>
                                <dd class="text-sm text-gray-500">৳{{ number_format($summary['overdue_amount'] ?? 0, 0) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <svg class="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Pending Today</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $summary['pending_today_count'] ?? 0 }}</dd>
                                <dd class="text-sm text-gray-500">৳{{ number_format($summary['pending_today_amount'] ?? 0, 0) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Date Filter -->
        <div class="bg-white shadow rounded-lg mb-6">
            <div class="px-4 py-5 sm:p-6">
                <form method="GET" action="{{ route('field-officer.collections.index') }}" class="flex items-center space-x-4">
                    <div>
                        <label for="date" class="block text-sm font-medium text-gray-700">Collection Date</label>
                        <input type="date" name="date" id="date" value="{{ $date }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                    </div>
                    <div class="flex items-end">
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path>
                            </svg>
                            Filter
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Collection Tabs -->
        <div class="bg-white shadow rounded-lg">
            <div class="border-b border-gray-200">
                <nav class="-mb-px flex space-x-8 px-6">
                    <button class="collection-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="todays">
                        Today's Collections ({{ $todaysCollections->count() }})
                    </button>
                    <button class="collection-tab border-indigo-500 text-indigo-600 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="overdue">
                        Overdue Collections ({{ $overdueCollections->count() }})
                    </button>
                    <button class="collection-tab border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" data-tab="collected">
                        Collected Today ({{ $collectionsMadeToday->count() }})
                    </button>
                </nav>
            </div>

            <!-- Today's Collections -->
            <div id="todays-tab" class="collection-content hidden">
                <div class="px-6 py-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Today's Due Collections</h3>
                    @forelse($todaysCollections as $installment)
                        <div class="border border-gray-200 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="text-md font-medium text-gray-900">{{ $installment->loan->member->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $installment->loan->member->member_id }} • {{ $installment->loan->member->phone_number }}</p>
                                    <p class="text-sm text-gray-600 mt-1">
                                        Loan: {{ $installment->loan->loan_id }} • Installment #{{ $installment->installment_no }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-medium text-gray-900">৳{{ number_format($installment->amount, 2) }}</p>
                                    <p class="text-sm text-gray-500">Due: {{ $installment->installment_date->format('M d, Y') }}</p>
                                    @if($installment->status === 'pending')
                                        <a href="{{ route('field-officer.collections.collect', ['member_id' => $installment->loan->member->id]) }}" class="mt-2 inline-flex items-center px-3 py-1 border border-transparent rounded-md text-xs font-medium text-white bg-green-600 hover:bg-green-700">
                                            Collect Now
                                        </a>
                                    @else
                                        <span class="mt-2 inline-flex items-center px-3 py-1 rounded-md text-xs font-medium bg-green-100 text-green-800">
                                            Collected
                                        </span>
                                    @endif
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No collections due today</h3>
                            <p class="mt-1 text-sm text-gray-500">All collections for today have been completed.</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Overdue Collections -->
            <div id="overdue-tab" class="collection-content">
                <div class="px-6 py-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Overdue Collections</h3>
                    @forelse($overdueCollections as $installment)
                        <div class="border border-red-200 bg-red-50 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="text-md font-medium text-gray-900">{{ $installment->loan->member->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $installment->loan->member->member_id }} • {{ $installment->loan->member->phone_number }}</p>
                                    <p class="text-sm text-gray-600 mt-1">
                                        Loan: {{ $installment->loan->loan_id }} • Installment #{{ $installment->installment_no }}
                                    </p>
                                    <p class="text-sm text-red-600 mt-1">
                                        Overdue by {{ $installment->installment_date->diffInDays(now()) }} days
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-medium text-red-600">৳{{ number_format($installment->amount, 2) }}</p>
                                    <p class="text-sm text-red-500">Due: {{ $installment->installment_date->format('M d, Y') }}</p>
                                    <a href="{{ route('field-officer.collections.collect', ['member_id' => $installment->loan->member->id]) }}" class="mt-2 inline-flex items-center px-3 py-1 border border-transparent rounded-md text-xs font-medium text-white bg-red-600 hover:bg-red-700">
                                        Collect Now
                                    </a>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No overdue collections</h3>
                            <p class="mt-1 text-sm text-gray-500">All your members are up to date with their payments.</p>
                        </div>
                    @endforelse
                </div>
            </div>

            <!-- Collected Today -->
            <div id="collected-tab" class="collection-content hidden">
                <div class="px-6 py-4">
                    <h3 class="text-lg font-medium text-gray-900 mb-4">Collections Made Today</h3>
                    @forelse($collectionsMadeToday as $installment)
                        <div class="border border-green-200 bg-green-50 rounded-lg p-4 mb-4">
                            <div class="flex justify-between items-start">
                                <div>
                                    <h4 class="text-md font-medium text-gray-900">{{ $installment->loan->member->name }}</h4>
                                    <p class="text-sm text-gray-500">{{ $installment->loan->member->member_id }} • {{ $installment->loan->member->phone_number }}</p>
                                    <p class="text-sm text-gray-600 mt-1">
                                        Loan: {{ $installment->loan->loan_id }} • Installment #{{ $installment->installment_no }}
                                    </p>
                                    <p class="text-sm text-green-600 mt-1">
                                        Collected at {{ $installment->collection_date->format('h:i A') }}
                                    </p>
                                </div>
                                <div class="text-right">
                                    <p class="text-lg font-medium text-green-600">৳{{ number_format($installment->amount, 2) }}</p>
                                    @if($installment->late_fee > 0)
                                        <p class="text-sm text-orange-600">Late Fee: ৳{{ number_format($installment->late_fee, 2) }}</p>
                                    @endif
                                    <a href="{{ route('field-officer.collections.receipt', $installment) }}" class="mt-2 inline-flex items-center px-3 py-1 border border-transparent rounded-md text-xs font-medium text-white bg-blue-600 hover:bg-blue-700">
                                        View Receipt
                                    </a>
                                </div>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                            </svg>
                            <h3 class="mt-2 text-sm font-medium text-gray-900">No collections made today</h3>
                            <p class="mt-1 text-sm text-gray-500">Start collecting payments to see them here.</p>
                        </div>
                    @endforelse
                </div>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Tab functionality
    const tabs = document.querySelectorAll('.collection-tab');
    const contents = document.querySelectorAll('.collection-content');
    
    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');
            
            // Remove active classes from all tabs
            tabs.forEach(t => {
                t.classList.remove('border-indigo-500', 'text-indigo-600');
                t.classList.add('border-transparent', 'text-gray-500');
            });
            
            // Add active classes to clicked tab
            this.classList.remove('border-transparent', 'text-gray-500');
            this.classList.add('border-indigo-500', 'text-indigo-600');
            
            // Hide all content
            contents.forEach(content => {
                content.classList.add('hidden');
            });
            
            // Show target content
            document.getElementById(targetTab + '-tab').classList.remove('hidden');
        });
    });

    // Set default date to today
    if (!document.getElementById('date').value) {
        document.getElementById('date').value = new Date().toISOString().split('T')[0];
    }
});
</script>
@endpush
