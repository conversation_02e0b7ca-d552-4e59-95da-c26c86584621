@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Register New Member</h1>
                    <p class="text-gray-600">Fill out the comprehensive member registration form</p>
                </div>
                <a href="{{ route('field-officer.members.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                    <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd"></path>
                    </svg>
                    Back to Members
                </a>
            </div>
        </div>

        <!-- Registration Form -->
        <form method="POST" action="{{ route('field-officer.members.store') }}" enctype="multipart/form-data" id="memberForm">
            @csrf
            
            <!-- Personal Information -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Personal Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="name" class="block text-sm font-medium text-gray-700">Full Name *</label>
                            <input type="text" name="name" id="name" required value="{{ old('name') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="father_or_husband_name" class="block text-sm font-medium text-gray-700">Father/Husband Name *</label>
                            <input type="text" name="father_or_husband_name" id="father_or_husband_name" required value="{{ old('father_or_husband_name') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('father_or_husband_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="mother_name" class="block text-sm font-medium text-gray-700">Mother's Name *</label>
                            <input type="text" name="mother_name" id="mother_name" required value="{{ old('mother_name') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('mother_name')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="date_of_birth" class="block text-sm font-medium text-gray-700">Date of Birth *</label>
                            <input type="date" name="date_of_birth" id="date_of_birth" required value="{{ old('date_of_birth') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('date_of_birth')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="nid_number" class="block text-sm font-medium text-gray-700">NID Number *</label>
                            <input type="text" name="nid_number" id="nid_number" required value="{{ old('nid_number') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('nid_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="phone_number" class="block text-sm font-medium text-gray-700">Phone Number *</label>
                            <input type="tel" name="phone_number" id="phone_number" required value="{{ old('phone_number') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('phone_number')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="religion" class="block text-sm font-medium text-gray-700">Religion *</label>
                            <select name="religion" id="religion" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select Religion</option>
                                <option value="islam" {{ old('religion') == 'islam' ? 'selected' : '' }}>Islam</option>
                                <option value="hinduism" {{ old('religion') == 'hinduism' ? 'selected' : '' }}>Hinduism</option>
                                <option value="christianity" {{ old('religion') == 'christianity' ? 'selected' : '' }}>Christianity</option>
                                <option value="buddhism" {{ old('religion') == 'buddhism' ? 'selected' : '' }}>Buddhism</option>
                                <option value="other" {{ old('religion') == 'other' ? 'selected' : '' }}>Other</option>
                            </select>
                            @error('religion')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="blood_group" class="block text-sm font-medium text-gray-700">Blood Group</label>
                            <select name="blood_group" id="blood_group" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                                <option value="">Select Blood Group</option>
                                <option value="A+" {{ old('blood_group') == 'A+' ? 'selected' : '' }}>A+</option>
                                <option value="A-" {{ old('blood_group') == 'A-' ? 'selected' : '' }}>A-</option>
                                <option value="B+" {{ old('blood_group') == 'B+' ? 'selected' : '' }}>B+</option>
                                <option value="B-" {{ old('blood_group') == 'B-' ? 'selected' : '' }}>B-</option>
                                <option value="AB+" {{ old('blood_group') == 'AB+' ? 'selected' : '' }}>AB+</option>
                                <option value="AB-" {{ old('blood_group') == 'AB-' ? 'selected' : '' }}>AB-</option>
                                <option value="O+" {{ old('blood_group') == 'O+' ? 'selected' : '' }}>O+</option>
                                <option value="O-" {{ old('blood_group') == 'O-' ? 'selected' : '' }}>O-</option>
                            </select>
                            @error('blood_group')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Address Information -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Address Information</h3>
                    <div class="grid grid-cols-1 gap-6">
                        <div>
                            <label for="present_address" class="block text-sm font-medium text-gray-700">Present Address *</label>
                            <textarea name="present_address" id="present_address" rows="3" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('present_address') }}</textarea>
                            @error('present_address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <div class="flex items-center mb-2">
                                <input type="checkbox" id="same_as_present" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                                <label for="same_as_present" class="ml-2 block text-sm text-gray-700">Same as present address</label>
                            </div>
                            <label for="permanent_address" class="block text-sm font-medium text-gray-700">Permanent Address *</label>
                            <textarea name="permanent_address" id="permanent_address" rows="3" required class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">{{ old('permanent_address') }}</textarea>
                            @error('permanent_address')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Occupation & Income -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Occupation & Income Information</h3>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <label for="occupation" class="block text-sm font-medium text-gray-700">Occupation *</label>
                            <input type="text" name="occupation" id="occupation" required value="{{ old('occupation') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('occupation')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <div>
                            <label for="monthly_income" class="block text-sm font-medium text-gray-700">Monthly Income (৳) *</label>
                            <input type="number" name="monthly_income" id="monthly_income" required min="0" step="0.01" value="{{ old('monthly_income') }}" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            @error('monthly_income')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>
                    </div>
                </div>
            </div>

            <!-- Photo Upload -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Photo Upload</h3>

                    <x-image-upload
                        name="photo"
                        label="Member Photo"
                        :required="false"
                        max-size="2MB"
                        dimensions="400x400"
                        help-text="Upload a clear photo of the member (JPG, PNG, or WebP format, max 2MB)"
                    />
                </div>
            </div>

            <!-- Reference Information -->
            <div class="bg-white shadow rounded-lg mb-6">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Reference Information</h3>
                    <div>
                        <label for="reference_id" class="block text-sm font-medium text-gray-700">Reference Member</label>
                        <select name="reference_id" id="reference_id" class="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-500 focus:ring-indigo-500">
                            <option value="">Select Reference Member (Optional)</option>
                            @foreach($existingMembers as $member)
                                <option value="{{ $member->id }}" {{ old('reference_id') == $member->id ? 'selected' : '' }}>
                                    {{ $member->member_id }} - {{ $member->name }}
                                </option>
                            @endforeach
                        </select>
                        @error('reference_id')
                            <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                        @enderror
                    </div>
                </div>
            </div>

            <!-- Form Actions -->
            <div class="bg-white shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex justify-between">
                        <button type="button" id="preview-btn" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                            <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"></path>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"></path>
                            </svg>
                            Preview
                        </button>
                        <div class="space-x-3">
                            <a href="{{ route('field-officer.members.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50">
                                Cancel
                            </a>
                            <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700">
                                <svg class="w-4 h-4 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                </svg>
                                Register Member
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Preview Modal -->
<div id="preview-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden">
    <div class="relative top-20 mx-auto p-5 border w-11/12 max-w-4xl shadow-lg rounded-md bg-white">
        <div class="mt-3">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Member Registration Preview</h3>
            <div id="preview-content" class="space-y-4">
                <!-- Preview content will be populated by JavaScript -->
            </div>
            <div class="flex justify-end space-x-3 mt-6">
                <button type="button" onclick="closePreview()" class="px-4 py-2 bg-gray-300 text-gray-700 rounded-md hover:bg-gray-400">
                    Close
                </button>
                <button type="button" onclick="submitForm()" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                    Confirm & Register
                </button>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation and other functionality can be added here

    // Same address checkbox
    const sameAsPresent = document.getElementById('same_as_present');
    const presentAddress = document.getElementById('present_address');
    const permanentAddress = document.getElementById('permanent_address');

    sameAsPresent.addEventListener('change', function() {
        if (this.checked) {
            permanentAddress.value = presentAddress.value;
        }
    });

    presentAddress.addEventListener('input', function() {
        if (sameAsPresent.checked) {
            permanentAddress.value = this.value;
        }
    });

    // Preview functionality
    document.getElementById('preview-btn').addEventListener('click', showPreview);
});

function showPreview() {
    const form = document.getElementById('memberForm');
    const formData = new FormData(form);
    
    let previewHtml = '<div class="grid grid-cols-1 md:grid-cols-2 gap-6">';
    
    // Personal Information
    previewHtml += '<div><h4 class="font-medium text-gray-900 mb-2">Personal Information</h4>';
    previewHtml += '<div class="space-y-1 text-sm">';
    previewHtml += `<p><span class="font-medium">Name:</span> ${formData.get('name') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Father/Husband:</span> ${formData.get('father_or_husband_name') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Mother:</span> ${formData.get('mother_name') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Date of Birth:</span> ${formData.get('date_of_birth') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">NID:</span> ${formData.get('nid_number') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Phone:</span> ${formData.get('phone_number') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Religion:</span> ${formData.get('religion') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Blood Group:</span> ${formData.get('blood_group') || 'Not provided'}</p>`;
    previewHtml += '</div></div>';
    
    // Address Information
    previewHtml += '<div><h4 class="font-medium text-gray-900 mb-2">Address Information</h4>';
    previewHtml += '<div class="space-y-1 text-sm">';
    previewHtml += `<p><span class="font-medium">Present Address:</span> ${formData.get('present_address') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Permanent Address:</span> ${formData.get('permanent_address') || 'Not provided'}</p>`;
    previewHtml += '</div></div>';
    
    previewHtml += '</div>';
    
    // Occupation & Income
    previewHtml += '<div class="mt-4"><h4 class="font-medium text-gray-900 mb-2">Occupation & Income</h4>';
    previewHtml += '<div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">';
    previewHtml += `<p><span class="font-medium">Occupation:</span> ${formData.get('occupation') || 'Not provided'}</p>`;
    previewHtml += `<p><span class="font-medium">Monthly Income:</span> ৳${formData.get('monthly_income') || 'Not provided'}</p>`;
    previewHtml += '</div></div>';
    
    document.getElementById('preview-content').innerHTML = previewHtml;
    document.getElementById('preview-modal').classList.remove('hidden');
}

function closePreview() {
    document.getElementById('preview-modal').classList.add('hidden');
}

function submitForm() {
    document.getElementById('memberForm').submit();
}
</script>
@endpush
