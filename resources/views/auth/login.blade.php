@extends('layouts.app')

@section('title', 'Login - Sonali Microfinance')

@section('content')
<div class="min-h-screen bg-gradient-to-br from-primary-50 via-white to-secondary-50 flex">
    <!-- Left Side - Login Form -->
    <div class="flex-1 flex items-center justify-center px-4 sm:px-6 lg:px-8">
        <div class="max-w-md w-full space-y-8">
            <!-- Header -->
            <div class="text-center">
                <div class="mx-auto h-16 w-16 bg-primary-100 rounded-full flex items-center justify-center">
                    <i class="fas fa-university text-2xl text-primary-600"></i>
                </div>
                <h2 class="mt-6 text-3xl font-bold text-gray-900">
                    Welcome Back
                </h2>
                <p class="mt-2 text-sm text-gray-600">
                    Sign in to your Sonali Microfinance account
                </p>
            </div>

            <!-- Login Form -->
            <form class="mt-8 space-y-6" method="POST" action="{{ route('login') }}">
                @csrf

                <!-- Flash Messages -->
                @if(session('error'))
                    <div class="bg-danger-50 border border-danger-200 text-danger-800 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-circle mr-2"></i>
                            {{ session('error') }}
                        </div>
                    </div>
                @endif

                @if(session('success'))
                    <div class="bg-success-50 border border-success-200 text-success-800 px-4 py-3 rounded-lg">
                        <div class="flex items-center">
                            <i class="fas fa-check-circle mr-2"></i>
                            {{ session('success') }}
                        </div>
                    </div>
                @endif

                <div class="space-y-4">
                    <!-- Email Field -->
                    <div>
                        <label for="email" class="block text-sm font-medium text-gray-700 mb-1">
                            Email Address
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-envelope text-gray-400"></i>
                            </div>
                            <input
                                id="email"
                                name="email"
                                type="email"
                                autocomplete="email"
                                required
                                value="{{ old('email') }}"
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('email') border-danger-300 focus:ring-danger-500 focus:border-danger-500 @enderror"
                                placeholder="Enter your email address"
                            >
                        </div>
                        @error('email')
                            <p class="mt-1 text-sm text-danger-600">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>

                    <!-- Password Field -->
                    <div>
                        <label for="password" class="block text-sm font-medium text-gray-700 mb-1">
                            Password
                        </label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <i class="fas fa-lock text-gray-400"></i>
                            </div>
                            <input
                                id="password"
                                name="password"
                                type="password"
                                autocomplete="current-password"
                                required
                                class="block w-full pl-10 pr-3 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 @error('password') border-danger-300 focus:ring-danger-500 focus:border-danger-500 @enderror"
                                placeholder="Enter your password"
                            >
                        </div>
                        @error('password')
                            <p class="mt-1 text-sm text-danger-600">
                                <i class="fas fa-exclamation-circle mr-1"></i>
                                {{ $message }}
                            </p>
                        @enderror
                    </div>
                </div>

                <!-- Remember Me & Forgot Password -->
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <input
                            id="remember"
                            name="remember"
                            type="checkbox"
                            {{ old('remember') ? 'checked' : '' }}
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                        >
                        <label for="remember" class="ml-2 block text-sm text-gray-700">
                            Remember me
                        </label>
                    </div>

                    @if (Route::has('password.request'))
                        <div class="text-sm">
                            <a href="{{ route('password.request') }}" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200">
                                Forgot your password?
                            </a>
                        </div>
                    @endif
                </div>

                <!-- Submit Button -->
                <div>
                    <button
                        type="submit"
                        class="group relative w-full flex justify-center py-3 px-4 border border-transparent text-sm font-medium rounded-lg text-white bg-primary-600 hover:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
                    >
                        <span class="absolute left-0 inset-y-0 flex items-center pl-3">
                            <i class="fas fa-sign-in-alt text-primary-500 group-hover:text-primary-400"></i>
                        </span>
                        Sign In
                    </button>
                </div>

                <!-- Additional Links -->
                <div class="text-center">
                    <p class="text-sm text-gray-600">
                        Need help?
                        <a href="#" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200">
                            Contact Support
                        </a>
                    </p>
                </div>
            </form>
        </div>
    </div>

    <!-- Right Side - Advertisement Section -->
    <div class="hidden lg:block lg:w-1/2 bg-gradient-to-br from-primary-600 to-secondary-600 relative overflow-hidden">
        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        <div class="relative z-10 flex flex-col justify-center h-full p-12 text-white">
            <!-- Advertisement Content -->
            @php
                $advertisements = collect(); // Temporarily disable advertisements to fix login
            @endphp

            @if($advertisements->count() > 0)
                <div class="space-y-8">
                    @foreach($advertisements as $ad)
                        <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                            @if($ad->image_path)
                                <div class="mb-4">
                                    <img src="{{ asset('storage/' . $ad->image_path) }}" alt="{{ $ad->title }}" class="w-full h-32 object-cover rounded-lg">
                                </div>
                            @endif
                            <h3 class="text-xl font-semibold mb-2">{{ $ad->title }}</h3>
                            <p class="text-white text-opacity-90 mb-4">{{ $ad->content }}</p>
                            @if($ad->link_url)
                                <a href="{{ $ad->link_url }}" target="_blank" class="inline-flex items-center text-accent-300 hover:text-accent-200 font-medium transition-colors duration-200" onclick="incrementAdClick({{ $ad->id }})">
                                    Learn More
                                    <i class="fas fa-arrow-right ml-2"></i>
                                </a>
                            @endif
                        </div>
                    @endforeach
                </div>
            @else
                <!-- Default Content -->
                <div class="space-y-8">
                    <div class="text-center">
                        <h2 class="text-4xl font-bold mb-4">Empowering Communities</h2>
                        <p class="text-xl text-white text-opacity-90 mb-8">
                            Through accessible microfinance solutions that create opportunities and build sustainable livelihoods.
                        </p>
                    </div>

                    <div class="grid grid-cols-1 gap-6">
                        <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-hand-holding-usd text-2xl text-accent-300 mr-3"></i>
                                <h3 class="text-lg font-semibold">Flexible Loans</h3>
                            </div>
                            <p class="text-white text-opacity-90">
                                Get access to affordable loans with flexible repayment terms designed for your needs.
                            </p>
                        </div>

                        <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-piggy-bank text-2xl text-accent-300 mr-3"></i>
                                <h3 class="text-lg font-semibold">Smart Savings</h3>
                            </div>
                            <p class="text-white text-opacity-90">
                                Build your financial future with our competitive savings accounts and investment options.
                            </p>
                        </div>

                        <div class="bg-white bg-opacity-10 backdrop-blur-sm rounded-xl p-6 border border-white border-opacity-20">
                            <div class="flex items-center mb-3">
                                <i class="fas fa-graduation-cap text-2xl text-accent-300 mr-3"></i>
                                <h3 class="text-lg font-semibold">Financial Education</h3>
                            </div>
                            <p class="text-white text-opacity-90">
                                Learn essential financial skills through our comprehensive education programs.
                            </p>
                        </div>
                    </div>
                </div>
            @endif
        </div>

        <!-- Decorative Elements -->
        <div class="absolute top-0 right-0 w-64 h-64 bg-white bg-opacity-5 rounded-full -mr-32 -mt-32"></div>
        <div class="absolute bottom-0 left-0 w-48 h-48 bg-white bg-opacity-5 rounded-full -ml-24 -mb-24"></div>
    </div>
</div>

@push('scripts')
<script>
function incrementAdClick(adId) {
    fetch(`/api/advertisements/${adId}/click`, {
        method: 'POST',
        headers: {
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
            'Content-Type': 'application/json',
        },
    });
}

// Increment view count for advertisements
document.addEventListener('DOMContentLoaded', function() {
    @if($advertisements->count() > 0)
        @foreach($advertisements as $ad)
            fetch(`/api/advertisements/{{ $ad->id }}/view`, {
                method: 'POST',
                headers: {
                    'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                    'Content-Type': 'application/json',
                },
            });
        @endforeach
    @endif
});
</script>
@endpush
@endsection
