@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">Member Dashboard</h1>
            <p class="text-gray-600">Welcome back, {{ $member->name }}. Here's your account overview.</p>
        </div>

        <!-- Notifications -->
        @if(!empty($notifications))
            <div class="mb-8 space-y-4">
                @foreach($notifications as $notification)
                    <div class="bg-{{ $notification['type'] === 'danger' ? 'red' : ($notification['type'] === 'warning' ? 'yellow' : 'blue') }}-50 border border-{{ $notification['type'] === 'danger' ? 'red' : ($notification['type'] === 'warning' ? 'yellow' : 'blue') }}-200 rounded-lg p-4">
                        <div class="flex items-start">
                            <div class="flex-shrink-0">
                                <i class="{{ $notification['icon'] }} text-{{ $notification['type'] === 'danger' ? 'red' : ($notification['type'] === 'warning' ? 'yellow' : 'blue') }}-500"></i>
                            </div>
                            <div class="ml-3 flex-1">
                                <h3 class="text-sm font-medium text-{{ $notification['type'] === 'danger' ? 'red' : ($notification['type'] === 'warning' ? 'yellow' : 'blue') }}-800">
                                    {{ $notification['title'] }}
                                </h3>
                                <p class="mt-1 text-sm text-{{ $notification['type'] === 'danger' ? 'red' : ($notification['type'] === 'warning' ? 'yellow' : 'blue') }}-700">
                                    {{ $notification['message'] }}
                                </p>
                                @if(isset($notification['action_url']))
                                    <div class="mt-3">
                                        <a href="{{ $notification['action_url'] }}" class="text-sm font-medium text-{{ $notification['type'] === 'danger' ? 'red' : ($notification['type'] === 'warning' ? 'yellow' : 'blue') }}-800 hover:text-{{ $notification['type'] === 'danger' ? 'red' : ($notification['type'] === 'warning' ? 'yellow' : 'blue') }}-900">
                                            {{ $notification['action_text'] }} →
                                        </a>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>
                @endforeach
            </div>
        @endif

        <!-- Member Info Card -->
        <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        @if($member->photo)
                            <img class="w-16 h-16 rounded-full object-cover" src="{{ Storage::url($member->photo) }}" alt="{{ $member->name }}">
                        @else
                            <div class="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center">
                                <span class="text-xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                            </div>
                        @endif
                    </div>
                    <div class="ml-4 flex-1">
                        <h3 class="text-lg font-medium text-gray-900">{{ $member->name }}</h3>
                        <p class="text-sm text-gray-500">Member ID: {{ $member->member_id }}</p>
                        <p class="text-sm text-gray-500">Branch: {{ $member->branch->name ?? 'N/A' }}</p>
                        <p class="text-sm text-gray-500">Member Since: {{ $member->created_at->format('M d, Y') }}</p>
                    </div>
                    <div class="flex-shrink-0">
                        <a href="{{ route('member.profile.index') }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-user mr-2"></i>
                            View Profile
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Stats Grid -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Total Savings -->
            <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-piggy-bank text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Savings</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['total_savings'] ?? 0, 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('member.savings.index') }}" class="text-sm text-green-600 hover:text-green-800">View Details →</a>
                    </div>
                </div>
            </div>

            <!-- Active Loans -->
            <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-hand-holding-usd text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Loans</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['active_loans'] ?? 0 }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('member.loans.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View Details →</a>
                    </div>
                </div>
            </div>

            <!-- Outstanding Amount -->
            <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Outstanding</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['outstanding_amount'] ?? 0, 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('member.installments.index') }}" class="text-sm text-yellow-600 hover:text-yellow-800">View Details →</a>
                    </div>
                </div>
            </div>

            <!-- Monthly Installment -->
            <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Monthly Payment</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['monthly_installment'] ?? 0, 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                    <div class="mt-3">
                        <a href="{{ route('member.installments.schedule') }}" class="text-sm text-red-600 hover:text-red-800">View Schedule →</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <a href="{{ route('member.loans.index') }}" class="flex flex-col items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200">
                        <i class="fas fa-file-alt text-blue-500 text-2xl mb-2"></i>
                        <span class="text-sm font-medium text-blue-700">My Loans</span>
                    </a>
                    <a href="{{ route('member.savings.index') }}" class="flex flex-col items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
                        <i class="fas fa-wallet text-green-500 text-2xl mb-2"></i>
                        <span class="text-sm font-medium text-green-700">My Savings</span>
                    </a>
                    <a href="{{ route('member.installments.history') }}" class="flex flex-col items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-200">
                        <i class="fas fa-history text-purple-500 text-2xl mb-2"></i>
                        <span class="text-sm font-medium text-purple-700">Payment History</span>
                    </a>
                    <a href="{{ route('member.profile.index') }}" class="flex flex-col items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                        <i class="fas fa-user text-gray-500 text-2xl mb-2"></i>
                        <span class="text-sm font-medium text-gray-700">My Profile</span>
                    </a>
                </div>
            </div>
        </div>

        <!-- Upcoming Installments & Overdue Payments -->
        @if($upcomingInstallments->count() > 0 || $overdueInstallments->count() > 0)
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
                <!-- Upcoming Installments -->
                @if($upcomingInstallments->count() > 0)
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg leading-6 font-medium text-gray-900">Upcoming Payments</h3>
                                <a href="{{ route('member.installments.schedule') }}" class="text-sm text-blue-600 hover:text-blue-800">View All</a>
                            </div>
                            <div class="space-y-3">
                                @foreach($upcomingInstallments->take(5) as $installment)
                                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Installment #{{ $installment->installment_no }}</p>
                                            <p class="text-xs text-gray-500">Due: {{ $installment->installment_date->format('M d, Y') }}</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium text-blue-600">৳{{ number_format($installment->amount, 2) }}</p>
                                            <p class="text-xs text-gray-500">{{ $installment->installment_date->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif

                <!-- Overdue Installments -->
                @if($overdueInstallments->count() > 0)
                    <div class="bg-white overflow-hidden shadow rounded-lg">
                        <div class="px-4 py-5 sm:p-6">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg leading-6 font-medium text-red-900">Overdue Payments</h3>
                                <a href="{{ route('member.installments.index') }}" class="text-sm text-red-600 hover:text-red-800">View All</a>
                            </div>
                            <div class="space-y-3">
                                @foreach($overdueInstallments->take(5) as $installment)
                                    <div class="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                                        <div>
                                            <p class="text-sm font-medium text-gray-900">Installment #{{ $installment->installment_no }}</p>
                                            <p class="text-xs text-red-600">Overdue: {{ $installment->installment_date->format('M d, Y') }}</p>
                                        </div>
                                        <div class="text-right">
                                            <p class="text-sm font-medium text-red-600">৳{{ number_format($installment->amount + $installment->calculateLateFee(), 2) }}</p>
                                            <p class="text-xs text-red-500">{{ $installment->installment_date->diffForHumans() }}</p>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    </div>
                @endif
            </div>
        @endif

        <!-- Accounts Overview -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
            <!-- Savings Accounts -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Savings Accounts</h3>
                        <a href="{{ route('member.savings.index') }}" class="text-sm text-green-600 hover:text-green-800">View All</a>
                    </div>
                    <div class="space-y-3">
                        @forelse($stats['savings_accounts'] ?? [] as $savings)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">{{ $savings->account_number }}</p>
                                    <p class="text-xs text-gray-500">{{ ucfirst($savings->account_type) }} Account</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-green-600">৳{{ number_format($savings->current_balance, 2) }}</p>
                                    <p class="text-xs text-gray-500">{{ $savings->interest_rate }}% Interest</p>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-piggy-bank text-gray-300 text-3xl mb-2"></i>
                                <p class="text-sm text-gray-500">No savings accounts found</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>

            <!-- Active Loans -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h3 class="text-lg leading-6 font-medium text-gray-900">Active Loans</h3>
                        <a href="{{ route('member.loans.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View All</a>
                    </div>
                    <div class="space-y-3">
                        @forelse($stats['loans'] ?? [] as $loan)
                            <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors duration-200">
                                <div>
                                    <p class="text-sm font-medium text-gray-900">Loan #{{ $loan->id }}</p>
                                    <p class="text-xs text-gray-500">{{ $loan->loan_date->format('M d, Y') }}</p>
                                </div>
                                <div class="text-right">
                                    <p class="text-sm font-medium text-blue-600">৳{{ number_format($loan->loan_amount, 2) }}</p>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        Active
                                    </span>
                                </div>
                            </div>
                        @empty
                            <div class="text-center py-4">
                                <i class="fas fa-hand-holding-usd text-gray-300 text-3xl mb-2"></i>
                                <p class="text-sm text-gray-500">No active loans found</p>
                            </div>
                        @endforelse
                    </div>
                </div>
            </div>
        </div>

        <!-- Recent Transactions -->
        <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900">Recent Transactions</h3>
                    <a href="{{ route('member.savings.index') }}" class="text-sm text-blue-600 hover:text-blue-800">View All</a>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Account</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Type</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Amount</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Balance</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            @forelse($recentTransactions ?? [] as $transaction)
                                <tr class="hover:bg-gray-50">
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        {{ $transaction->transaction_date->format('M d, Y') }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                        {{ $transaction->savingAccount->account_number }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium
                                            {{ $transaction->transaction_type === 'deposit' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}">
                                            {{ ucfirst($transaction->transaction_type) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 text-sm text-gray-500">
                                        {{ $transaction->description }}
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <span class="{{ $transaction->transaction_type === 'deposit' ? 'text-green-600' : 'text-red-600' }}">
                                            {{ $transaction->transaction_type === 'deposit' ? '+' : '-' }}৳{{ number_format($transaction->amount, 2) }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        ৳{{ number_format($transaction->balance_after, 2) }}
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-8 text-center">
                                        <i class="fas fa-exchange-alt text-gray-300 text-3xl mb-2"></i>
                                        <p class="text-sm text-gray-500">No recent transactions found</p>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
