<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Payment Receipt - {{ $installment->id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            color: #333;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #2563eb;
            font-size: 28px;
        }
        .header h2 {
            margin: 5px 0;
            color: #666;
            font-size: 18px;
        }
        .receipt-info {
            background-color: #f8f9fa;
            padding: 15px;
            margin-bottom: 20px;
            border: 1px solid #ddd;
            text-align: center;
        }
        .receipt-info h3 {
            margin: 0;
            color: #16a34a;
            font-size: 20px;
        }
        .details-section {
            margin-bottom: 20px;
        }
        .details-table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 15px;
        }
        .details-table td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .details-table td:first-child {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 40%;
        }
        .amount-section {
            background-color: #e8f5e8;
            padding: 15px;
            border: 2px solid #16a34a;
            margin: 20px 0;
            text-align: center;
        }
        .amount-section h3 {
            margin: 0;
            color: #16a34a;
            font-size: 24px;
        }
        .payment-breakdown {
            margin: 20px 0;
        }
        .payment-breakdown table {
            width: 100%;
            border-collapse: collapse;
        }
        .payment-breakdown td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .payment-breakdown td:first-child {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 60%;
        }
        .payment-breakdown td:last-child {
            text-align: right;
            font-weight: bold;
        }
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 12px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }
        .signature-section {
            margin-top: 40px;
            display: flex;
            justify-content: space-between;
        }
        .signature-box {
            width: 45%;
            text-align: center;
            border-top: 1px solid #333;
            padding-top: 10px;
            margin-top: 40px;
        }
        .status-paid {
            color: #16a34a;
            font-weight: bold;
            font-size: 16px;
        }
        .watermark {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) rotate(-45deg);
            font-size: 80px;
            color: rgba(22, 163, 74, 0.1);
            z-index: -1;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="watermark">PAID</div>
    
    <div class="header">
        <h1>Sonali Microfinance</h1>
        <h2>Payment Receipt</h2>
        <p>{{ $member->branch->address ?? 'Main Branch' }}</p>
    </div>

    <div class="receipt-info">
        <h3>PAYMENT RECEIVED</h3>
        <p><strong>Receipt No:</strong> RCP-{{ str_pad($installment->id, 6, '0', STR_PAD_LEFT) }}</p>
        <p><strong>Date:</strong> {{ $installment->collection_date->format('F d, Y \a\t g:i A') }}</p>
    </div>

    <div class="details-section">
        <h3>Member Information</h3>
        <table class="details-table">
            <tr>
                <td>Member Name</td>
                <td>{{ $member->name }}</td>
            </tr>
            <tr>
                <td>Member ID</td>
                <td>{{ $member->member_id }}</td>
            </tr>
            <tr>
                <td>Branch</td>
                <td>{{ $member->branch->name ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td>Phone Number</td>
                <td>{{ $member->phone_number ?? 'N/A' }}</td>
            </tr>
        </table>
    </div>

    <div class="details-section">
        <h3>Loan Information</h3>
        <table class="details-table">
            <tr>
                <td>Loan ID</td>
                <td>{{ $installment->loan->id }}</td>
            </tr>
            <tr>
                <td>Loan Amount</td>
                <td>৳{{ number_format($installment->loan->loan_amount, 2) }}</td>
            </tr>
            <tr>
                <td>Interest Rate</td>
                <td>{{ $installment->loan->interest_rate }}% per year</td>
            </tr>
            <tr>
                <td>Loan Date</td>
                <td>{{ $installment->loan->loan_date->format('F d, Y') }}</td>
            </tr>
        </table>
    </div>

    <div class="details-section">
        <h3>Installment Details</h3>
        <table class="details-table">
            <tr>
                <td>Installment Number</td>
                <td>{{ $installment->installment_no }}</td>
            </tr>
            <tr>
                <td>Due Date</td>
                <td>{{ $installment->installment_date->format('F d, Y') }}</td>
            </tr>
            <tr>
                <td>Payment Date</td>
                <td>{{ $installment->collection_date->format('F d, Y') }}</td>
            </tr>
            <tr>
                <td>Status</td>
                <td><span class="status-paid">PAID</span></td>
            </tr>
            @if($installment->notes)
            <tr>
                <td>Notes</td>
                <td>{{ $installment->notes }}</td>
            </tr>
            @endif
        </table>
    </div>

    <div class="payment-breakdown">
        <h3>Payment Breakdown</h3>
        <table>
            <tr>
                <td>Installment Amount</td>
                <td>৳{{ number_format($installment->amount, 2) }}</td>
            </tr>
            @if($installment->late_fee > 0)
            <tr>
                <td>Late Fee</td>
                <td>৳{{ number_format($installment->late_fee, 2) }}</td>
            </tr>
            @endif
            <tr style="border-top: 2px solid #333;">
                <td><strong>Total Amount Paid</strong></td>
                <td><strong>৳{{ number_format($installment->amount + ($installment->late_fee ?? 0), 2) }}</strong></td>
            </tr>
        </table>
    </div>

    <div class="amount-section">
        <h3>Amount Received: ৳{{ number_format($installment->amount + ($installment->late_fee ?? 0), 2) }}</h3>
        <p>Payment Method: Cash</p>
    </div>

    <div class="details-section">
        <h3>Collection Information</h3>
        <table class="details-table">
            <tr>
                <td>Collected By</td>
                <td>{{ $installment->collectedBy->name ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td>Collection Date</td>
                <td>{{ $installment->collection_date->format('F d, Y \a\t g:i A') }}</td>
            </tr>
            <tr>
                <td>Payment Status</td>
                <td><span class="status-paid">COMPLETED</span></td>
            </tr>
        </table>
    </div>

    @php
        $remainingInstallments = $installment->loan->installments()
            ->where('status', 'pending')
            ->where('installment_date', '>', $installment->installment_date)
            ->count();
        
        $nextInstallment = $installment->loan->installments()
            ->where('status', 'pending')
            ->where('installment_date', '>', $installment->installment_date)
            ->orderBy('installment_date')
            ->first();
    @endphp

    @if($remainingInstallments > 0)
        <div class="details-section">
            <h3>Next Payment Information</h3>
            <table class="details-table">
                <tr>
                    <td>Remaining Installments</td>
                    <td>{{ $remainingInstallments }}</td>
                </tr>
                @if($nextInstallment)
                <tr>
                    <td>Next Due Date</td>
                    <td>{{ $nextInstallment->installment_date->format('F d, Y') }}</td>
                </tr>
                <tr>
                    <td>Next Amount Due</td>
                    <td>৳{{ number_format($nextInstallment->amount, 2) }}</td>
                </tr>
                @endif
            </table>
        </div>
    @else
        <div class="amount-section" style="background-color: #e8f5e8; border-color: #16a34a;">
            <h3 style="color: #16a34a;">🎉 LOAN COMPLETED 🎉</h3>
            <p>Congratulations! You have successfully completed all loan payments.</p>
        </div>
    @endif

    <div class="signature-section">
        <div class="signature-box">
            <strong>Member Signature</strong>
        </div>
        <div class="signature-box">
            <strong>Authorized Signature</strong><br>
            <small>{{ $installment->collectedBy->name ?? 'Field Officer' }}</small>
        </div>
    </div>

    <div class="footer">
        <p><strong>Thank you for your payment!</strong></p>
        <p>This is a computer-generated receipt. Please keep this receipt for your records.</p>
        <p>For any queries, please contact your field officer or visit the branch.</p>
        <p style="margin-top: 10px;">
            <strong>Sonali Microfinance</strong><br>
            Empowering Communities Through Financial Inclusion
        </p>
        <p style="font-size: 10px; margin-top: 15px;">
            Generated on: {{ $generated_at->format('F d, Y \a\t g:i A') }}
        </p>
    </div>
</body>
</html>
