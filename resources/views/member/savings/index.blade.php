@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">My Savings</h1>
            <p class="text-gray-600">Track your savings accounts and financial progress</p>
        </div>

        <!-- Savings Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-wallet text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Balance</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['total_balance'], 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-list text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Accounts</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['active_accounts'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-arrow-up text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Deposits</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['total_deposits'], 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-calendar-alt text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">This Month</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['monthly_deposits'], 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Account Types Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <a href="#all" class="tab-link active" data-tab="all">
                    <i class="fas fa-list mr-2"></i>
                    All Accounts
                </a>
                @foreach($accountsByType as $type => $accounts)
                    <a href="#{{ $type }}" class="tab-link" data-tab="{{ $type }}">
                        <i class="fas fa-{{ $type === 'general' ? 'wallet' : ($type === 'fixed' ? 'lock' : 'calendar-check') }} mr-2"></i>
                        {{ ucfirst($type) }} ({{ $accounts->count() }})
                    </a>
                @endforeach
            </nav>
        </div>

        <!-- All Accounts Tab -->
        <div id="all-tab" class="tab-content active">
            <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                @forelse($savingAccounts as $account)
                    <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center">
                                    <div class="w-10 h-10 bg-{{ $account->account_type === 'general' ? 'green' : ($account->account_type === 'fixed' ? 'blue' : 'purple') }}-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-{{ $account->account_type === 'general' ? 'wallet' : ($account->account_type === 'fixed' ? 'lock' : 'calendar-check') }} text-white"></i>
                                    </div>
                                    <div class="ml-3">
                                        <h3 class="text-lg font-medium text-gray-900">{{ $account->account_number }}</h3>
                                        <p class="text-sm text-gray-500">{{ ucfirst($account->account_type) }} Account</p>
                                    </div>
                                </div>
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {{ $account->status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800' }}">
                                    {{ ucfirst($account->status) }}
                                </span>
                            </div>

                            <div class="space-y-3">
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Current Balance</span>
                                    <span class="text-lg font-semibold text-green-600">৳{{ number_format($account->current_balance, 2) }}</span>
                                </div>
                                
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Interest Rate</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $account->interest_rate }}% per year</span>
                                </div>
                                
                                <div class="flex justify-between">
                                    <span class="text-sm text-gray-500">Opened Date</span>
                                    <span class="text-sm font-medium text-gray-900">{{ $account->opened_date->format('M d, Y') }}</span>
                                </div>

                                @if($account->minimum_balance > 0)
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">Minimum Balance</span>
                                        <span class="text-sm font-medium text-gray-900">৳{{ number_format($account->minimum_balance, 2) }}</span>
                                    </div>
                                @endif
                            </div>

                            <!-- Recent Transactions Preview -->
                            @if($account->transactions->count() > 0)
                                <div class="mt-4 pt-4 border-t border-gray-200">
                                    <h4 class="text-sm font-medium text-gray-900 mb-2">Recent Transactions</h4>
                                    <div class="space-y-2">
                                        @foreach($account->transactions->take(3) as $transaction)
                                            <div class="flex justify-between items-center text-sm">
                                                <div class="flex items-center">
                                                    <span class="w-2 h-2 rounded-full {{ $transaction->transaction_type === 'deposit' ? 'bg-green-400' : 'bg-red-400' }} mr-2"></span>
                                                    <span class="text-gray-600">{{ $transaction->transaction_date->format('M d') }}</span>
                                                </div>
                                                <span class="font-medium {{ $transaction->transaction_type === 'deposit' ? 'text-green-600' : 'text-red-600' }}">
                                                    {{ $transaction->transaction_type === 'deposit' ? '+' : '-' }}৳{{ number_format($transaction->amount, 2) }}
                                                </span>
                                            </div>
                                        @endforeach
                                    </div>
                                </div>
                            @endif

                            <div class="mt-6 flex flex-wrap gap-2">
                                <a href="{{ route('member.savings.show', $account) }}" class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </a>
                                <a href="{{ route('member.savings.transactions', $account) }}" class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-list mr-2"></i>
                                    Transactions
                                </a>
                            </div>
                        </div>
                    </div>
                @empty
                    <div class="col-span-full text-center py-12">
                        <i class="fas fa-piggy-bank text-gray-300 text-5xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Savings Accounts</h3>
                        <p class="text-gray-500">You don't have any savings accounts yet.</p>
                    </div>
                @endforelse
            </div>
        </div>

        <!-- Account Type Specific Tabs -->
        @foreach($accountsByType as $type => $accounts)
            <div id="{{ $type }}-tab" class="tab-content">
                <div class="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
                    @foreach($accounts as $account)
                        <div class="bg-white overflow-hidden shadow rounded-lg hover:shadow-lg transition-shadow duration-200">
                            <div class="p-6">
                                <div class="flex items-center justify-between mb-4">
                                    <div class="flex items-center">
                                        <div class="w-10 h-10 bg-{{ $type === 'general' ? 'green' : ($type === 'fixed' ? 'blue' : 'purple') }}-500 rounded-full flex items-center justify-center">
                                            <i class="fas fa-{{ $type === 'general' ? 'wallet' : ($type === 'fixed' ? 'lock' : 'calendar-check') }} text-white"></i>
                                        </div>
                                        <div class="ml-3">
                                            <h3 class="text-lg font-medium text-gray-900">{{ $account->account_number }}</h3>
                                            <p class="text-sm text-gray-500">{{ ucfirst($account->account_type) }} Account</p>
                                        </div>
                                    </div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                        {{ ucfirst($account->status) }}
                                    </span>
                                </div>

                                <div class="space-y-3">
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">Current Balance</span>
                                        <span class="text-lg font-semibold text-green-600">৳{{ number_format($account->current_balance, 2) }}</span>
                                    </div>
                                    
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">Interest Rate</span>
                                        <span class="text-sm font-medium text-gray-900">{{ $account->interest_rate }}% per year</span>
                                    </div>
                                    
                                    <div class="flex justify-between">
                                        <span class="text-sm text-gray-500">Opened Date</span>
                                        <span class="text-sm font-medium text-gray-900">{{ $account->opened_date->format('M d, Y') }}</span>
                                    </div>
                                </div>

                                <div class="mt-6 flex flex-wrap gap-2">
                                    <a href="{{ route('member.savings.show', $account) }}" class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <i class="fas fa-eye mr-2"></i>
                                        View Details
                                    </a>
                                    <a href="{{ route('member.savings.download-statement', $account) }}" class="flex-1 inline-flex justify-center items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                        <i class="fas fa-download mr-2"></i>
                                        Statement
                                    </a>
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endforeach

        <!-- Quick Actions -->
        <div class="mt-8 bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <a href="{{ route('member.savings.goals') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200">
                        <i class="fas fa-bullseye text-blue-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-700">Savings Goals</h4>
                            <p class="text-xs text-blue-600">Track your financial goals</p>
                        </div>
                    </a>
                    <a href="{{ route('member.profile.index') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
                        <i class="fas fa-user text-green-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-green-700">Update Profile</h4>
                            <p class="text-xs text-green-600">Manage your information</p>
                        </div>
                    </a>
                    <a href="{{ route('member.installments.index') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-200">
                        <i class="fas fa-calendar-alt text-purple-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-purple-700">Payment Schedule</h4>
                            <p class="text-xs text-purple-600">View upcoming payments</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.tab-link {
    @apply text-gray-500 hover:text-gray-700 px-3 py-2 font-medium text-sm rounded-md flex items-center;
}

.tab-link.active {
    @apply text-blue-600 bg-blue-50;
}

.tab-content {
    @apply hidden;
}

.tab-content.active {
    @apply block;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs
            tabLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab') + '-tab';
            document.getElementById(tabId).classList.add('active');
        });
    });
});
</script>
@endsection
