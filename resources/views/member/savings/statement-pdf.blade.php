<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Savings Statement - {{ $savingAccount->account_number }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #2563eb;
            font-size: 24px;
        }
        .header h2 {
            margin: 5px 0;
            color: #666;
            font-size: 16px;
        }
        .account-info {
            margin-bottom: 20px;
        }
        .account-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .account-info td {
            padding: 5px;
            border: 1px solid #ddd;
        }
        .account-info td:first-child {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 30%;
        }
        .period-info {
            background-color: #e3f2fd;
            padding: 10px;
            margin-bottom: 20px;
            border: 1px solid #2196f3;
            text-align: center;
        }
        .transactions-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .transactions-table th,
        .transactions-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .transactions-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .transactions-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .deposit {
            color: #16a34a;
            font-weight: bold;
        }
        .withdrawal {
            color: #dc2626;
            font-weight: bold;
        }
        .summary {
            margin-top: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #ddd;
        }
        .summary table {
            width: 100%;
            border-collapse: collapse;
        }
        .summary td {
            padding: 5px;
            border-bottom: 1px solid #ddd;
        }
        .summary td:first-child {
            font-weight: bold;
            width: 70%;
        }
        .summary td:last-child {
            text-align: right;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
        .no-transactions {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sonali Microfinance</h1>
        <h2>Savings Account Statement</h2>
        <p>Generated on: {{ $generated_at->format('F d, Y \a\t g:i A') }}</p>
    </div>

    <div class="account-info">
        <h3>Account Information</h3>
        <table>
            <tr>
                <td>Account Holder</td>
                <td>{{ $member->name }}</td>
            </tr>
            <tr>
                <td>Member ID</td>
                <td>{{ $member->member_id }}</td>
            </tr>
            <tr>
                <td>Account Number</td>
                <td>{{ $savingAccount->account_number }}</td>
            </tr>
            <tr>
                <td>Account Type</td>
                <td>{{ ucfirst($savingAccount->account_type) }} Savings Account</td>
            </tr>
            <tr>
                <td>Branch</td>
                <td>{{ $member->branch->name ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td>Account Opened</td>
                <td>{{ $savingAccount->opened_date->format('F d, Y') }}</td>
            </tr>
            <tr>
                <td>Interest Rate</td>
                <td>{{ $savingAccount->interest_rate }}% per year</td>
            </tr>
            <tr>
                <td>Minimum Balance</td>
                <td>৳{{ number_format($savingAccount->minimum_balance, 2) }}</td>
            </tr>
        </table>
    </div>

    <div class="period-info">
        <strong>Statement Period: {{ $from_date->format('F d, Y') }} to {{ $to_date->format('F d, Y') }}</strong>
    </div>

    @if($transactions->count() > 0)
        <h3>Transaction History</h3>
        <table class="transactions-table">
            <thead>
                <tr>
                    <th>Date</th>
                    <th>Description</th>
                    <th>Reference</th>
                    <th>Deposit</th>
                    <th>Withdrawal</th>
                    <th>Balance</th>
                    <th>Entered By</th>
                </tr>
            </thead>
            <tbody>
                @foreach($transactions as $transaction)
                    <tr>
                        <td>{{ $transaction->transaction_date->format('M d, Y') }}</td>
                        <td>{{ $transaction->description }}</td>
                        <td>{{ $transaction->reference_number ?? '-' }}</td>
                        <td>
                            @if($transaction->transaction_type === 'deposit')
                                <span class="deposit">৳{{ number_format($transaction->amount, 2) }}</span>
                            @else
                                -
                            @endif
                        </td>
                        <td>
                            @if($transaction->transaction_type === 'withdrawal')
                                <span class="withdrawal">৳{{ number_format($transaction->amount, 2) }}</span>
                            @else
                                -
                            @endif
                        </td>
                        <td>৳{{ number_format($transaction->balance_after, 2) }}</td>
                        <td>{{ $transaction->enteredBy->name ?? 'System' }}</td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-transactions">
            <h3>No Transactions Found</h3>
            <p>No transactions were recorded during the selected period.</p>
        </div>
    @endif

    <div class="summary">
        <h3>Statement Summary</h3>
        <table>
            <tr>
                <td>Opening Balance ({{ $from_date->format('M d, Y') }})</td>
                <td>৳{{ number_format($summary['opening_balance'], 2) }}</td>
            </tr>
            <tr>
                <td>Total Deposits</td>
                <td>৳{{ number_format($summary['total_deposits'], 2) }}</td>
            </tr>
            <tr>
                <td>Total Withdrawals</td>
                <td>৳{{ number_format($summary['total_withdrawals'], 2) }}</td>
            </tr>
            <tr>
                <td>Net Change</td>
                <td>৳{{ number_format($summary['total_deposits'] - $summary['total_withdrawals'], 2) }}</td>
            </tr>
            <tr>
                <td>Closing Balance ({{ $to_date->format('M d, Y') }})</td>
                <td>৳{{ number_format($summary['closing_balance'], 2) }}</td>
            </tr>
            <tr>
                <td>Number of Transactions</td>
                <td>{{ $summary['transaction_count'] }}</td>
            </tr>
        </table>
    </div>

    @if($savingAccount->interest_rate > 0)
        <div class="summary" style="margin-top: 10px;">
            <h3>Interest Information</h3>
            <table>
                <tr>
                    <td>Annual Interest Rate</td>
                    <td>{{ $savingAccount->interest_rate }}%</td>
                </tr>
                <tr>
                    <td>Estimated Annual Interest (on current balance)</td>
                    <td>৳{{ number_format(($savingAccount->current_balance * $savingAccount->interest_rate) / 100, 2) }}</td>
                </tr>
                <tr>
                    <td>Estimated Monthly Interest (on current balance)</td>
                    <td>৳{{ number_format((($savingAccount->current_balance * $savingAccount->interest_rate) / 100) / 12, 2) }}</td>
                </tr>
            </table>
            <p style="font-size: 10px; color: #666; margin-top: 10px;">
                * Interest calculations are estimates based on current balance and may vary based on actual daily balances and bank policies.
            </p>
        </div>
    @endif

    <div class="footer">
        <p>This is a computer-generated statement. For any queries, please contact your field officer or visit the branch.</p>
        <p><strong>Important:</strong> Please verify all transactions and report any discrepancies within 30 days.</p>
        <p>Sonali Microfinance - Empowering Communities Through Financial Inclusion</p>
    </div>
</body>
</html>
