@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">My Loans</h1>
            <p class="text-gray-600">Manage your loan applications and track active loans</p>
        </div>

        <!-- Loan Statistics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-list text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Loans</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['total_loans'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Loans</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $stats['active_loans'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-money-bill-wave text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Borrowed</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['total_borrowed'], 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-exclamation-triangle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Outstanding</dt>
                                <dd class="text-lg font-medium text-gray-900">৳{{ number_format($stats['outstanding_amount'], 2) }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tabs -->
        <div class="mb-6">
            <nav class="flex space-x-8" aria-label="Tabs">
                <a href="#loans" class="tab-link active" data-tab="loans">
                    <i class="fas fa-hand-holding-usd mr-2"></i>
                    My Loans
                </a>
                <a href="#applications" class="tab-link" data-tab="applications">
                    <i class="fas fa-file-alt mr-2"></i>
                    Applications
                </a>
            </nav>
        </div>

        <!-- Loans Tab -->
        <div id="loans-tab" class="tab-content active">
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Active Loans</h3>
                    
                    @forelse($loans as $loan)
                        <div class="border border-gray-200 rounded-lg p-4 mb-4 hover:shadow-md transition-shadow duration-200">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h4 class="text-lg font-medium text-gray-900">Loan #{{ $loan->id }}</h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                            Active
                                        </span>
                                    </div>
                                    
                                    <div class="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm text-gray-500">Loan Amount</p>
                                            <p class="text-lg font-semibold text-blue-600">৳{{ number_format($loan->loan_amount, 2) }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">Interest Rate</p>
                                            <p class="text-lg font-semibold text-gray-900">{{ $loan->interest_rate }}%</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">Duration</p>
                                            <p class="text-lg font-semibold text-gray-900">{{ $loan->loan_duration_months }} months</p>
                                        </div>
                                    </div>

                                    <div class="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm text-gray-500">Loan Date</p>
                                            <p class="text-sm font-medium text-gray-900">{{ $loan->loan_date->format('M d, Y') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">Remaining Amount</p>
                                            <p class="text-sm font-medium text-red-600">৳{{ number_format($loan->calculateRemainingAmount(), 2) }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">Next Payment</p>
                                            @if($loan->getNextInstallmentDate())
                                                <p class="text-sm font-medium text-yellow-600">{{ $loan->getNextInstallmentDate()->format('M d, Y') }}</p>
                                            @else
                                                <p class="text-sm font-medium text-green-600">Completed</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4 flex flex-wrap gap-2">
                                <a href="{{ route('member.loans.show', $loan) }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Details
                                </a>
                                <a href="{{ route('member.loans.schedule', $loan) }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-calendar-alt mr-2"></i>
                                    Payment Schedule
                                </a>
                                <a href="{{ route('member.loans.download-statement', $loan) }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-download mr-2"></i>
                                    Download Statement
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <i class="fas fa-hand-holding-usd text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Active Loans</h3>
                            <p class="text-gray-500">You don't have any active loans at the moment.</p>
                        </div>
                    @endforelse

                    @if($loans->hasPages())
                        <div class="mt-6">
                            {{ $loans->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Applications Tab -->
        <div id="applications-tab" class="tab-content">
            <div class="bg-white shadow overflow-hidden sm:rounded-md">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Loan Applications</h3>
                    
                    @forelse($applications as $application)
                        <div class="border border-gray-200 rounded-lg p-4 mb-4">
                            <div class="flex items-center justify-between">
                                <div class="flex-1">
                                    <div class="flex items-center justify-between">
                                        <h4 class="text-lg font-medium text-gray-900">Application #{{ $application->id }}</h4>
                                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                            @if($application->status === 'pending') bg-yellow-100 text-yellow-800
                                            @elseif($application->status === 'approved') bg-green-100 text-green-800
                                            @elseif($application->status === 'rejected') bg-red-100 text-red-800
                                            @else bg-gray-100 text-gray-800 @endif">
                                            {{ ucfirst($application->status) }}
                                        </span>
                                    </div>
                                    
                                    <div class="mt-2 grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div>
                                            <p class="text-sm text-gray-500">Requested Amount</p>
                                            <p class="text-lg font-semibold text-blue-600">৳{{ number_format($application->requested_amount, 2) }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">Application Date</p>
                                            <p class="text-sm font-medium text-gray-900">{{ $application->application_date->format('M d, Y') }}</p>
                                        </div>
                                        <div>
                                            <p class="text-sm text-gray-500">Purpose</p>
                                            <p class="text-sm font-medium text-gray-900">{{ $application->purpose }}</p>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="mt-4">
                                <a href="{{ route('member.loans.application-show', $application) }}" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                                    <i class="fas fa-eye mr-2"></i>
                                    View Application
                                </a>
                            </div>
                        </div>
                    @empty
                        <div class="text-center py-8">
                            <i class="fas fa-file-alt text-gray-300 text-4xl mb-4"></i>
                            <h3 class="text-lg font-medium text-gray-900 mb-2">No Applications</h3>
                            <p class="text-gray-500">You haven't submitted any loan applications yet.</p>
                        </div>
                    @endforelse

                    @if($applications->hasPages())
                        <div class="mt-6">
                            {{ $applications->links() }}
                        </div>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.tab-link {
    @apply text-gray-500 hover:text-gray-700 px-3 py-2 font-medium text-sm rounded-md flex items-center;
}

.tab-link.active {
    @apply text-blue-600 bg-blue-50;
}

.tab-content {
    @apply hidden;
}

.tab-content.active {
    @apply block;
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const tabLinks = document.querySelectorAll('.tab-link');
    const tabContents = document.querySelectorAll('.tab-content');

    tabLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all tabs
            tabLinks.forEach(l => l.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));
            
            // Add active class to clicked tab
            this.classList.add('active');
            const tabId = this.getAttribute('data-tab') + '-tab';
            document.getElementById(tabId).classList.add('active');
        });
    });
});
</script>
@endsection
