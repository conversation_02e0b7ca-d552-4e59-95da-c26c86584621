<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Loan Statement - {{ $loan->id }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
        }
        .header h1 {
            margin: 0;
            color: #2563eb;
            font-size: 24px;
        }
        .header h2 {
            margin: 5px 0;
            color: #666;
            font-size: 16px;
        }
        .member-info {
            margin-bottom: 20px;
        }
        .member-info table {
            width: 100%;
            border-collapse: collapse;
        }
        .member-info td {
            padding: 5px;
            border: 1px solid #ddd;
        }
        .member-info td:first-child {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 30%;
        }
        .loan-details {
            margin-bottom: 20px;
        }
        .loan-details table {
            width: 100%;
            border-collapse: collapse;
        }
        .loan-details td {
            padding: 8px;
            border: 1px solid #ddd;
        }
        .loan-details td:first-child {
            background-color: #f8f9fa;
            font-weight: bold;
            width: 30%;
        }
        .installments-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 20px;
        }
        .installments-table th,
        .installments-table td {
            padding: 8px;
            border: 1px solid #ddd;
            text-align: left;
        }
        .installments-table th {
            background-color: #f8f9fa;
            font-weight: bold;
        }
        .installments-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .status-paid {
            color: #16a34a;
            font-weight: bold;
        }
        .status-pending {
            color: #eab308;
            font-weight: bold;
        }
        .status-overdue {
            color: #dc2626;
            font-weight: bold;
        }
        .summary {
            margin-top: 20px;
            background-color: #f8f9fa;
            padding: 15px;
            border: 1px solid #ddd;
        }
        .summary table {
            width: 100%;
            border-collapse: collapse;
        }
        .summary td {
            padding: 5px;
            border-bottom: 1px solid #ddd;
        }
        .summary td:first-child {
            font-weight: bold;
            width: 70%;
        }
        .summary td:last-child {
            text-align: right;
            font-weight: bold;
        }
        .footer {
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 10px;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Sonali Microfinance</h1>
        <h2>Loan Statement</h2>
        <p>Generated on: {{ $generated_at->format('F d, Y \a\t g:i A') }}</p>
    </div>

    <div class="member-info">
        <h3>Member Information</h3>
        <table>
            <tr>
                <td>Member Name</td>
                <td>{{ $member->name }}</td>
            </tr>
            <tr>
                <td>Member ID</td>
                <td>{{ $member->member_id }}</td>
            </tr>
            <tr>
                <td>Branch</td>
                <td>{{ $member->branch->name ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td>Phone Number</td>
                <td>{{ $member->phone_number ?? 'N/A' }}</td>
            </tr>
            <tr>
                <td>Address</td>
                <td>{{ $member->present_address ?? 'N/A' }}</td>
            </tr>
        </table>
    </div>

    <div class="loan-details">
        <h3>Loan Details</h3>
        <table>
            <tr>
                <td>Loan ID</td>
                <td>{{ $loan->id }}</td>
            </tr>
            <tr>
                <td>Loan Amount</td>
                <td>৳{{ number_format($loan->loan_amount, 2) }}</td>
            </tr>
            <tr>
                <td>Interest Rate</td>
                <td>{{ $loan->interest_rate }}% per year</td>
            </tr>
            <tr>
                <td>Loan Duration</td>
                <td>{{ $loan->loan_duration_months }} months</td>
            </tr>
            <tr>
                <td>Repayment Method</td>
                <td>{{ ucfirst(str_replace('_', ' ', $loan->repayment_method)) }}</td>
            </tr>
            <tr>
                <td>Loan Date</td>
                <td>{{ $loan->loan_date->format('F d, Y') }}</td>
            </tr>
            <tr>
                <td>First Installment Date</td>
                <td>{{ $loan->first_installment_date->format('F d, Y') }}</td>
            </tr>
            <tr>
                <td>Last Installment Date</td>
                <td>{{ $loan->last_installment_date->format('F d, Y') }}</td>
            </tr>
            @if($loan->advance_payment > 0)
            <tr>
                <td>Advance Payment</td>
                <td>৳{{ number_format($loan->advance_payment, 2) }}</td>
            </tr>
            @endif
        </table>
    </div>

    <h3>Installment Schedule</h3>
    <table class="installments-table">
        <thead>
            <tr>
                <th>Installment #</th>
                <th>Due Date</th>
                <th>Amount</th>
                <th>Late Fee</th>
                <th>Total Due</th>
                <th>Status</th>
                <th>Collection Date</th>
                <th>Collected By</th>
            </tr>
        </thead>
        <tbody>
            @php
                $totalAmount = 0;
                $totalPaid = 0;
                $totalLateFees = 0;
                $paidCount = 0;
                $pendingCount = 0;
                $overdueCount = 0;
            @endphp
            @foreach($installments as $installment)
                @php
                    $totalAmount += $installment->amount;
                    $lateFee = $installment->status === 'paid' ? $installment->late_fee : $installment->calculateLateFee();
                    $totalLateFees += $lateFee;
                    
                    if ($installment->status === 'paid') {
                        $totalPaid += $installment->amount + $lateFee;
                        $paidCount++;
                    } elseif ($installment->installment_date < now()) {
                        $overdueCount++;
                    } else {
                        $pendingCount++;
                    }
                @endphp
                <tr>
                    <td>{{ $installment->installment_no }}</td>
                    <td>{{ $installment->installment_date->format('M d, Y') }}</td>
                    <td>৳{{ number_format($installment->amount, 2) }}</td>
                    <td>৳{{ number_format($lateFee, 2) }}</td>
                    <td>৳{{ number_format($installment->amount + $lateFee, 2) }}</td>
                    <td>
                        @if($installment->status === 'paid')
                            <span class="status-paid">PAID</span>
                        @elseif($installment->installment_date < now())
                            <span class="status-overdue">OVERDUE</span>
                        @else
                            <span class="status-pending">PENDING</span>
                        @endif
                    </td>
                    <td>
                        {{ $installment->collection_date ? $installment->collection_date->format('M d, Y') : '-' }}
                    </td>
                    <td>
                        {{ $installment->collectedBy->name ?? '-' }}
                    </td>
                </tr>
            @endforeach
        </tbody>
    </table>

    <div class="summary">
        <h3>Loan Summary</h3>
        <table>
            <tr>
                <td>Total Loan Amount</td>
                <td>৳{{ number_format($loan->loan_amount, 2) }}</td>
            </tr>
            <tr>
                <td>Total Installment Amount (with interest)</td>
                <td>৳{{ number_format($totalAmount, 2) }}</td>
            </tr>
            <tr>
                <td>Total Late Fees</td>
                <td>৳{{ number_format($totalLateFees, 2) }}</td>
            </tr>
            <tr>
                <td>Total Amount Payable</td>
                <td>৳{{ number_format($totalAmount + $totalLateFees, 2) }}</td>
            </tr>
            <tr>
                <td>Total Paid</td>
                <td>৳{{ number_format($totalPaid, 2) }}</td>
            </tr>
            <tr>
                <td>Remaining Balance</td>
                <td>৳{{ number_format(($totalAmount + $totalLateFees) - $totalPaid, 2) }}</td>
            </tr>
            <tr>
                <td colspan="2" style="border-bottom: none; padding-top: 10px;"><strong>Installment Status:</strong></td>
            </tr>
            <tr>
                <td>Paid Installments</td>
                <td>{{ $paidCount }}</td>
            </tr>
            <tr>
                <td>Pending Installments</td>
                <td>{{ $pendingCount }}</td>
            </tr>
            <tr>
                <td>Overdue Installments</td>
                <td>{{ $overdueCount }}</td>
            </tr>
            <tr>
                <td>Total Installments</td>
                <td>{{ $installments->count() }}</td>
            </tr>
        </table>
    </div>

    <div class="footer">
        <p>This is a computer-generated statement. For any queries, please contact your field officer or visit the branch.</p>
        <p>Sonali Microfinance - Empowering Communities Through Financial Inclusion</p>
    </div>
</body>
</html>
