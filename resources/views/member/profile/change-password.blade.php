@extends('layouts.app')

@section('content')
<div class="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Change Password</h1>
                    <p class="text-gray-600">Update your account password for security</p>
                </div>
                <a href="{{ route('member.profile.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Profile
                </a>
            </div>
        </div>

        <!-- Security Tips -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-8">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-shield-alt text-blue-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-blue-800">Password Security Tips</h3>
                    <div class="mt-2 text-sm text-blue-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Use at least 8 characters</li>
                            <li>Include uppercase and lowercase letters</li>
                            <li>Include at least one number</li>
                            <li>Include at least one special character</li>
                            <li>Don't use personal information</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Change Password Form -->
        <div class="bg-white shadow rounded-lg">
            <form action="{{ route('member.profile.update-password') }}" method="POST">
                @csrf
                @method('PUT')
                
                <div class="px-4 py-5 sm:p-6">
                    <div class="space-y-6">
                        <!-- Current Password -->
                        <div>
                            <label for="current_password" class="block text-sm font-medium text-gray-700">
                                Current Password *
                            </label>
                            <div class="mt-1 relative">
                                <input type="password" name="current_password" id="current_password" required class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 pr-10">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('current_password')">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="current_password_icon"></i>
                                </button>
                            </div>
                            @error('current_password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                        </div>

                        <!-- New Password -->
                        <div>
                            <label for="password" class="block text-sm font-medium text-gray-700">
                                New Password *
                            </label>
                            <div class="mt-1 relative">
                                <input type="password" name="password" id="password" required class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 pr-10">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('password')">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="password_icon"></i>
                                </button>
                            </div>
                            @error('password')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            
                            <!-- Password Strength Indicator -->
                            <div class="mt-2">
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-500">Password strength:</span>
                                    <div class="flex space-x-1">
                                        <div class="h-2 w-8 bg-gray-200 rounded" id="strength-1"></div>
                                        <div class="h-2 w-8 bg-gray-200 rounded" id="strength-2"></div>
                                        <div class="h-2 w-8 bg-gray-200 rounded" id="strength-3"></div>
                                        <div class="h-2 w-8 bg-gray-200 rounded" id="strength-4"></div>
                                    </div>
                                    <span class="text-sm" id="strength-text">Weak</span>
                                </div>
                            </div>
                        </div>

                        <!-- Confirm New Password -->
                        <div>
                            <label for="password_confirmation" class="block text-sm font-medium text-gray-700">
                                Confirm New Password *
                            </label>
                            <div class="mt-1 relative">
                                <input type="password" name="password_confirmation" id="password_confirmation" required class="block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500 pr-10">
                                <button type="button" class="absolute inset-y-0 right-0 pr-3 flex items-center" onclick="togglePassword('password_confirmation')">
                                    <i class="fas fa-eye text-gray-400 hover:text-gray-600" id="password_confirmation_icon"></i>
                                </button>
                            </div>
                            @error('password_confirmation')
                                <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                            @enderror
                            
                            <!-- Password Match Indicator -->
                            <div class="mt-2" id="password-match" style="display: none;">
                                <div class="flex items-center">
                                    <i class="fas fa-check text-green-500 mr-2" id="match-icon"></i>
                                    <span class="text-sm text-green-600" id="match-text">Passwords match</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 rounded-b-lg">
                    <div class="flex justify-end space-x-3">
                        <a href="{{ route('member.profile.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-key mr-2"></i>
                            Update Password
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Security Information -->
        <div class="mt-8 bg-white shadow rounded-lg p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Account Security</h3>
            <div class="space-y-4">
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-lock text-green-500 mt-1"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-gray-900">Password Protection</h4>
                        <p class="text-sm text-gray-600">Your password is encrypted and stored securely. We never store your password in plain text.</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-user-shield text-blue-500 mt-1"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-gray-900">Account Access</h4>
                        <p class="text-sm text-gray-600">Only you should know your password. Never share it with anyone, including staff members.</p>
                    </div>
                </div>
                
                <div class="flex items-start">
                    <div class="flex-shrink-0">
                        <i class="fas fa-exclamation-triangle text-yellow-500 mt-1"></i>
                    </div>
                    <div class="ml-3">
                        <h4 class="text-sm font-medium text-gray-900">Forgot Password?</h4>
                        <p class="text-sm text-gray-600">If you forget your password, contact your field officer or visit the branch for assistance.</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function togglePassword(fieldId) {
    const field = document.getElementById(fieldId);
    const icon = document.getElementById(fieldId + '_icon');
    
    if (field.type === 'password') {
        field.type = 'text';
        icon.classList.remove('fa-eye');
        icon.classList.add('fa-eye-slash');
    } else {
        field.type = 'password';
        icon.classList.remove('fa-eye-slash');
        icon.classList.add('fa-eye');
    }
}

function checkPasswordStrength(password) {
    let strength = 0;
    const checks = [
        /.{8,}/, // At least 8 characters
        /[a-z]/, // Lowercase letter
        /[A-Z]/, // Uppercase letter
        /[0-9]/, // Number
        /[^A-Za-z0-9]/ // Special character
    ];
    
    checks.forEach(check => {
        if (check.test(password)) strength++;
    });
    
    return strength;
}

function updatePasswordStrength() {
    const password = document.getElementById('password').value;
    const strength = checkPasswordStrength(password);
    
    // Reset all strength indicators
    for (let i = 1; i <= 4; i++) {
        const indicator = document.getElementById(`strength-${i}`);
        indicator.className = 'h-2 w-8 bg-gray-200 rounded';
    }
    
    // Update strength indicators
    const colors = ['bg-red-500', 'bg-yellow-500', 'bg-blue-500', 'bg-green-500'];
    const texts = ['Very Weak', 'Weak', 'Good', 'Strong'];
    
    if (password.length > 0) {
        const level = Math.min(Math.max(strength - 1, 0), 3);
        
        for (let i = 0; i <= level; i++) {
            document.getElementById(`strength-${i + 1}`).className = `h-2 w-8 ${colors[level]} rounded`;
        }
        
        document.getElementById('strength-text').textContent = texts[level];
        document.getElementById('strength-text').className = `text-sm ${level < 2 ? 'text-red-600' : level < 3 ? 'text-yellow-600' : 'text-green-600'}`;
    } else {
        document.getElementById('strength-text').textContent = 'Weak';
        document.getElementById('strength-text').className = 'text-sm text-gray-500';
    }
}

function checkPasswordMatch() {
    const password = document.getElementById('password').value;
    const confirmation = document.getElementById('password_confirmation').value;
    const matchDiv = document.getElementById('password-match');
    const matchIcon = document.getElementById('match-icon');
    const matchText = document.getElementById('match-text');
    
    if (confirmation.length > 0) {
        matchDiv.style.display = 'block';
        
        if (password === confirmation) {
            matchIcon.className = 'fas fa-check text-green-500 mr-2';
            matchText.textContent = 'Passwords match';
            matchText.className = 'text-sm text-green-600';
        } else {
            matchIcon.className = 'fas fa-times text-red-500 mr-2';
            matchText.textContent = 'Passwords do not match';
            matchText.className = 'text-sm text-red-600';
        }
    } else {
        matchDiv.style.display = 'none';
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const passwordField = document.getElementById('password');
    const confirmationField = document.getElementById('password_confirmation');
    
    passwordField.addEventListener('input', function() {
        updatePasswordStrength();
        checkPasswordMatch();
    });
    
    confirmationField.addEventListener('input', checkPasswordMatch);
});
</script>
@endsection
