@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Emergency Contacts</h1>
                    <p class="text-gray-600">Important contact information for emergencies</p>
                </div>
                <a href="{{ route('member.profile.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Profile
                </a>
            </div>
        </div>

        <!-- Emergency Contacts List -->
        <div class="space-y-6">
            @forelse($emergencyContacts as $contact)
                <div class="bg-white shadow rounded-lg overflow-hidden">
                    <div class="px-4 py-5 sm:p-6">
                        <div class="flex items-start justify-between">
                            <div class="flex items-start space-x-4">
                                <div class="flex-shrink-0">
                                    <div class="w-12 h-12 bg-{{ $contact['relationship'] === 'Reference' ? 'blue' : 'green' }}-500 rounded-full flex items-center justify-center">
                                        <i class="fas fa-{{ $contact['relationship'] === 'Reference' ? 'user-friends' : 'user-tie' }} text-white"></i>
                                    </div>
                                </div>
                                <div class="flex-1">
                                    <h3 class="text-lg font-medium text-gray-900">{{ $contact['name'] }}</h3>
                                    <p class="text-sm text-gray-600">{{ $contact['relationship'] }}</p>
                                    
                                    <div class="mt-4 space-y-2">
                                        <div class="flex items-center">
                                            <i class="fas fa-phone text-gray-400 w-5 h-5 mr-3"></i>
                                            <span class="text-sm text-gray-900">{{ $contact['phone'] ?? 'N/A' }}</span>
                                        </div>
                                        
                                        <div class="flex items-start">
                                            <i class="fas fa-map-marker-alt text-gray-400 w-5 h-5 mr-3 mt-0.5"></i>
                                            <span class="text-sm text-gray-900">{{ $contact['address'] ?? 'N/A' }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="flex-shrink-0">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    {{ $contact['relationship'] === 'Reference' ? 'bg-blue-100 text-blue-800' : 'bg-green-100 text-green-800' }}">
                                    {{ $contact['relationship'] }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            @empty
                <div class="bg-white shadow rounded-lg">
                    <div class="px-4 py-12 text-center">
                        <i class="fas fa-address-book text-gray-300 text-5xl mb-4"></i>
                        <h3 class="text-lg font-medium text-gray-900 mb-2">No Emergency Contacts</h3>
                        <p class="text-gray-500">No emergency contacts have been set up for your account.</p>
                    </div>
                </div>
            @endforelse
        </div>

        <!-- Important Information -->
        <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-triangle text-yellow-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-yellow-800">Important Information</h3>
                    <div class="mt-2 text-sm text-yellow-700">
                        <ul class="list-disc list-inside space-y-1">
                            <li>Emergency contacts are used to reach you in case of urgent matters</li>
                            <li>Please ensure the contact information is current and accurate</li>
                            <li>To update emergency contacts, please contact your field officer</li>
                            <li>These contacts may be used for loan verification purposes</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Branch Contact Information -->
        <div class="mt-8 bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Branch Contact Information</h3>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Your Branch</h4>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <i class="fas fa-building text-gray-400 w-5 h-5 mr-3"></i>
                                <span class="text-sm text-gray-900">{{ $member->branch->name ?? 'N/A' }}</span>
                            </div>
                            
                            @if($member->branch && $member->branch->address)
                                <div class="flex items-start">
                                    <i class="fas fa-map-marker-alt text-gray-400 w-5 h-5 mr-3 mt-0.5"></i>
                                    <span class="text-sm text-gray-900">{{ $member->branch->address }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                    
                    <div>
                        <h4 class="text-sm font-medium text-gray-900 mb-2">Your Field Officer</h4>
                        <div class="space-y-2">
                            <div class="flex items-center">
                                <i class="fas fa-user text-gray-400 w-5 h-5 mr-3"></i>
                                <span class="text-sm text-gray-900">{{ $member->createdBy->name ?? 'N/A' }}</span>
                            </div>
                            
                            @if($member->createdBy && $member->createdBy->email)
                                <div class="flex items-center">
                                    <i class="fas fa-envelope text-gray-400 w-5 h-5 mr-3"></i>
                                    <span class="text-sm text-gray-900">{{ $member->createdBy->email }}</span>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Emergency Procedures -->
        <div class="mt-8 bg-red-50 border border-red-200 rounded-lg p-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Emergency Procedures</h3>
                    <div class="mt-2 text-sm text-red-700">
                        <p class="mb-2">In case of emergency or urgent matters:</p>
                        <ol class="list-decimal list-inside space-y-1">
                            <li>Contact your field officer first</li>
                            <li>If unavailable, contact the branch directly</li>
                            <li>For after-hours emergencies, contact the emergency hotline</li>
                            <li>Keep your member ID ready when calling</li>
                        </ol>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-3 gap-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Update Information</h3>
                <p class="text-sm text-gray-600 mb-4">Need to update your contact information?</p>
                <a href="{{ route('member.profile.edit') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-edit mr-2"></i>
                    Edit Profile
                </a>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Field Officer</h3>
                <p class="text-sm text-gray-600 mb-4">Need to speak with your field officer?</p>
                <button class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50" onclick="alert('Please contact your field officer directly or visit the branch.')">
                    <i class="fas fa-phone mr-2"></i>
                    Contact Now
                </button>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Visit Branch</h3>
                <p class="text-sm text-gray-600 mb-4">Need to visit the branch office?</p>
                <a href="{{ route('member.profile.membership-details') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-building mr-2"></i>
                    Branch Details
                </a>
            </div>
        </div>
    </div>
</div>
@endsection
