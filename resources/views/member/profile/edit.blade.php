@extends('layouts.app')

@section('content')
<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-3xl font-bold text-gray-900">Edit Profile</h1>
                    <p class="text-gray-600">Update your personal information</p>
                </div>
                <a href="{{ route('member.profile.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-arrow-left mr-2"></i>
                    Back to Profile
                </a>
            </div>
        </div>

        <!-- Edit Form -->
        <div class="bg-white shadow rounded-lg">
            <form action="{{ route('member.profile.update') }}" method="POST" enctype="multipart/form-data">
                @csrf
                @method('PUT')
                
                <div class="px-4 py-5 sm:p-6">
                    <!-- Profile Photo Section -->
                    <div class="mb-8">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Profile Photo</h3>
                        <div class="flex items-center space-x-6">
                            <div class="flex-shrink-0">
                                @if($member->photo)
                                    <img class="w-20 h-20 rounded-full object-cover" src="{{ Storage::url($member->photo) }}" alt="{{ $member->name }}" id="photo-preview">
                                @else
                                    <div class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center" id="photo-preview">
                                        <span class="text-2xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                                    </div>
                                @endif
                            </div>
                            <div>
                                <label for="photo" class="block text-sm font-medium text-gray-700">
                                    Change Photo
                                </label>
                                <input type="file" name="photo" id="photo" accept="image/*" class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                                <p class="mt-1 text-xs text-gray-500">JPG, PNG up to 2MB</p>
                                @error('photo')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Contact Information -->
                    <div class="mb-8">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Contact Information</h3>
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label for="phone_number" class="block text-sm font-medium text-gray-700">
                                    Phone Number *
                                </label>
                                <input type="tel" name="phone_number" id="phone_number" value="{{ old('phone_number', $member->phone_number) }}" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                @error('phone_number')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Address Information -->
                    <div class="mb-8">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Address Information</h3>
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label for="present_address" class="block text-sm font-medium text-gray-700">
                                    Present Address *
                                </label>
                                <textarea name="present_address" id="present_address" rows="3" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">{{ old('present_address', $member->present_address) }}</textarea>
                                @error('present_address')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>

                            <div>
                                <label for="permanent_address" class="block text-sm font-medium text-gray-700">
                                    Permanent Address *
                                </label>
                                <textarea name="permanent_address" id="permanent_address" rows="3" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">{{ old('permanent_address', $member->permanent_address) }}</textarea>
                                @error('permanent_address')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Professional Information -->
                    <div class="mb-8">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Professional Information</h3>
                        <div class="grid grid-cols-1 gap-6">
                            <div>
                                <label for="occupation" class="block text-sm font-medium text-gray-700">
                                    Occupation *
                                </label>
                                <input type="text" name="occupation" id="occupation" value="{{ old('occupation', $member->occupation) }}" required class="mt-1 block w-full border-gray-300 rounded-md shadow-sm focus:ring-blue-500 focus:border-blue-500">
                                @error('occupation')
                                    <p class="mt-1 text-sm text-red-600">{{ $message }}</p>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Read-only Information -->
                    <div class="mb-8">
                        <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Member Information (Read Only)</h3>
                        <div class="bg-gray-50 p-4 rounded-lg">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Full Name</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $member->name }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Member ID</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $member->member_id }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Date of Birth</label>
                                    <p class="mt-1 text-sm text-gray-900">
                                        {{ $member->date_of_birth ? $member->date_of_birth->format('M d, Y') : 'N/A' }}
                                        @if($member->date_of_birth)
                                            <span class="text-gray-500">({{ $member->age }} years old)</span>
                                        @endif
                                    </p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">NID Number</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $member->nid_number ?? 'N/A' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Branch</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $member->branch->name ?? 'N/A' }}</p>
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-500">Field Officer</label>
                                    <p class="mt-1 text-sm text-gray-900">{{ $member->createdBy->name ?? 'N/A' }}</p>
                                </div>
                            </div>
                            <div class="mt-4 p-3 bg-blue-50 rounded-md">
                                <div class="flex">
                                    <div class="flex-shrink-0">
                                        <i class="fas fa-info-circle text-blue-400"></i>
                                    </div>
                                    <div class="ml-3">
                                        <p class="text-sm text-blue-700">
                                            To update your name, date of birth, NID number, or other official information, please contact your field officer or visit the branch.
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Form Actions -->
                <div class="px-4 py-3 bg-gray-50 text-right sm:px-6 rounded-b-lg">
                    <div class="flex justify-end space-x-3">
                        <a href="{{ route('member.profile.index') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            Cancel
                        </a>
                        <button type="submit" class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-save mr-2"></i>
                            Save Changes
                        </button>
                    </div>
                </div>
            </form>
        </div>

        <!-- Additional Actions -->
        <div class="mt-8 grid grid-cols-1 md:grid-cols-2 gap-6">
            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Security Settings</h3>
                <p class="text-sm text-gray-600 mb-4">Manage your account security and password.</p>
                <a href="{{ route('member.profile.change-password') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-key mr-2"></i>
                    Change Password
                </a>
            </div>

            <div class="bg-white shadow rounded-lg p-6">
                <h3 class="text-lg font-medium text-gray-900 mb-4">Emergency Contacts</h3>
                <p class="text-sm text-gray-600 mb-4">View and manage your emergency contact information.</p>
                <a href="{{ route('member.profile.emergency-contacts') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                    <i class="fas fa-phone mr-2"></i>
                    View Contacts
                </a>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const photoInput = document.getElementById('photo');
    const photoPreview = document.getElementById('photo-preview');

    photoInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                photoPreview.innerHTML = `<img class="w-20 h-20 rounded-full object-cover" src="${e.target.result}" alt="Preview">`;
            };
            reader.readAsDataURL(file);
        }
    });
});
</script>
@endsection
