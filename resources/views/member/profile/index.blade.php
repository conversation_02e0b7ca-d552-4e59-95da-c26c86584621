@extends('layouts.app')

@section('content')
<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="py-6">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900">My Profile</h1>
            <p class="text-gray-600">Manage your personal information and account settings</p>
        </div>

        <!-- Profile Overview -->
        <div class="bg-white overflow-hidden shadow rounded-lg mb-8">
            <div class="px-4 py-5 sm:p-6">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if($member->photo)
                                <img class="w-20 h-20 rounded-full object-cover" src="{{ Storage::url($member->photo) }}" alt="{{ $member->name }}">
                            @else
                                <div class="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center">
                                    <span class="text-2xl font-bold text-white">{{ substr($member->name, 0, 1) }}</span>
                                </div>
                            @endif
                        </div>
                        <div class="ml-6">
                            <h2 class="text-2xl font-bold text-gray-900">{{ $member->name }}</h2>
                            <p class="text-lg text-gray-600">Member ID: {{ $member->member_id }}</p>
                            <p class="text-sm text-gray-500">{{ $member->branch->name ?? 'N/A' }} Branch</p>
                            <p class="text-sm text-gray-500">Member since {{ $membershipStats['member_since']->format('M d, Y') }}</p>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-2">
                        <a href="{{ route('member.profile.edit') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-edit mr-2"></i>
                            Edit Profile
                        </a>
                        <a href="{{ route('member.profile.change-password') }}" class="inline-flex items-center px-4 py-2 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50">
                            <i class="fas fa-key mr-2"></i>
                            Change Password
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-hand-holding-usd text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Total Loans</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $membershipStats['total_loans'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-green-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-check-circle text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Active Loans</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $membershipStats['active_loans'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-piggy-bank text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Savings Accounts</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $membershipStats['total_savings_accounts'] }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>

            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="p-5">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            <div class="w-8 h-8 bg-{{ $membershipStats['can_apply_for_loan'] ? 'green' : 'red' }}-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-{{ $membershipStats['can_apply_for_loan'] ? 'check' : 'times' }} text-white text-sm"></i>
                            </div>
                        </div>
                        <div class="ml-5 w-0 flex-1">
                            <dl>
                                <dt class="text-sm font-medium text-gray-500 truncate">Loan Eligibility</dt>
                                <dd class="text-lg font-medium text-gray-900">{{ $membershipStats['can_apply_for_loan'] ? 'Eligible' : 'Not Eligible' }}</dd>
                            </dl>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Profile Sections -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Personal Information -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Personal Information</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Full Name</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->name }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Father/Husband Name</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->father_or_husband_name ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Mother's Name</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->mother_name ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Date of Birth</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                {{ $member->date_of_birth ? $member->date_of_birth->format('M d, Y') : 'N/A' }}
                                @if($member->date_of_birth)
                                    <span class="text-gray-500">({{ $member->age }} years old)</span>
                                @endif
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">NID Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->nid_number ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Religion</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->religion ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Blood Group</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->blood_group ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Occupation</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->occupation ?? 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Contact Information</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-4">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Phone Number</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->phone_number ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Present Address</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->present_address ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Permanent Address</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->permanent_address ?? 'N/A' }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Membership Details -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Membership Details</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Member ID</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->member_id }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Branch</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->branch->name ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Field Officer</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $member->createdBy->name ?? 'N/A' }}</dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Reference Member</dt>
                            <dd class="mt-1 text-sm text-gray-900">
                                @if($member->reference)
                                    {{ $member->reference->name }} ({{ $member->reference->member_id }})
                                @else
                                    N/A
                                @endif
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Current Loan Status</dt>
                            <dd class="mt-1">
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                    @if($membershipStats['current_loan_status'] === 'No Active Loan') bg-green-100 text-green-800
                                    @elseif($membershipStats['current_loan_status'] === 'Current') bg-blue-100 text-blue-800
                                    @else bg-red-100 text-red-800 @endif">
                                    {{ $membershipStats['current_loan_status'] }}
                                </span>
                            </dd>
                        </div>
                        <div>
                            <dt class="text-sm font-medium text-gray-500">Next Loan Cycle</dt>
                            <dd class="mt-1 text-sm text-gray-900">{{ $membershipStats['next_loan_cycle'] }}</dd>
                        </div>
                    </dl>
                </div>
            </div>

            <!-- Account Summary -->
            <div class="bg-white overflow-hidden shadow rounded-lg">
                <div class="px-4 py-5 sm:p-6">
                    <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Account Summary</h3>
                    <dl class="grid grid-cols-1 gap-x-4 gap-y-4">
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-gray-500">Total Savings Balance</dt>
                            <dd class="text-sm font-medium text-green-600">৳{{ number_format($member->getSavingsBalance(), 2) }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-gray-500">Total Loan Amount</dt>
                            <dd class="text-sm font-medium text-blue-600">৳{{ number_format($member->getTotalLoanAmount(), 2) }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-gray-500">Total Paid Amount</dt>
                            <dd class="text-sm font-medium text-gray-900">৳{{ number_format($member->getTotalPaidAmount(), 2) }}</dd>
                        </div>
                        <div class="flex justify-between">
                            <dt class="text-sm font-medium text-gray-500">Outstanding Amount</dt>
                            <dd class="text-sm font-medium text-red-600">৳{{ number_format($member->getTotalOutstandingAmount(), 2) }}</dd>
                        </div>
                    </dl>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="mt-8 bg-white overflow-hidden shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                <h3 class="text-lg leading-6 font-medium text-gray-900 mb-4">Quick Actions</h3>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                    <a href="{{ route('member.profile.membership-details') }}" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors duration-200">
                        <i class="fas fa-id-card text-blue-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-blue-700">Membership Details</h4>
                            <p class="text-xs text-blue-600">View detailed information</p>
                        </div>
                    </a>
                    <a href="{{ route('member.profile.emergency-contacts') }}" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition-colors duration-200">
                        <i class="fas fa-phone text-green-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-green-700">Emergency Contacts</h4>
                            <p class="text-xs text-green-600">View contact information</p>
                        </div>
                    </a>
                    <a href="{{ route('member.profile.documents') }}" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition-colors duration-200">
                        <i class="fas fa-file-alt text-purple-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-purple-700">My Documents</h4>
                            <p class="text-xs text-purple-600">View uploaded documents</p>
                        </div>
                    </a>
                    <a href="{{ route('member.loans.index') }}" class="flex items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition-colors duration-200">
                        <i class="fas fa-hand-holding-usd text-yellow-500 text-xl mr-3"></i>
                        <div>
                            <h4 class="text-sm font-medium text-yellow-700">My Loans</h4>
                            <p class="text-xs text-yellow-600">View loan information</p>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
