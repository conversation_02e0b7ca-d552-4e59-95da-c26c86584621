@extends('layouts.app')

@section('title', 'File Management')

@section('content')
<div class="container mx-auto px-4 py-6">
    <!-- Header -->
    <div class="mb-6">
        <h1 class="text-3xl font-bold text-gray-900">File Management</h1>
        <p class="text-gray-600 mt-2">Monitor and manage file storage across the system</p>
    </div>

    <!-- Storage Statistics -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-blue-100 text-blue-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Images</p>
                    <p class="text-2xl font-semibold text-gray-900" id="images-count">-</p>
                    <p class="text-sm text-gray-500" id="images-size">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-green-100 text-green-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Documents</p>
                    <p class="text-2xl font-semibold text-gray-900" id="documents-count">-</p>
                    <p class="text-sm text-gray-500" id="documents-size">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-yellow-100 text-yellow-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8m-9 4h4"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Backups</p>
                    <p class="text-2xl font-semibold text-gray-900" id="backups-count">-</p>
                    <p class="text-sm text-gray-500" id="backups-size">-</p>
                </div>
            </div>
        </div>

        <div class="bg-white rounded-lg shadow p-6">
            <div class="flex items-center">
                <div class="p-3 rounded-full bg-red-100 text-red-600">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                    </svg>
                </div>
                <div class="ml-4">
                    <p class="text-sm font-medium text-gray-600">Temp Files</p>
                    <p class="text-2xl font-semibold text-gray-900" id="temp-count">-</p>
                    <p class="text-sm text-gray-500" id="temp-size">-</p>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="bg-white rounded-lg shadow mb-8">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">File Management Actions</h2>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <button 
                    onclick="cleanupTempFiles()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    Cleanup Temp Files
                </button>

                <button 
                    onclick="optimizeImages()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Optimize Images
                </button>

                <button 
                    onclick="checkIntegrity()"
                    class="inline-flex items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-yellow-600 hover:bg-yellow-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-yellow-500"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Check Integrity
                </button>

                <button 
                    onclick="refreshStats()"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Refresh Stats
                </button>
            </div>
        </div>
    </div>

    <!-- Detailed Storage Information -->
    <div class="bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Storage Details</h2>
        </div>
        <div class="p-6">
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disk</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">File Count</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Total Size</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        </tr>
                    </thead>
                    <tbody id="storage-table-body" class="bg-white divide-y divide-gray-200">
                        <!-- Data will be populated by JavaScript -->
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Activity Log -->
    <div class="mt-8 bg-white rounded-lg shadow">
        <div class="px-6 py-4 border-b border-gray-200">
            <h2 class="text-lg font-semibold text-gray-900">Recent Activity</h2>
        </div>
        <div class="p-6">
            <div id="activity-log" class="space-y-3">
                <!-- Activity items will be populated here -->
            </div>
        </div>
    </div>
</div>

<!-- Loading Modal -->
<div id="loading-modal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden items-center justify-center z-50">
    <div class="bg-white rounded-lg p-6 max-w-sm mx-auto">
        <div class="flex items-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
            <div class="ml-3">
                <p class="text-sm font-medium text-gray-900" id="loading-text">Processing...</p>
            </div>
        </div>
    </div>
</div>
@endsection

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    refreshStats();
});

function showLoading(text = 'Processing...') {
    document.getElementById('loading-text').textContent = text;
    document.getElementById('loading-modal').classList.remove('hidden');
    document.getElementById('loading-modal').classList.add('flex');
}

function hideLoading() {
    document.getElementById('loading-modal').classList.add('hidden');
    document.getElementById('loading-modal').classList.remove('flex');
}

function refreshStats() {
    showLoading('Loading storage statistics...');
    
    fetch('{{ route("files.storage-stats") }}')
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                updateStorageStats(data.data);
            } else {
                showAlert('Error loading storage statistics', 'error');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showAlert('Failed to load storage statistics', 'error');
        })
        .finally(() => {
            hideLoading();
        });
}

function updateStorageStats(stats) {
    // Update summary cards
    if (stats.images) {
        document.getElementById('images-count').textContent = stats.images.file_count || 0;
        document.getElementById('images-size').textContent = stats.images.total_size_human || '0 B';
    }
    
    if (stats.documents) {
        document.getElementById('documents-count').textContent = stats.documents.file_count || 0;
        document.getElementById('documents-size').textContent = stats.documents.total_size_human || '0 B';
    }
    
    if (stats.backups) {
        document.getElementById('backups-count').textContent = stats.backups.file_count || 0;
        document.getElementById('backups-size').textContent = stats.backups.total_size_human || '0 B';
    }
    
    if (stats.temp) {
        document.getElementById('temp-count').textContent = stats.temp.file_count || 0;
        document.getElementById('temp-size').textContent = stats.temp.total_size_human || '0 B';
    }
    
    // Update detailed table
    const tableBody = document.getElementById('storage-table-body');
    tableBody.innerHTML = '';
    
    Object.entries(stats).forEach(([disk, data]) => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">${disk}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${data.file_count || 'Error'}</td>
            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">${data.total_size_human || data.error || 'Unknown'}</td>
            <td class="px-6 py-4 whitespace-nowrap">
                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full ${data.error ? 'bg-red-100 text-red-800' : 'bg-green-100 text-green-800'}">
                    ${data.error ? 'Error' : 'OK'}
                </span>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

function cleanupTempFiles() {
    if (!confirm('Are you sure you want to cleanup temporary files? This action cannot be undone.')) {
        return;
    }
    
    showLoading('Cleaning up temporary files...');
    addActivity('Started temporary file cleanup');
    
    // Simulate cleanup process (replace with actual API call)
    setTimeout(() => {
        hideLoading();
        addActivity('Temporary file cleanup completed');
        showAlert('Temporary files cleaned up successfully', 'success');
        refreshStats();
    }, 3000);
}

function optimizeImages() {
    if (!confirm('Are you sure you want to optimize all images? This may take a while.')) {
        return;
    }
    
    showLoading('Optimizing images...');
    addActivity('Started image optimization');
    
    // Simulate optimization process (replace with actual API call)
    setTimeout(() => {
        hideLoading();
        addActivity('Image optimization completed');
        showAlert('Images optimized successfully', 'success');
        refreshStats();
    }, 5000);
}

function checkIntegrity() {
    showLoading('Checking file integrity...');
    addActivity('Started file integrity check');
    
    // Simulate integrity check (replace with actual API call)
    setTimeout(() => {
        hideLoading();
        addActivity('File integrity check completed - All files OK');
        showAlert('File integrity check completed successfully', 'success');
    }, 4000);
}

function addActivity(message) {
    const activityLog = document.getElementById('activity-log');
    const timestamp = new Date().toLocaleTimeString();
    
    const activityItem = document.createElement('div');
    activityItem.className = 'flex items-center text-sm text-gray-600';
    activityItem.innerHTML = `
        <div class="w-2 h-2 bg-blue-400 rounded-full mr-3"></div>
        <span class="text-gray-500 mr-2">${timestamp}</span>
        <span>${message}</span>
    `;
    
    activityLog.insertBefore(activityItem, activityLog.firstChild);
    
    // Keep only last 10 activities
    while (activityLog.children.length > 10) {
        activityLog.removeChild(activityLog.lastChild);
    }
}

function showAlert(message, type = 'info') {
    // Create alert element
    const alert = document.createElement('div');
    alert.className = `fixed top-4 right-4 p-4 rounded-md shadow-lg z-50 ${
        type === 'success' ? 'bg-green-100 text-green-800 border border-green-200' :
        type === 'error' ? 'bg-red-100 text-red-800 border border-red-200' :
        'bg-blue-100 text-blue-800 border border-blue-200'
    }`;
    alert.innerHTML = `
        <div class="flex items-center">
            <span>${message}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-3 text-lg font-semibold">&times;</button>
        </div>
    `;
    
    document.body.appendChild(alert);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
        if (alert.parentElement) {
            alert.remove();
        }
    }, 5000);
}
</script>
@endpush
