@extends('layouts.app')

@section('title', 'Add New Branch - Admin')

@section('content')
<!-- Header -->
<div class="mb-6">
    <div class="flex items-center">
        <a href="{{ route('admin.branches.index') }}" class="text-gray-500 hover:text-gray-700 mr-4">
            <i class="fas fa-arrow-left"></i>
        </a>
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Add New Branch</h1>
            <p class="mt-1 text-sm text-gray-600">Create a new microfinance branch location</p>
        </div>
    </div>
</div>

<!-- Form -->
<div class="max-w-2xl">
    <form action="{{ route('admin.branches.store') }}" method="POST" class="space-y-6">
        @csrf

        <!-- Branch Information Card -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Branch Information</h3>
                <p class="text-sm text-gray-500">Basic details about the branch</p>
            </div>
            <div class="px-6 py-6 space-y-6">
                <!-- Branch Name -->
                <div>
                    <label for="name" class="form-label">
                        Branch Name <span class="text-danger-500">*</span>
                    </label>
                    <input 
                        type="text" 
                        name="name" 
                        id="name"
                        value="{{ old('name') }}"
                        class="form-input @error('name') error @enderror"
                        placeholder="Enter branch name"
                        required
                    >
                    @error('name')
                        <p class="form-error">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">
                        Choose a unique and descriptive name for the branch
                    </p>
                </div>

                <!-- Branch Address -->
                <div>
                    <label for="address" class="form-label">
                        Branch Address <span class="text-danger-500">*</span>
                    </label>
                    <textarea 
                        name="address" 
                        id="address"
                        rows="3"
                        class="form-input @error('address') error @enderror"
                        placeholder="Enter complete branch address"
                        required
                    >{{ old('address') }}</textarea>
                    @error('address')
                        <p class="form-error">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                    <p class="text-xs text-gray-500 mt-1">
                        Include street address, area, city, and postal code
                    </p>
                </div>
            </div>
        </div>

        <!-- Manager Assignment Card -->
        <div class="bg-white shadow-sm rounded-lg border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 class="text-lg font-medium text-gray-900">Manager Assignment</h3>
                <p class="text-sm text-gray-500">Assign a manager to this branch (optional)</p>
            </div>
            <div class="px-6 py-6">
                <!-- Manager Selection -->
                <div>
                    <label for="manager_id" class="form-label">
                        Branch Manager
                    </label>
                    <select 
                        name="manager_id" 
                        id="manager_id"
                        class="form-input @error('manager_id') error @enderror"
                    >
                        <option value="">Select a manager (optional)</option>
                        @foreach($availableManagers as $manager)
                            <option value="{{ $manager->id }}" {{ old('manager_id') == $manager->id ? 'selected' : '' }}>
                                {{ $manager->name }} ({{ $manager->email }})
                            </option>
                        @endforeach
                    </select>
                    @error('manager_id')
                        <p class="form-error">
                            <i class="fas fa-exclamation-circle mr-1"></i>
                            {{ $message }}
                        </p>
                    @enderror
                    
                    @if($availableManagers->count() === 0)
                        <div class="mt-3 p-4 bg-warning-50 border border-warning-200 rounded-lg">
                            <div class="flex items-center">
                                <i class="fas fa-exclamation-triangle text-warning-600 mr-2"></i>
                                <div>
                                    <p class="text-sm font-medium text-warning-800">No Available Managers</p>
                                    <p class="text-sm text-warning-700">
                                        All managers are already assigned to branches. You can assign a manager later or 
                                        <a href="{{ route('admin.users.create') }}" class="underline hover:no-underline">create a new manager</a>.
                                    </p>
                                </div>
                            </div>
                        </div>
                    @else
                        <p class="text-xs text-gray-500 mt-1">
                            Only unassigned managers are shown. You can change the manager later.
                        </p>
                    @endif
                </div>
            </div>
        </div>

        <!-- Form Actions -->
        <div class="flex items-center justify-between pt-6 border-t border-gray-200">
            <a href="{{ route('admin.branches.index') }}" class="btn-outline">
                <i class="fas fa-times mr-2"></i>
                Cancel
            </a>
            <button type="submit" class="btn-primary">
                <i class="fas fa-save mr-2"></i>
                Create Branch
            </button>
        </div>
    </form>
</div>

<!-- Help Section -->
<div class="mt-8 max-w-2xl">
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
        <h4 class="text-sm font-medium text-blue-900 mb-2">
            <i class="fas fa-info-circle mr-2"></i>
            Branch Creation Guidelines
        </h4>
        <ul class="text-sm text-blue-800 space-y-1">
            <li>• Branch names must be unique across the system</li>
            <li>• Provide complete address information for accurate location tracking</li>
            <li>• Managers can be assigned during creation or later through the edit form</li>
            <li>• Each manager can only be assigned to one branch at a time</li>
            <li>• Branch statistics will be available once members and loans are added</li>
        </ul>
    </div>
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-focus on branch name field
    document.getElementById('name').focus();
    
    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const name = document.getElementById('name').value.trim();
        const address = document.getElementById('address').value.trim();
        
        if (!name) {
            e.preventDefault();
            alert('Please enter a branch name.');
            document.getElementById('name').focus();
            return;
        }
        
        if (!address) {
            e.preventDefault();
            alert('Please enter a branch address.');
            document.getElementById('address').focus();
            return;
        }
    });
});
</script>
@endpush
@endsection
