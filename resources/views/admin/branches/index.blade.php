@extends('layouts.app')

@section('title', 'Branch Management - Admin')

@section('content')
<!-- Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">Branch Management</h1>
            <p class="mt-1 text-sm text-gray-600">Manage all microfinance branch locations</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.branches.create') }}" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>
                Add New Branch
            </a>
        </div>
    </div>
</div>

<!-- Search and Filters -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
    <div class="p-6">
        <form method="GET" action="{{ route('admin.branches.index') }}" class="flex flex-col sm:flex-row gap-4">
            <div class="flex-1">
                <label for="search" class="sr-only">Search branches</label>
                <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                        <i class="fas fa-search text-gray-400"></i>
                    </div>
                    <input 
                        type="text" 
                        name="search" 
                        id="search"
                        value="{{ request('search') }}"
                        class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                        placeholder="Search by branch name, address, or manager..."
                    >
                </div>
            </div>
            <div class="flex gap-2">
                <button type="submit" class="btn-primary">
                    <i class="fas fa-search mr-2"></i>
                    Search
                </button>
                @if(request()->hasAny(['search']))
                    <a href="{{ route('admin.branches.index') }}" class="btn-outline">
                        <i class="fas fa-times mr-2"></i>
                        Clear
                    </a>
                @endif
            </div>
        </form>
    </div>
</div>

<!-- Branches Table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
            All Branches 
            <span class="text-sm font-normal text-gray-500">({{ $branches->total() }} total)</span>
        </h3>
    </div>

    @if($branches->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Branch Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Manager
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Statistics
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($branches as $branch)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <div>
                                    <div class="text-sm font-medium text-gray-900">{{ $branch->name }}</div>
                                    <div class="text-sm text-gray-500">
                                        <i class="fas fa-map-marker-alt mr-1"></i>
                                        {{ Str::limit($branch->address, 50) }}
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if($branch->manager)
                                    <div class="flex items-center">
                                        <div class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center mr-3">
                                            <span class="text-xs font-medium text-primary-700">
                                                {{ substr($branch->manager->name, 0, 1) }}
                                            </span>
                                        </div>
                                        <div>
                                            <div class="text-sm font-medium text-gray-900">{{ $branch->manager->name }}</div>
                                            <div class="text-sm text-gray-500">{{ $branch->manager->email }}</div>
                                        </div>
                                    </div>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800">
                                        <i class="fas fa-exclamation-triangle mr-1"></i>
                                        No Manager
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4">
                                <div class="text-sm text-gray-900">
                                    <div class="flex items-center mb-1">
                                        <i class="fas fa-users text-gray-400 mr-2 w-4"></i>
                                        <span>{{ $branch->members->count() }} members</span>
                                    </div>
                                    <div class="flex items-center mb-1">
                                        <i class="fas fa-user-friends text-gray-400 mr-2 w-4"></i>
                                        <span>{{ $branch->users->count() }} staff</span>
                                    </div>
                                    <div class="flex items-center">
                                        <i class="fas fa-hand-holding-usd text-gray-400 mr-2 w-4"></i>
                                        <span>{{ $branch->getTotalActiveLoans() }} active loans</span>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if($branch->manager && $branch->manager->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-danger-100 text-danger-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Inactive
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ route('admin.branches.show', $branch) }}" 
                                       class="text-primary-600 hover:text-primary-900 p-1 rounded"
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.branches.edit', $branch) }}" 
                                       class="text-secondary-600 hover:text-secondary-900 p-1 rounded"
                                       title="Edit Branch">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="deleteBranch({{ $branch->id }}, '{{ $branch->name }}')" 
                                            class="text-danger-600 hover:text-danger-900 p-1 rounded"
                                            title="Delete Branch">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $branches->links() }}
        </div>
    @else
        <div class="px-6 py-12 text-center">
            <i class="fas fa-building text-4xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No branches found</h3>
            <p class="text-gray-500 mb-6">
                @if(request('search'))
                    No branches match your search criteria.
                @else
                    Get started by creating your first branch.
                @endif
            </p>
            @if(!request('search'))
                <a href="{{ route('admin.branches.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    Add First Branch
                </a>
            @endif
        </div>
    @endif
</div>

<!-- Delete Confirmation Modal -->
<div id="deleteModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 overflow-y-auto h-full w-full hidden z-50">
    <div class="relative top-20 mx-auto p-5 border w-96 shadow-lg rounded-md bg-white">
        <div class="mt-3 text-center">
            <div class="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-danger-100 mb-4">
                <i class="fas fa-exclamation-triangle text-danger-600"></i>
            </div>
            <h3 class="text-lg font-medium text-gray-900 mb-2">Delete Branch</h3>
            <p class="text-sm text-gray-500 mb-4">
                Are you sure you want to delete <strong id="branchName"></strong>? This action cannot be undone.
            </p>
            <div class="flex justify-center space-x-4">
                <button onclick="closeDeleteModal()" class="btn-outline">Cancel</button>
                <form id="deleteForm" method="POST" class="inline">
                    @csrf
                    @method('DELETE')
                    <button type="submit" class="btn-danger">Delete Branch</button>
                </form>
            </div>
        </div>
    </div>
</div>

@push('scripts')
<script>
function deleteBranch(branchId, branchName) {
    document.getElementById('branchName').textContent = branchName;
    document.getElementById('deleteForm').action = `/admin/branches/${branchId}`;
    document.getElementById('deleteModal').classList.remove('hidden');
}

function closeDeleteModal() {
    document.getElementById('deleteModal').classList.add('hidden');
}

// Close modal when clicking outside
document.getElementById('deleteModal').addEventListener('click', function(e) {
    if (e.target === this) {
        closeDeleteModal();
    }
});
</script>
@endpush
@endsection
