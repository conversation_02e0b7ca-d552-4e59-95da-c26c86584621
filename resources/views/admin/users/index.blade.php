@extends('layouts.app')

@section('title', 'User Management - Admin')

@section('content')
<!-- Header -->
<div class="mb-6">
    <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
            <h1 class="text-2xl font-bold text-gray-900">User Management</h1>
            <p class="mt-1 text-sm text-gray-600">Manage system users and their roles</p>
        </div>
        <div class="mt-4 sm:mt-0">
            <a href="{{ route('admin.users.create') }}" class="btn-primary">
                <i class="fas fa-plus mr-2"></i>
                Add New User
            </a>
        </div>
    </div>
</div>

<!-- Statistics Cards -->
<div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 mb-6">
    <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-gray-900">{{ $stats['total_users'] }}</div>
        <div class="text-sm text-gray-500">Total Users</div>
    </div>
    <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-success-600">{{ $stats['active_users'] }}</div>
        <div class="text-sm text-gray-500">Active Users</div>
    </div>
    <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-primary-600">{{ $stats['admins'] }}</div>
        <div class="text-sm text-gray-500">Admins</div>
    </div>
    <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-secondary-600">{{ $stats['managers'] }}</div>
        <div class="text-sm text-gray-500">Managers</div>
    </div>
    <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-accent-600">{{ $stats['field_officers'] }}</div>
        <div class="text-sm text-gray-500">Field Officers</div>
    </div>
    <div class="bg-white p-4 rounded-lg border border-gray-200">
        <div class="text-2xl font-bold text-warning-600">{{ $stats['members'] }}</div>
        <div class="text-sm text-gray-500">Members</div>
    </div>
</div>

<!-- Search and Filters -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 mb-6">
    <div class="p-6">
        <form method="GET" action="{{ route('admin.users.index') }}" class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Search -->
                <div>
                    <label for="search" class="sr-only">Search users</label>
                    <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                            <i class="fas fa-search text-gray-400"></i>
                        </div>
                        <input 
                            type="text" 
                            name="search" 
                            id="search"
                            value="{{ request('search') }}"
                            class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                            placeholder="Search users..."
                        >
                    </div>
                </div>

                <!-- Role Filter -->
                <div>
                    <select name="role" class="form-input">
                        <option value="">All Roles</option>
                        <option value="admin" {{ request('role') === 'admin' ? 'selected' : '' }}>Admin</option>
                        <option value="manager" {{ request('role') === 'manager' ? 'selected' : '' }}>Manager</option>
                        <option value="field_officer" {{ request('role') === 'field_officer' ? 'selected' : '' }}>Field Officer</option>
                        <option value="member" {{ request('role') === 'member' ? 'selected' : '' }}>Member</option>
                    </select>
                </div>

                <!-- Status Filter -->
                <div>
                    <select name="status" class="form-input">
                        <option value="">All Status</option>
                        <option value="active" {{ request('status') === 'active' ? 'selected' : '' }}>Active</option>
                        <option value="inactive" {{ request('status') === 'inactive' ? 'selected' : '' }}>Inactive</option>
                    </select>
                </div>

                <!-- Actions -->
                <div class="flex gap-2">
                    <button type="submit" class="btn-primary flex-1">
                        <i class="fas fa-search mr-2"></i>
                        Filter
                    </button>
                    @if(request()->hasAny(['search', 'role', 'status']))
                        <a href="{{ route('admin.users.index') }}" class="btn-outline">
                            <i class="fas fa-times"></i>
                        </a>
                    @endif
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Bulk Actions -->
<div id="bulkActions" class="bg-primary-50 border border-primary-200 rounded-lg p-4 mb-6 hidden">
    <div class="flex items-center justify-between">
        <div class="flex items-center">
            <span class="text-sm font-medium text-primary-800">
                <span id="selectedCount">0</span> users selected
            </span>
        </div>
        <div class="flex items-center space-x-2">
            <button onclick="bulkAction('activate')" class="btn-success btn-sm">
                <i class="fas fa-check mr-1"></i>
                Activate
            </button>
            <button onclick="bulkAction('deactivate')" class="btn-warning btn-sm">
                <i class="fas fa-pause mr-1"></i>
                Deactivate
            </button>
            <button onclick="bulkAction('delete')" class="btn-danger btn-sm">
                <i class="fas fa-trash mr-1"></i>
                Delete
            </button>
            <button onclick="clearSelection()" class="btn-outline btn-sm">
                Clear
            </button>
        </div>
    </div>
</div>

<!-- Users Table -->
<div class="bg-white shadow-sm rounded-lg border border-gray-200 overflow-hidden">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">
            All Users 
            <span class="text-sm font-normal text-gray-500">({{ $users->total() }} total)</span>
        </h3>
    </div>

    @if($users->count() > 0)
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left">
                            <input type="checkbox" id="selectAll" class="rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            User Details
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Role & Branch
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Status
                        </th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Last Activity
                        </th>
                        <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                            Actions
                        </th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @foreach($users as $user)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4">
                                <input type="checkbox" name="user_ids[]" value="{{ $user->id }}" class="user-checkbox rounded border-gray-300 text-primary-600 focus:ring-primary-500">
                            </td>
                            <td class="px-6 py-4">
                                <div class="flex items-center">
                                    <div class="h-10 w-10 rounded-full bg-primary-100 flex items-center justify-center mr-4">
                                        <span class="text-sm font-medium text-primary-700">
                                            {{ substr($user->name, 0, 1) }}
                                        </span>
                                    </div>
                                    <div>
                                        <div class="text-sm font-medium text-gray-900">{{ $user->name }}</div>
                                        <div class="text-sm text-gray-500">{{ $user->email }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                <div>
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium 
                                        @if($user->role === 'admin') bg-primary-100 text-primary-800
                                        @elseif($user->role === 'manager') bg-secondary-100 text-secondary-800
                                        @elseif($user->role === 'field_officer') bg-accent-100 text-accent-800
                                        @else bg-warning-100 text-warning-800 @endif">
                                        {{ $user->display_role }}
                                    </span>
                                    @if($user->branch)
                                        <div class="text-xs text-gray-500 mt-1">
                                            <i class="fas fa-building mr-1"></i>
                                            {{ $user->branch->name }}
                                        </div>
                                    @endif
                                </div>
                            </td>
                            <td class="px-6 py-4">
                                @if($user->is_active)
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                        <i class="fas fa-check-circle mr-1"></i>
                                        Active
                                    </span>
                                @else
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-danger-100 text-danger-800">
                                        <i class="fas fa-times-circle mr-1"></i>
                                        Inactive
                                    </span>
                                @endif
                            </td>
                            <td class="px-6 py-4 text-sm text-gray-500">
                                {{ $user->updated_at->diffForHumans() }}
                            </td>
                            <td class="px-6 py-4 text-right text-sm font-medium">
                                <div class="flex items-center justify-end space-x-2">
                                    <a href="{{ route('admin.users.show', $user) }}" 
                                       class="text-primary-600 hover:text-primary-900 p-1 rounded"
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.users.edit', $user) }}" 
                                       class="text-secondary-600 hover:text-secondary-900 p-1 rounded"
                                       title="Edit User">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <button onclick="toggleUserStatus({{ $user->id }}, {{ $user->is_active ? 'false' : 'true' }})" 
                                            class="text-warning-600 hover:text-warning-900 p-1 rounded"
                                            title="{{ $user->is_active ? 'Deactivate' : 'Activate' }} User">
                                        <i class="fas fa-{{ $user->is_active ? 'pause' : 'play' }}"></i>
                                    </button>
                                    <button onclick="resetPassword({{ $user->id }}, '{{ $user->name }}')" 
                                            class="text-accent-600 hover:text-accent-900 p-1 rounded"
                                            title="Reset Password">
                                        <i class="fas fa-key"></i>
                                    </button>
                                    @if(!$user->isAdmin() || \App\Models\User::where('role', 'admin')->count() > 1)
                                        <button onclick="deleteUser({{ $user->id }}, '{{ $user->name }}')" 
                                                class="text-danger-600 hover:text-danger-900 p-1 rounded"
                                                title="Delete User">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                    @endforeach
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        <div class="px-6 py-4 border-t border-gray-200">
            {{ $users->links() }}
        </div>
    @else
        <div class="px-6 py-12 text-center">
            <i class="fas fa-users text-4xl text-gray-300 mb-4"></i>
            <h3 class="text-lg font-medium text-gray-900 mb-2">No users found</h3>
            <p class="text-gray-500 mb-6">
                @if(request()->hasAny(['search', 'role', 'status']))
                    No users match your search criteria.
                @else
                    Get started by creating your first user.
                @endif
            </p>
            @if(!request()->hasAny(['search', 'role', 'status']))
                <a href="{{ route('admin.users.create') }}" class="btn-primary">
                    <i class="fas fa-plus mr-2"></i>
                    Add First User
                </a>
            @endif
        </div>
    @endif
</div>

@push('scripts')
<script>
// Bulk selection functionality
document.addEventListener('DOMContentLoaded', function() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const userCheckboxes = document.querySelectorAll('.user-checkbox');
    const bulkActions = document.getElementById('bulkActions');
    const selectedCount = document.getElementById('selectedCount');

    function updateBulkActions() {
        const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
        const count = checkedBoxes.length;
        
        selectedCount.textContent = count;
        
        if (count > 0) {
            bulkActions.classList.remove('hidden');
        } else {
            bulkActions.classList.add('hidden');
        }
        
        selectAllCheckbox.indeterminate = count > 0 && count < userCheckboxes.length;
        selectAllCheckbox.checked = count === userCheckboxes.length;
    }

    selectAllCheckbox.addEventListener('change', function() {
        userCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActions();
    });

    userCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', updateBulkActions);
    });
});

function clearSelection() {
    document.querySelectorAll('.user-checkbox').forEach(checkbox => {
        checkbox.checked = false;
    });
    document.getElementById('selectAll').checked = false;
    document.getElementById('bulkActions').classList.add('hidden');
}

function bulkAction(action) {
    const checkedBoxes = document.querySelectorAll('.user-checkbox:checked');
    const userIds = Array.from(checkedBoxes).map(cb => cb.value);
    
    if (userIds.length === 0) {
        alert('Please select users first.');
        return;
    }
    
    const actionText = action === 'delete' ? 'delete' : action;
    if (!confirm(`Are you sure you want to ${actionText} ${userIds.length} selected user(s)?`)) {
        return;
    }
    
    fetch('{{ route("admin.users.bulk-action") }}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            action: action,
            user_ids: userIds
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || 'An error occurred');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('An error occurred');
    });
}

function toggleUserStatus(userId, newStatus) {
    fetch(`/admin/users/${userId}/toggle-status`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            location.reload();
        } else {
            alert(data.error || 'An error occurred');
        }
    });
}

function resetPassword(userId, userName) {
    const newPassword = prompt(`Enter new password for ${userName}:`);
    if (!newPassword) return;
    
    const confirmPassword = prompt('Confirm new password:');
    if (newPassword !== confirmPassword) {
        alert('Passwords do not match');
        return;
    }
    
    fetch(`/admin/users/${userId}/reset-password`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
        },
        body: JSON.stringify({
            password: newPassword,
            password_confirmation: confirmPassword
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Password reset successfully');
        } else {
            alert(data.error || 'An error occurred');
        }
    });
}

function deleteUser(userId, userName) {
    if (!confirm(`Are you sure you want to delete ${userName}? This action cannot be undone.`)) {
        return;
    }
    
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = `/admin/users/${userId}`;
    form.innerHTML = `
        @csrf
        @method('DELETE')
    `;
    document.body.appendChild(form);
    form.submit();
}
</script>
@endpush
@endsection
