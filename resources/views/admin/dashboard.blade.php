@extends('layouts.app')

@section('title', 'Admin Dashboard - Sonali Microfinance')

@push('styles')
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
@endpush

@section('content')
<!-- Modern Header -->
<div class="mb-8 animate-fadeInUp">
    <div class="bg-gradient-to-r from-white to-gray-50/50 rounded-2xl p-6 shadow-lg border border-gray-200/50">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between">
            <div>
                <div class="flex items-center mb-2">
                    <div class="w-12 h-12 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mr-4 shadow-lg">
                        <i class="fas fa-tachometer-alt text-white text-xl"></i>
                    </div>
                    <div>
                        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
                        <div class="flex items-center text-sm text-gray-500 mt-1">
                            <i class="fas fa-clock mr-1"></i>
                            Last updated: {{ now()->format('M d, Y h:i A') }}
                        </div>
                    </div>
                </div>
                <p class="text-gray-600 text-lg">Welcome back, <span class="font-semibold text-primary-600">{{ auth()->user()->name }}</span>. Here's what's happening with Sonali Microfinance today.</p>
            </div>
            <div class="mt-4 sm:mt-0">
                <div class="flex items-center space-x-3">
                    <div class="bg-success-100 text-success-800 px-3 py-1 rounded-full text-sm font-medium">
                        <i class="fas fa-circle text-xs mr-1"></i>
                        System Online
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Quick Stats Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Branches -->
    <div class="dashboard-card group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="stat-icon bg-gradient-to-br from-primary-500 to-primary-600 text-white shadow-lg shadow-primary-500/30">
                    <i class="fas fa-building"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <div class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Total Branches</div>
                <div class="text-3xl font-bold text-gray-900 mt-1">{{ $stats['total_branches'] ?? 0 }}</div>
                <div class="text-xs text-gray-500 mt-2 flex items-center">
                    <i class="fas fa-map-marker-alt mr-1"></i>
                    Active locations
                </div>
            </div>
        </div>
    </div>

    <!-- Total Users -->
    <div class="dashboard-card group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="stat-icon bg-gradient-to-br from-secondary-500 to-secondary-600 text-white shadow-lg shadow-secondary-500/30">
                    <i class="fas fa-users"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <div class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Total Users</div>
                <div class="text-3xl font-bold text-gray-900 mt-1">{{ $stats['total_users'] ?? 0 }}</div>
                <div class="text-xs text-gray-500 mt-2 flex items-center">
                    <i class="fas fa-user-cog mr-1"></i>
                    System users
                </div>
            </div>
        </div>
    </div>

    <!-- Total Members -->
    <div class="dashboard-card group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="stat-icon bg-gradient-to-br from-success-500 to-success-600 text-white shadow-lg shadow-success-500/30">
                    <i class="fas fa-user-friends"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <div class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Total Members</div>
                <div class="text-3xl font-bold text-gray-900 mt-1">{{ $stats['total_members'] ?? 0 }}</div>
                <div class="text-xs text-success-600 mt-2 flex items-center">
                    <i class="fas fa-plus mr-1"></i>
                    {{ $quickStats['new_members_this_month'] ?? 0 }} this month
                </div>
            </div>
        </div>
    </div>

    <!-- Active Loans -->
    <div class="dashboard-card group">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="stat-icon bg-gradient-to-br from-accent-500 to-accent-600 text-white shadow-lg shadow-accent-500/30">
                    <i class="fas fa-hand-holding-usd"></i>
                </div>
            </div>
            <div class="ml-4 flex-1">
                <div class="text-sm font-semibold text-gray-500 uppercase tracking-wide">Active Loans</div>
                <div class="text-3xl font-bold text-gray-900 mt-1">{{ $stats['active_loans'] ?? 0 }}</div>
                <div class="text-xs mt-2 flex items-center">
                    @if(($stats['overdue_loans'] ?? 0) > 0)
                        <span class="text-danger-600 flex items-center">
                            <i class="fas fa-exclamation-triangle mr-1"></i>
                            {{ $stats['overdue_loans'] }} overdue
                        </span>
                    @else
                        <span class="text-success-600 flex items-center">
                            <i class="fas fa-check mr-1"></i>
                            All current
                        </span>
                    @endif
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Modern Financial Overview Cards -->
<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
    <!-- Total Loan Amount -->
    <div class="dashboard-card-gradient bg-gradient-to-br from-primary-500 via-primary-600 to-primary-700 shadow-glow">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-primary-100 text-sm font-semibold uppercase tracking-wide">Total Loan Portfolio</div>
                <div class="text-3xl font-bold mt-2">৳{{ number_format($stats['total_loan_amount'] ?? 0, 0) }}</div>
                <div class="text-primary-200 text-xs mt-1 flex items-center">
                    <i class="fas fa-trending-up mr-1"></i>
                    Portfolio value
                </div>
            </div>
            <div class="text-primary-200 opacity-80">
                <i class="fas fa-chart-line text-3xl"></i>
            </div>
        </div>
    </div>

    <!-- Total Savings -->
    <div class="dashboard-card-gradient bg-gradient-to-br from-success-500 via-success-600 to-success-700 shadow-glow">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-success-100 text-sm font-semibold uppercase tracking-wide">Total Savings</div>
                <div class="text-3xl font-bold mt-2">৳{{ number_format($stats['total_savings_balance'] ?? 0, 0) }}</div>
                <div class="text-success-200 text-xs mt-1 flex items-center">
                    <i class="fas fa-arrow-up mr-1"></i>
                    Member deposits
                </div>
            </div>
            <div class="text-success-200 opacity-80">
                <i class="fas fa-piggy-bank text-3xl"></i>
            </div>
        </div>
    </div>

    <!-- Today's Collections -->
    <div class="dashboard-card-gradient bg-gradient-to-br from-secondary-500 via-secondary-600 to-secondary-700 shadow-glow-blue">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-secondary-100 text-sm font-semibold uppercase tracking-wide">Today's Collections</div>
                <div class="text-3xl font-bold mt-2">৳{{ number_format($quickStats['today_collections'] ?? 0, 0) }}</div>
                <div class="text-secondary-200 text-xs mt-1 flex items-center">
                    <i class="fas fa-calendar-day mr-1"></i>
                    Daily intake
                </div>
            </div>
            <div class="text-secondary-200 opacity-80">
                <i class="fas fa-coins text-3xl"></i>
            </div>
        </div>
    </div>

    <!-- Collection Rate -->
    <div class="dashboard-card-gradient bg-gradient-to-br from-accent-500 via-accent-600 to-accent-700 shadow-glow-orange">
        <div class="flex items-center justify-between">
            <div>
                <div class="text-accent-100 text-sm font-semibold uppercase tracking-wide">Collection Rate</div>
                <div class="text-3xl font-bold mt-2">{{ $quickStats['collection_rate'] ?? 0 }}%</div>
                <div class="text-accent-200 text-xs mt-1 flex items-center">
                    <i class="fas fa-chart-pie mr-1"></i>
                    Performance
                </div>
            </div>
            <div class="text-accent-200 opacity-80">
                <i class="fas fa-percentage text-3xl"></i>
            </div>
        </div>
    </div>
</div>

<!-- Modern Charts Section -->
<div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8">
    <!-- Monthly Loan Disbursement Chart -->
    <div class="card-modern">
        <div class="card-header">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-br from-primary-500 to-primary-600 rounded-xl flex items-center justify-center mr-3">
                    <i class="fas fa-chart-line text-white"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Monthly Loan Disbursement</h3>
                    <p class="text-sm text-gray-500">Last 12 months loan disbursement trends</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            <canvas id="disbursementChart" height="300"></canvas>
        </div>
    </div>

    <!-- Collection Trends Chart -->
    <div class="card-modern">
        <div class="card-header">
            <div class="flex items-center">
                <div class="w-10 h-10 bg-gradient-to-br from-secondary-500 to-secondary-600 rounded-xl flex items-center justify-center mr-3">
                    <i class="fas fa-chart-bar text-white"></i>
                </div>
                <div>
                    <h3 class="text-lg font-bold text-gray-900">Collection Trends</h3>
                    <p class="text-sm text-gray-500">Monthly collection performance</p>
                </div>
            </div>
        </div>
        <div class="card-body">
            <canvas id="collectionChart" height="300"></canvas>
        </div>
    </div>
</div>

<!-- Branch Performance & Quick Actions -->
<div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
    <!-- Branch Performance -->
    <div class="lg:col-span-2 bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Branch Performance</h3>
            <p class="text-sm text-gray-500">Overview of all branch operations</p>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Branch</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Members</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Active Loans</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Disbursed</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Collection Rate</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    @forelse($branchPerformance as $branch)
                        <tr class="hover:bg-gray-50">
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm font-medium text-gray-900">{{ $branch['name'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $branch['total_members'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">{{ $branch['active_loans'] }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="text-sm text-gray-900">৳{{ number_format($branch['total_disbursed'], 0) }}</div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex items-center">
                                    <div class="text-sm text-gray-900">{{ $branch['collection_rate'] }}%</div>
                                    <div class="ml-2 w-16 bg-gray-200 rounded-full h-2">
                                        <div class="bg-primary-600 h-2 rounded-full" style="width: {{ min($branch['collection_rate'], 100) }}%"></div>
                                    </div>
                                </div>
                            </td>
                        </tr>
                    @empty
                        <tr>
                            <td colspan="5" class="px-6 py-4 text-center text-gray-500">No branches found</td>
                        </tr>
                    @endforelse
                </tbody>
            </table>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-medium text-gray-900">Quick Actions</h3>
            <p class="text-sm text-gray-500">Frequently used administrative functions</p>
        </div>
        <div class="p-6">
            <div class="grid grid-cols-1 gap-4">
                <a href="{{ route('admin.members.create') }}" class="flex items-center p-4 bg-primary-50 hover:bg-primary-100 rounded-lg transition-colors duration-200 group">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-primary-100 group-hover:bg-primary-200 rounded-lg flex items-center justify-center">
                            <i class="fas fa-user-plus text-primary-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">Add New Member</div>
                        <div class="text-xs text-gray-500">Register a new microfinance member</div>
                    </div>
                    <div class="ml-auto">
                        <i class="fas fa-chevron-right text-gray-400 group-hover:text-gray-600"></i>
                    </div>
                </a>

                <a href="{{ route('admin.branches.create') }}" class="flex items-center p-4 bg-secondary-50 hover:bg-secondary-100 rounded-lg transition-colors duration-200 group">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-secondary-100 group-hover:bg-secondary-200 rounded-lg flex items-center justify-center">
                            <i class="fas fa-building text-secondary-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">Add New Branch</div>
                        <div class="text-xs text-gray-500">Create a new branch location</div>
                    </div>
                    <div class="ml-auto">
                        <i class="fas fa-chevron-right text-gray-400 group-hover:text-gray-600"></i>
                    </div>
                </a>

                <a href="{{ route('admin.loans.pending') }}" class="flex items-center p-4 bg-warning-50 hover:bg-warning-100 rounded-lg transition-colors duration-200 group">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-warning-100 group-hover:bg-warning-200 rounded-lg flex items-center justify-center">
                            <i class="fas fa-file-alt text-warning-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">Review Loan Applications</div>
                        <div class="text-xs text-gray-500">{{ $stats['pending_applications'] ?? 0 }} pending applications</div>
                    </div>
                    <div class="ml-auto">
                        @if(($stats['pending_applications'] ?? 0) > 0)
                            <span class="bg-warning-100 text-warning-800 text-xs px-2 py-1 rounded-full mr-2">{{ $stats['pending_applications'] }}</span>
                        @endif
                        <i class="fas fa-chevron-right text-gray-400 group-hover:text-gray-600"></i>
                    </div>
                </a>

                <a href="{{ route('admin.analytics') }}" class="flex items-center p-4 bg-accent-50 hover:bg-accent-100 rounded-lg transition-colors duration-200 group">
                    <div class="flex-shrink-0">
                        <div class="w-10 h-10 bg-accent-100 group-hover:bg-accent-200 rounded-lg flex items-center justify-center">
                            <i class="fas fa-chart-bar text-accent-600"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-gray-900">View Analytics</div>
                        <div class="text-xs text-gray-500">Detailed reports and insights</div>
                    </div>
                    <div class="ml-auto">
                        <i class="fas fa-chevron-right text-gray-400 group-hover:text-gray-600"></i>
                    </div>
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activities -->
<div class="bg-white overflow-hidden shadow-sm rounded-lg border border-gray-200">
    <div class="px-6 py-4 border-b border-gray-200">
        <h3 class="text-lg font-medium text-gray-900">Recent Activities</h3>
        <p class="text-sm text-gray-500">Latest system activities and transactions</p>
    </div>
    <div class="divide-y divide-gray-200">
        @forelse($recentActivities as $activity)
            <div class="px-6 py-4 hover:bg-gray-50">
                <div class="flex items-center justify-between">
                    <div class="flex items-center">
                        <div class="flex-shrink-0">
                            @if($activity['type'] === 'loan_application')
                                <div class="w-8 h-8 bg-warning-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-file-alt text-warning-600 text-sm"></i>
                                </div>
                            @elseif($activity['type'] === 'loan_approval')
                                <div class="w-8 h-8 bg-success-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-check-circle text-success-600 text-sm"></i>
                                </div>
                            @else
                                <div class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-info-circle text-gray-600 text-sm"></i>
                                </div>
                            @endif
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-gray-900">{{ $activity['description'] }}</div>
                            <div class="text-xs text-gray-500">{{ $activity['date']->diffForHumans() }}</div>
                        </div>
                    </div>
                    <div class="flex items-center">
                        <div class="text-sm font-medium text-gray-900">৳{{ number_format($activity['amount'], 0) }}</div>
                        <div class="ml-2">
                            @if($activity['status'] === 'pending')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-warning-100 text-warning-800">
                                    Pending
                                </span>
                            @elseif($activity['status'] === 'approved')
                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-success-100 text-success-800">
                                    Approved
                                </span>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @empty
            <div class="px-6 py-8 text-center">
                <i class="fas fa-inbox text-4xl text-gray-300 mb-4"></i>
                <p class="text-gray-500">No recent activities</p>
            </div>
        @endforelse
    </div>
    @if($recentActivities->count() > 0)
        <div class="px-6 py-3 bg-gray-50 border-t border-gray-200">
            <a href="{{ route('admin.activities') }}" class="text-sm font-medium text-primary-600 hover:text-primary-500">
                View all activities
                <i class="fas fa-arrow-right ml-1"></i>
            </a>
        </div>
    @endif
</div>

@push('scripts')
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Monthly Disbursement Chart
    const disbursementCtx = document.getElementById('disbursementChart').getContext('2d');
    const disbursementData = @json($monthlyDisbursement);

    const disbursementLabels = disbursementData.map(item => {
        const date = new Date(item.year, item.month - 1);
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });

    const disbursementAmounts = disbursementData.map(item => item.total_amount);

    new Chart(disbursementCtx, {
        type: 'line',
        data: {
            labels: disbursementLabels,
            datasets: [{
                label: 'Loan Disbursement (৳)',
                data: disbursementAmounts,
                borderColor: 'rgb(34, 197, 94)',
                backgroundColor: 'rgba(34, 197, 94, 0.1)',
                borderWidth: 2,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '৳' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Amount: ৳' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });

    // Collection Trends Chart
    const collectionCtx = document.getElementById('collectionChart').getContext('2d');
    const collectionData = @json($collectionTrends);

    const collectionLabels = collectionData.map(item => {
        const date = new Date(item.year, item.month - 1);
        return date.toLocaleDateString('en-US', { month: 'short', year: 'numeric' });
    });

    const collectionAmounts = collectionData.map(item => item.total_collected);

    new Chart(collectionCtx, {
        type: 'bar',
        data: {
            labels: collectionLabels,
            datasets: [{
                label: 'Collections (৳)',
                data: collectionAmounts,
                backgroundColor: 'rgba(59, 130, 246, 0.8)',
                borderColor: 'rgb(59, 130, 246)',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return '৳' + value.toLocaleString();
                        }
                    }
                }
            },
            plugins: {
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            return 'Collections: ৳' + context.parsed.y.toLocaleString();
                        }
                    }
                }
            }
        }
    });
});
</script>
@endpush
@endsection
