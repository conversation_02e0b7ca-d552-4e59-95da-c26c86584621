@props([
    'name' => 'file',
    'label' => 'Upload File',
    'accept' => '*',
    'maxSize' => '5MB',
    'multiple' => false,
    'preview' => true,
    'category' => 'documents',
    'required' => false,
    'helpText' => null,
    'existingFiles' => [],
    'allowedTypes' => null
])

<div class="file-upload-component" x-data="fileUpload({
    name: '{{ $name }}',
    multiple: {{ $multiple ? 'true' : 'false' }},
    preview: {{ $preview ? 'true' : 'false' }},
    category: '{{ $category }}',
    maxSize: '{{ $maxSize }}',
    allowedTypes: {{ $allowedTypes ? json_encode($allowedTypes) : 'null' }},
    existingFiles: {{ json_encode($existingFiles) }}
})">
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
        
        @if($helpText)
            <p class="text-sm text-gray-500 mb-2">{{ $helpText }}</p>
        @endif
        
        <!-- File Input Area -->
        <div class="relative">
            <input 
                type="file" 
                name="{{ $name }}{{ $multiple ? '[]' : '' }}"
                id="{{ $name }}"
                accept="{{ $accept }}"
                {{ $multiple ? 'multiple' : '' }}
                {{ $required ? 'required' : '' }}
                class="hidden"
                x-ref="fileInput"
                @change="handleFileSelect($event)"
            >
            
            <!-- Drop Zone -->
            <div 
                class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer transition-colors hover:border-blue-400 hover:bg-blue-50"
                :class="{ 'border-blue-400 bg-blue-50': isDragOver, 'border-red-400 bg-red-50': hasError }"
                @click="$refs.fileInput.click()"
                @dragover.prevent="isDragOver = true"
                @dragleave.prevent="isDragOver = false"
                @drop.prevent="handleDrop($event)"
            >
                <div class="flex flex-col items-center">
                    <svg class="w-12 h-12 text-gray-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    <p class="text-lg font-medium text-gray-700 mb-2">
                        Drop files here or click to browse
                    </p>
                    <p class="text-sm text-gray-500">
                        Maximum file size: {{ $maxSize }}
                        @if($allowedTypes)
                            <br>Allowed types: {{ implode(', ', $allowedTypes) }}
                        @endif
                    </p>
                </div>
            </div>
        </div>
        
        <!-- Error Messages -->
        <div x-show="hasError" class="mt-2">
            <template x-for="error in errors" :key="error">
                <p class="text-sm text-red-600" x-text="error"></p>
            </template>
        </div>
        
        <!-- Upload Progress -->
        <div x-show="isUploading" class="mt-4">
            <div class="bg-gray-200 rounded-full h-2">
                <div 
                    class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                    :style="`width: ${uploadProgress}%`"
                ></div>
            </div>
            <p class="text-sm text-gray-600 mt-1" x-text="`Uploading... ${uploadProgress}%`"></p>
        </div>
        
        <!-- File Previews -->
        <div x-show="selectedFiles.length > 0 || existingFiles.length > 0" class="mt-4">
            <h4 class="text-sm font-medium text-gray-700 mb-2">Selected Files:</h4>
            <div class="space-y-2">
                <!-- Existing Files -->
                <template x-for="(file, index) in existingFiles" :key="`existing-${index}`">
                    <div class="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <!-- File Icon -->
                            <div class="flex-shrink-0">
                                <template x-if="file.type && file.type.startsWith('image/')">
                                    <img 
                                        :src="file.url || file.thumbnail_url" 
                                        :alt="file.name"
                                        class="w-10 h-10 object-cover rounded"
                                    >
                                </template>
                                <template x-if="!file.type || !file.type.startsWith('image/')">
                                    <div class="w-10 h-10 bg-blue-100 rounded flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                </template>
                            </div>
                            
                            <!-- File Info -->
                            <div>
                                <p class="text-sm font-medium text-gray-900" x-text="file.name || file.original_name"></p>
                                <p class="text-xs text-gray-500" x-text="formatFileSize(file.size)"></p>
                            </div>
                        </div>
                        
                        <!-- Actions -->
                        <div class="flex items-center space-x-2">
                            <button 
                                type="button"
                                @click="downloadFile(file)"
                                class="text-blue-600 hover:text-blue-800 text-sm"
                            >
                                Download
                            </button>
                            <button 
                                type="button"
                                @click="removeExistingFile(index)"
                                class="text-red-600 hover:text-red-800 text-sm"
                            >
                                Remove
                            </button>
                        </div>
                    </div>
                </template>
                
                <!-- New Selected Files -->
                <template x-for="(file, index) in selectedFiles" :key="`new-${index}`">
                    <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <div class="flex items-center space-x-3">
                            <!-- File Preview -->
                            <div class="flex-shrink-0">
                                <template x-if="file.type.startsWith('image/') && file.preview">
                                    <img 
                                        :src="file.preview" 
                                        :alt="file.name"
                                        class="w-10 h-10 object-cover rounded"
                                    >
                                </template>
                                <template x-if="!file.type.startsWith('image/')">
                                    <div class="w-10 h-10 bg-blue-100 rounded flex items-center justify-center">
                                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                        </svg>
                                    </div>
                                </template>
                            </div>
                            
                            <!-- File Info -->
                            <div>
                                <p class="text-sm font-medium text-gray-900" x-text="file.name"></p>
                                <p class="text-xs text-gray-500" x-text="formatFileSize(file.size)"></p>
                                <div x-show="file.uploadProgress !== undefined" class="w-32 bg-gray-200 rounded-full h-1 mt-1">
                                    <div 
                                        class="bg-blue-600 h-1 rounded-full transition-all duration-300"
                                        :style="`width: ${file.uploadProgress}%`"
                                    ></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Remove Button -->
                        <button 
                            type="button"
                            @click="removeFile(index)"
                            class="text-red-600 hover:text-red-800"
                        >
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </template>
            </div>
        </div>
    </div>
</div>

<script>
function fileUpload(config) {
    return {
        selectedFiles: [],
        existingFiles: config.existingFiles || [],
        isDragOver: false,
        isUploading: false,
        uploadProgress: 0,
        hasError: false,
        errors: [],
        
        init() {
            // Initialize component
        },
        
        handleFileSelect(event) {
            this.processFiles(event.target.files);
        },
        
        handleDrop(event) {
            this.isDragOver = false;
            this.processFiles(event.dataTransfer.files);
        },
        
        processFiles(files) {
            this.clearErrors();
            
            Array.from(files).forEach(file => {
                if (this.validateFile(file)) {
                    this.addFile(file);
                }
            });
        },
        
        validateFile(file) {
            // Check file size
            const maxSizeBytes = this.parseSize(config.maxSize);
            if (file.size > maxSizeBytes) {
                this.addError(`File "${file.name}" exceeds maximum size of ${config.maxSize}`);
                return false;
            }
            
            // Check file type
            if (config.allowedTypes) {
                const extension = file.name.split('.').pop().toLowerCase();
                if (!config.allowedTypes.includes(extension)) {
                    this.addError(`File type "${extension}" is not allowed`);
                    return false;
                }
            }
            
            return true;
        },
        
        addFile(file) {
            const fileObj = {
                file: file,
                name: file.name,
                size: file.size,
                type: file.type,
                preview: null,
                uploadProgress: 0
            };
            
            // Generate preview for images
            if (config.preview && file.type.startsWith('image/')) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    fileObj.preview = e.target.result;
                };
                reader.readAsDataURL(file);
            }
            
            this.selectedFiles.push(fileObj);
        },
        
        removeFile(index) {
            this.selectedFiles.splice(index, 1);
        },
        
        removeExistingFile(index) {
            this.existingFiles.splice(index, 1);
        },
        
        downloadFile(file) {
            if (file.download_url) {
                window.open(file.download_url, '_blank');
            } else if (file.url) {
                window.open(file.url, '_blank');
            }
        },
        
        formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        parseSize(sizeStr) {
            const units = { 'B': 1, 'KB': 1024, 'MB': 1024*1024, 'GB': 1024*1024*1024 };
            const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
            if (match) {
                return parseFloat(match[1]) * units[match[2].toUpperCase()];
            }
            return 5 * 1024 * 1024; // Default 5MB
        },
        
        addError(message) {
            this.errors.push(message);
            this.hasError = true;
        },
        
        clearErrors() {
            this.errors = [];
            this.hasError = false;
        }
    };
}
</script>
