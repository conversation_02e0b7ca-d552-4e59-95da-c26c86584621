@props([
    'name' => 'photo',
    'label' => 'Upload Photo',
    'required' => false,
    'currentImage' => null,
    'maxSize' => '2MB',
    'dimensions' => '400x400',
    'helpText' => 'Upload a clear photo (JPG, PNG, or WebP format)'
])

<div class="image-upload-component" x-data="imageUpload({
    name: '{{ $name }}',
    currentImage: '{{ $currentImage }}',
    maxSize: '{{ $maxSize }}',
    dimensions: '{{ $dimensions }}'
})">
    <div class="mb-4">
        <label class="block text-sm font-medium text-gray-700 mb-2">
            {{ $label }}
            @if($required)
                <span class="text-red-500">*</span>
            @endif
        </label>
        
        @if($helpText)
            <p class="text-sm text-gray-500 mb-3">{{ $helpText }}</p>
        @endif
        
        <div class="flex items-start space-x-6">
            <!-- Current/Preview Image -->
            <div class="flex-shrink-0">
                <div class="relative">
                    <div 
                        class="w-32 h-32 border-2 border-gray-300 border-dashed rounded-lg flex items-center justify-center overflow-hidden bg-gray-50"
                        :class="{ 'border-blue-400': isDragOver }"
                    >
                        <!-- Image Preview -->
                        <img 
                            x-show="previewUrl || currentImageUrl" 
                            :src="previewUrl || currentImageUrl" 
                            alt="Preview"
                            class="w-full h-full object-cover rounded-lg"
                        >
                        
                        <!-- Placeholder -->
                        <div x-show="!previewUrl && !currentImageUrl" class="text-center">
                            <svg class="w-12 h-12 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                            </svg>
                            <p class="text-xs text-gray-500">No image</p>
                        </div>
                    </div>
                    
                    <!-- Remove Button -->
                    <button 
                        x-show="previewUrl || currentImageUrl"
                        type="button"
                        @click="removeImage()"
                        class="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center hover:bg-red-600 transition-colors"
                    >
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
            
            <!-- Upload Controls -->
            <div class="flex-1">
                <input 
                    type="file" 
                    :name="name"
                    :id="name"
                    accept="image/jpeg,image/jpg,image/png,image/webp"
                    {{ $required ? 'required' : '' }}
                    class="hidden"
                    x-ref="fileInput"
                    @change="handleFileSelect($event)"
                >
                
                <!-- Upload Button -->
                <button 
                    type="button"
                    @click="$refs.fileInput.click()"
                    class="inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    <svg class="w-5 h-5 mr-2 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"></path>
                    </svg>
                    Choose Image
                </button>
                
                <!-- Drag & Drop Area -->
                <div 
                    class="mt-3 p-4 border-2 border-dashed border-gray-300 rounded-lg text-center cursor-pointer transition-colors hover:border-blue-400 hover:bg-blue-50"
                    :class="{ 'border-blue-400 bg-blue-50': isDragOver, 'border-red-400 bg-red-50': hasError }"
                    @click="$refs.fileInput.click()"
                    @dragover.prevent="isDragOver = true"
                    @dragleave.prevent="isDragOver = false"
                    @drop.prevent="handleDrop($event)"
                >
                    <p class="text-sm text-gray-600">
                        Drag and drop an image here, or click to browse
                    </p>
                    <p class="text-xs text-gray-500 mt-1">
                        Max size: {{ $maxSize }} | Recommended: {{ $dimensions }}px
                    </p>
                </div>
                
                <!-- Error Messages -->
                <div x-show="hasError" class="mt-2">
                    <template x-for="error in errors" :key="error">
                        <p class="text-sm text-red-600" x-text="error"></p>
                    </template>
                </div>
                
                <!-- Image Info -->
                <div x-show="selectedFile" class="mt-3 text-sm text-gray-600">
                    <p><strong>File:</strong> <span x-text="selectedFile?.name"></span></p>
                    <p><strong>Size:</strong> <span x-text="formatFileSize(selectedFile?.size)"></span></p>
                    <p x-show="imageDimensions"><strong>Dimensions:</strong> <span x-text="imageDimensions"></span></p>
                </div>
                
                <!-- Upload Progress -->
                <div x-show="isUploading" class="mt-3">
                    <div class="bg-gray-200 rounded-full h-2">
                        <div 
                            class="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            :style="`width: ${uploadProgress}%`"
                        ></div>
                    </div>
                    <p class="text-sm text-gray-600 mt-1" x-text="`Uploading... ${uploadProgress}%`"></p>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function imageUpload(config) {
    return {
        selectedFile: null,
        previewUrl: null,
        currentImageUrl: config.currentImage || null,
        isDragOver: false,
        isUploading: false,
        uploadProgress: 0,
        hasError: false,
        errors: [],
        imageDimensions: null,
        
        init() {
            // Initialize component
        },
        
        handleFileSelect(event) {
            const file = event.target.files[0];
            if (file) {
                this.processFile(file);
            }
        },
        
        handleDrop(event) {
            this.isDragOver = false;
            const file = event.dataTransfer.files[0];
            if (file) {
                this.processFile(file);
            }
        },
        
        processFile(file) {
            this.clearErrors();
            
            if (this.validateFile(file)) {
                this.selectedFile = file;
                this.generatePreview(file);
                this.getImageDimensions(file);
            }
        },
        
        validateFile(file) {
            // Check if it's an image
            if (!file.type.startsWith('image/')) {
                this.addError('Please select an image file');
                return false;
            }
            
            // Check file type
            const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                this.addError('Only JPG, PNG, and WebP images are allowed');
                return false;
            }
            
            // Check file size
            const maxSizeBytes = this.parseSize(config.maxSize);
            if (file.size > maxSizeBytes) {
                this.addError(`Image size must be less than ${config.maxSize}`);
                return false;
            }
            
            return true;
        },
        
        generatePreview(file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                this.previewUrl = e.target.result;
                this.currentImageUrl = null; // Hide current image when new one is selected
            };
            reader.readAsDataURL(file);
        },
        
        getImageDimensions(file) {
            const img = new Image();
            img.onload = () => {
                this.imageDimensions = `${img.width} × ${img.height}px`;
                
                // Validate dimensions if needed
                const [recommendedWidth, recommendedHeight] = config.dimensions.split('x').map(Number);
                if (img.width < recommendedWidth * 0.5 || img.height < recommendedHeight * 0.5) {
                    this.addError(`Image is too small. Recommended minimum: ${recommendedWidth/2} × ${recommendedHeight/2}px`);
                }
            };
            img.src = URL.createObjectURL(file);
        },
        
        removeImage() {
            this.selectedFile = null;
            this.previewUrl = null;
            this.currentImageUrl = null;
            this.imageDimensions = null;
            this.clearErrors();
            
            // Clear the file input
            this.$refs.fileInput.value = '';
        },
        
        formatFileSize(bytes) {
            if (!bytes) return '';
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        },
        
        parseSize(sizeStr) {
            const units = { 'B': 1, 'KB': 1024, 'MB': 1024*1024, 'GB': 1024*1024*1024 };
            const match = sizeStr.match(/^(\d+(?:\.\d+)?)\s*(B|KB|MB|GB)$/i);
            if (match) {
                return parseFloat(match[1]) * units[match[2].toUpperCase()];
            }
            return 2 * 1024 * 1024; // Default 2MB
        },
        
        addError(message) {
            this.errors.push(message);
            this.hasError = true;
        },
        
        clearErrors() {
            this.errors = [];
            this.hasError = false;
        }
    };
}
</script>
