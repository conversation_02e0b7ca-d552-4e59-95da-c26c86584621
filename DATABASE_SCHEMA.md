# Sonali Microfinance Database Schema

## Overview
This document describes the database schema for the Sonali Microfinance System, designed to manage microfinance operations including member management, loan processing, savings accounts, and branch operations.

## Tables and Relationships

### 1. users (Extended Laravel Users Table)
**Purpose**: System users with role-based access control

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| name | varchar(255) | NOT NULL | User's full name |
| email | varchar(255) | NOT NULL, UNIQUE | Email address |
| email_verified_at | timestamp | NULL | Email verification timestamp |
| password | varchar(255) | NOT NULL | Hashed password |
| role | enum | NOT NULL, DEFAULT 'member' | User role: admin, manager, field_officer, member |
| member_id | varchar(20) | NULL, UNIQUE | Link to members table |
| branch_id | bigint unsigned | NULL, FK | Assigned branch |
| is_active | boolean | DEFAULT true | Account status |
| remember_token | varchar(100) | NULL | Remember token |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: role, member_id, branch_id, is_active
**Foreign Keys**: branch_id → branches(id)

### 2. branches
**Purpose**: Microfinance branch locations and management

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| name | varchar(100) | NOT NULL | Branch name |
| address | text | NOT NULL | Branch address |
| manager_id | bigint unsigned | NULL, FK | Branch manager |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: manager_id, name
**Foreign Keys**: manager_id → users(id)

### 3. members
**Purpose**: Customer/member profiles and personal information

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| member_id | varchar(20) | NOT NULL, UNIQUE | Unique member identifier |
| name | varchar(100) | NOT NULL | Member's full name |
| father_or_husband_name | varchar(100) | NOT NULL | Father or husband name |
| mother_name | varchar(100) | NOT NULL | Mother's name |
| present_address | text | NOT NULL | Current address |
| permanent_address | text | NOT NULL | Permanent address |
| nid_number | varchar(20) | NOT NULL, UNIQUE | National ID number |
| date_of_birth | date | NOT NULL | Date of birth |
| religion | enum | DEFAULT 'islam' | Religion: islam, hinduism, christianity, buddhism, other |
| phone_number | varchar(15) | NOT NULL | Contact phone number |
| blood_group | enum | NULL | Blood group: A+, A-, B+, B-, AB+, AB-, O+, O- |
| photo | varchar(255) | NULL | Photo file path |
| occupation | varchar(100) | NOT NULL | Member's occupation |
| reference_id | bigint unsigned | NULL, FK | Reference member |
| branch_id | bigint unsigned | NOT NULL, FK | Assigned branch |
| created_by | bigint unsigned | NOT NULL, FK | Created by user |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: member_id, nid_number, phone_number, branch_id, created_by, reference_id
**Foreign Keys**: 
- reference_id → members(id)
- branch_id → branches(id)
- created_by → users(id)

### 4. loan_applications
**Purpose**: Loan application requests and approval workflow

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| member_id | bigint unsigned | NOT NULL, FK | Applicant member |
| applied_amount | decimal(12,2) | NOT NULL | Requested loan amount |
| reason | text | NOT NULL | Loan purpose/reason |
| loan_cycle_number | integer | DEFAULT 1 | Loan cycle for member |
| recommender | varchar(100) | NULL | Recommender name |
| status | enum | DEFAULT 'pending' | Status: pending, approved, rejected |
| advance_payment | decimal(10,2) | DEFAULT 0 | Advance payment amount |
| reviewed_by | bigint unsigned | NULL, FK | Reviewer user |
| reviewed_at | timestamp | NULL | Review timestamp |
| applied_at | timestamp | DEFAULT CURRENT_TIMESTAMP | Application timestamp |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: member_id, status, reviewed_by, applied_at, loan_cycle_number
**Foreign Keys**: 
- member_id → members(id)
- reviewed_by → users(id)

### 5. loans
**Purpose**: Approved loans with repayment terms

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| loan_application_id | bigint unsigned | NOT NULL, UNIQUE, FK | Source application |
| loan_date | date | NOT NULL | Loan disbursement date |
| loan_amount | decimal(12,2) | NOT NULL | Approved loan amount |
| total_repayment_amount | decimal(12,2) | NOT NULL | Total amount to repay |
| repayment_duration | integer | NOT NULL | Duration in weeks/months |
| repayment_method | enum | DEFAULT 'weekly' | Method: weekly, monthly |
| installment_count | integer | NOT NULL | Number of installments |
| installment_amount | decimal(10,2) | NOT NULL | Amount per installment |
| advance_payment | decimal(10,2) | DEFAULT 0 | Advance payment |
| first_installment_date | date | NOT NULL | First payment due date |
| last_installment_date | date | NOT NULL | Final payment due date |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: loan_application_id, loan_date, repayment_method, first_installment_date, last_installment_date
**Foreign Keys**: loan_application_id → loan_applications(id)

### 6. installments
**Purpose**: Individual loan installment tracking

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| loan_id | bigint unsigned | NOT NULL, FK | Parent loan |
| installment_no | integer | NOT NULL | Installment number |
| installment_date | date | NOT NULL | Due date |
| installment_amount | decimal(10,2) | NOT NULL | Amount due |
| advance_paid | decimal(10,2) | DEFAULT 0 | Advance payment |
| due_amount | decimal(10,2) | DEFAULT 0 | Remaining amount |
| collected_by | bigint unsigned | NULL, FK | Collector user |
| collection_date | date | NULL | Payment date |
| status | enum | DEFAULT 'pending' | Status: pending, paid, overdue |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: loan_id, installment_no, installment_date, status, collected_by, collection_date
**Unique Constraints**: (loan_id, installment_no)
**Foreign Keys**: 
- loan_id → loans(id)
- collected_by → users(id)

### 7. branch_transactions
**Purpose**: Branch-level financial transactions

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| branch_id | bigint unsigned | NOT NULL, FK | Branch |
| entry_type | enum | NOT NULL | Type: income, expense |
| serial_no | integer unsigned | NOT NULL | Serial number per branch |
| date | date | NOT NULL | Transaction date |
| description | text | NOT NULL | Transaction description |
| account_no | varchar(50) | NULL | Account number |
| category | varchar(100) | NOT NULL | Transaction category |
| voucher_no | varchar(50) | NULL | Voucher number |
| amount | decimal(12,2) | NOT NULL | Transaction amount |
| entered_by | bigint unsigned | NOT NULL, FK | Entry user |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: branch_id, entry_type, date, category, entered_by, voucher_no
**Unique Constraints**: (branch_id, serial_no)
**Foreign Keys**: 
- branch_id → branches(id)
- entered_by → users(id)

### 8. saving_accounts
**Purpose**: Member savings accounts and deposit schemes

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| member_id | bigint unsigned | NOT NULL, FK | Account holder |
| saving_type | enum | DEFAULT 'general' | Type: general, dps, fdr |
| joint_photo | varchar(255) | NULL | Joint account photo |
| nominee_name | varchar(100) | NULL | Nominee name |
| nominee_relation | varchar(50) | NULL | Nominee relationship |
| saving_method | enum | DEFAULT 'monthly' | Method: daily, monthly |
| monthly_amount | decimal(10,2) | DEFAULT 0 | Monthly deposit amount |
| fdr_amount | decimal(12,2) | NULL | Fixed deposit amount |
| start_date | date | NOT NULL | Account start date |
| created_by | bigint unsigned | NOT NULL, FK | Creator user |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: member_id, saving_type, saving_method, start_date, created_by
**Foreign Keys**: 
- member_id → members(id)
- created_by → users(id)

### 9. advertisements
**Purpose**: System advertisements and announcements

| Column | Type | Constraints | Description |
|--------|------|-------------|-------------|
| id | bigint unsigned | PK, AUTO_INCREMENT | Primary key |
| title | varchar(200) | NOT NULL | Advertisement title |
| image | varchar(255) | NOT NULL | Image file path |
| link_url | varchar(255) | NULL | Optional link URL |
| active | boolean | DEFAULT true | Active status |
| created_at | timestamp | NULL | Creation timestamp |
| updated_at | timestamp | NULL | Last update timestamp |

**Indexes**: active, title

## Key Relationships

1. **Users ↔ Branches**: Many-to-many (users can be assigned to branches, branches have managers)
2. **Branches → Members**: One-to-many (each member belongs to one branch)
3. **Members → Loan Applications**: One-to-many (members can have multiple loan applications)
4. **Loan Applications → Loans**: One-to-one (approved applications become loans)
5. **Loans → Installments**: One-to-many (loans have multiple installments)
6. **Members → Saving Accounts**: One-to-many (members can have multiple savings accounts)
7. **Branches → Branch Transactions**: One-to-many (branches have multiple transactions)

## Security Features

- All foreign key constraints with appropriate cascade/set null actions
- Unique constraints on critical identifiers (member_id, nid_number, email)
- Indexed columns for optimal query performance
- Enum constraints for data integrity
- Proper data types with appropriate precision for financial amounts

## Migration Order

1. Users table modification
2. Branches table creation
3. Members table creation
4. Loan applications table creation
5. Loans table creation
6. Installments table creation
7. Branch transactions table creation
8. Saving accounts table creation
9. Advertisements table creation
10. Foreign key constraints addition
