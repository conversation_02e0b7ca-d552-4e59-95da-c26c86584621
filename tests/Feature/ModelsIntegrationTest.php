<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\LoanApplication;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\SavingAccount;
use App\Models\BranchTransaction;
use App\Models\Advertisement;

class ModelsIntegrationTest extends TestCase
{
    use RefreshDatabase;

    public function test_user_model_role_methods()
    {
        $admin = User::factory()->create(['role' => 'admin']);
        $manager = User::factory()->create(['role' => 'manager']);
        $fieldOfficer = User::factory()->create(['role' => 'field_officer']);
        $member = User::factory()->create(['role' => 'member']);

        $this->assertTrue($admin->isAdmin());
        $this->assertTrue($manager->isManager());
        $this->assertTrue($fieldOfficer->isFieldOfficer());
        $this->assertTrue($member->isMember());

        $this->assertEquals('Administrator', $admin->display_role);
        $this->assertEquals('Branch Manager', $manager->display_role);
    }

    public function test_branch_model_relationships()
    {
        $manager = User::factory()->create(['role' => 'manager']);
        $branch = Branch::factory()->create(['manager_id' => $manager->id]);

        $this->assertEquals($manager->id, $branch->manager->id);
        $this->assertTrue($branch->managedBranches()->exists());
    }

    public function test_member_model_business_logic()
    {
        $branch = Branch::factory()->create();
        $user = User::factory()->create(['role' => 'field_officer']);
        
        $member = Member::factory()->create([
            'branch_id' => $branch->id,
            'created_by' => $user->id,
            'date_of_birth' => now()->subYears(30),
        ]);

        $this->assertEquals(30, $member->age);
        $this->assertTrue($member->canApplyForLoan());
        $this->assertEquals(1, $member->getNextLoanCycleNumber());
    }

    public function test_loan_application_workflow()
    {
        $branch = Branch::factory()->create();
        $member = Member::factory()->create(['branch_id' => $branch->id]);
        $reviewer = User::factory()->create(['role' => 'manager']);

        // Create loan application
        $application = LoanApplication::factory()->create([
            'member_id' => $member->id,
            'applied_amount' => 50000,
            'status' => 'pending',
        ]);

        $this->assertEquals('pending', $application->status);
        $this->assertTrue($application->member->is($member));

        // Test repayment calculation
        $monthlyPayment = $application->calculateRepaymentAmount(12.0, 12);
        $this->assertGreaterThan(0, $monthlyPayment);

        // Approve application
        $loan = $application->approve($reviewer, [
            'loan_amount' => 50000,
            'interest_rate' => 12.0,
            'loan_duration_months' => 12,
            'repayment_method' => 'monthly',
        ]);

        $this->assertEquals('approved', $application->fresh()->status);
        $this->assertInstanceOf(Loan::class, $loan);
        $this->assertEquals(50000, $loan->loan_amount);
    }

    public function test_loan_installment_generation()
    {
        $application = LoanApplication::factory()->create();
        $loan = Loan::factory()->create([
            'loan_application_id' => $application->id,
            'loan_amount' => 60000,
            'loan_duration_months' => 12,
            'repayment_method' => 'monthly',
        ]);

        $loan->generateInstallments();

        $this->assertEquals(12, $loan->installments()->count());
        
        $firstInstallment = $loan->installments()->orderBy('installment_no')->first();
        $this->assertEquals(1, $firstInstallment->installment_no);
        $this->assertEquals('pending', $firstInstallment->status);
    }

    public function test_installment_payment_process()
    {
        $loan = Loan::factory()->create();
        $installment = Installment::factory()->create([
            'loan_id' => $loan->id,
            'amount' => 5000,
            'status' => 'pending',
            'installment_date' => now()->subDays(5), // Overdue
        ]);

        $collector = User::factory()->create(['role' => 'field_officer']);

        $this->assertTrue($installment->isOverdue());
        $this->assertEquals(5, $installment->getDaysOverdue());
        $this->assertGreaterThan(0, $installment->calculateLateFee());

        // Mark as paid
        $installment->markAsPaid($collector, 25, 'Collected with late fee');

        $this->assertEquals('paid', $installment->fresh()->status);
        $this->assertEquals($collector->id, $installment->fresh()->collected_by);
        $this->assertEquals(25, $installment->fresh()->late_fee);
    }

    public function test_saving_account_operations()
    {
        $member = Member::factory()->create();
        $user = User::factory()->create(['role' => 'field_officer']);

        $savingAccount = SavingAccount::factory()->create([
            'member_id' => $member->id,
            'opening_balance' => 1000,
            'current_balance' => 1000,
            'minimum_balance' => 500,
            'created_by' => $user->id,
        ]);

        // Test deposit
        $depositTransaction = $savingAccount->addDeposit(2000, $user, 'Monthly deposit');
        
        $this->assertEquals(3000, $savingAccount->fresh()->current_balance);
        $this->assertEquals('deposit', $depositTransaction->transaction_type);
        $this->assertEquals(2000, $depositTransaction->amount);

        // Test withdrawal
        $withdrawalTransaction = $savingAccount->addWithdrawal(1000, $user, 'Emergency withdrawal');
        
        $this->assertEquals(2000, $savingAccount->fresh()->current_balance);
        $this->assertEquals('withdrawal', $withdrawalTransaction->transaction_type);

        // Test available balance
        $this->assertEquals(1500, $savingAccount->getAvailableBalance());
        $this->assertTrue($savingAccount->canWithdraw(1000));
        $this->assertFalse($savingAccount->canWithdraw(2000));
    }

    public function test_branch_transaction_calculations()
    {
        $branch = Branch::factory()->create();
        $user = User::factory()->create();

        // Create income transaction
        BranchTransaction::factory()->create([
            'branch_id' => $branch->id,
            'transaction_type' => 'income',
            'amount' => 10000,
            'entered_by' => $user->id,
            'transaction_date' => now(),
        ]);

        // Create expense transaction
        BranchTransaction::factory()->create([
            'branch_id' => $branch->id,
            'transaction_type' => 'expense',
            'amount' => 3000,
            'entered_by' => $user->id,
            'transaction_date' => now(),
        ]);

        $totalIncome = BranchTransaction::getTotalIncomeForBranch($branch->id);
        $totalExpense = BranchTransaction::getTotalExpenseForBranch($branch->id);
        $netIncome = BranchTransaction::getNetIncomeForBranch($branch->id);

        $this->assertEquals(10000, $totalIncome);
        $this->assertEquals(3000, $totalExpense);
        $this->assertEquals(7000, $netIncome);
    }

    public function test_advertisement_functionality()
    {
        $ad = Advertisement::factory()->create([
            'title' => 'Test Advertisement',
            'is_active' => true,
            'start_date' => now()->subDays(1),
            'end_date' => now()->addDays(10),
            'view_count' => 100,
            'click_count' => 5,
        ]);

        $this->assertTrue($ad->isCurrentlyActive());
        $this->assertFalse($ad->isExpired());
        $this->assertEquals(5.0, $ad->getClickThroughRate());

        $ad->incrementViewCount();
        $ad->incrementClickCount();

        $this->assertEquals(101, $ad->fresh()->view_count);
        $this->assertEquals(6, $ad->fresh()->click_count);
    }

    public function test_model_scopes()
    {
        // Test User scopes
        User::factory()->create(['role' => 'admin', 'is_active' => true]);
        User::factory()->create(['role' => 'manager', 'is_active' => false]);

        $this->assertEquals(1, User::admins()->count());
        $this->assertEquals(1, User::active()->count());

        // Test Advertisement scopes
        Advertisement::factory()->create(['is_active' => true, 'end_date' => now()->addDays(5)]);
        Advertisement::factory()->create(['is_active' => false]);
        Advertisement::factory()->create(['is_active' => true, 'end_date' => now()->subDays(1)]);

        $this->assertEquals(1, Advertisement::active()->count());
        $this->assertEquals(1, Advertisement::expired()->count());
    }
}
