<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use App\Models\User;
use App\Models\Branch;
use App\Models\Advertisement;

class AuthenticationSystemTest extends TestCase
{
    use RefreshDatabase;

    public function test_login_page_displays_correctly()
    {
        $response = $this->get('/login');
        
        $response->assertStatus(200);
        $response->assertSee('Welcome Back');
        $response->assertSee('Sign in to your Sonali Microfinance account');
    }

    public function test_login_page_shows_advertisements()
    {
        // Create test advertisement
        Advertisement::factory()->create([
            'title' => 'Test Advertisement',
            'content' => 'This is a test advertisement content',
            'position' => 'login',
            'is_active' => true,
            'priority' => 5,
        ]);

        $response = $this->get('/login');
        
        $response->assertStatus(200);
        $response->assertSee('Test Advertisement');
    }

    public function test_admin_can_login_and_redirect_to_admin_dashboard()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $response = $this->post('/login', [
            'email' => $admin->email,
            'password' => 'password',
        ]);

        $response->assertRedirect('/admin/dashboard');
        $this->assertAuthenticatedAs($admin);
    }

    public function test_manager_can_login_and_redirect_to_manager_dashboard()
    {
        $branch = Branch::factory()->create();
        $manager = User::factory()->create([
            'role' => 'manager',
            'branch_id' => $branch->id,
            'is_active' => true,
        ]);

        $response = $this->post('/login', [
            'email' => $manager->email,
            'password' => 'password',
        ]);

        $response->assertRedirect('/manager/dashboard');
        $this->assertAuthenticatedAs($manager);
    }

    public function test_field_officer_can_login_and_redirect_to_field_officer_dashboard()
    {
        $branch = Branch::factory()->create();
        $fieldOfficer = User::factory()->create([
            'role' => 'field_officer',
            'branch_id' => $branch->id,
            'is_active' => true,
        ]);

        $response = $this->post('/login', [
            'email' => $fieldOfficer->email,
            'password' => 'password',
        ]);

        $response->assertRedirect('/field-officer/dashboard');
        $this->assertAuthenticatedAs($fieldOfficer);
    }

    public function test_member_can_login_and_redirect_to_member_dashboard()
    {
        $branch = Branch::factory()->create();
        $member = User::factory()->create([
            'role' => 'member',
            'branch_id' => $branch->id,
            'is_active' => true,
        ]);

        $response = $this->post('/login', [
            'email' => $member->email,
            'password' => 'password',
        ]);

        $response->assertRedirect('/member/dashboard');
        $this->assertAuthenticatedAs($member);
    }

    public function test_inactive_user_cannot_login()
    {
        $user = User::factory()->create([
            'role' => 'member',
            'is_active' => false,
        ]);

        $response = $this->post('/login', [
            'email' => $user->email,
            'password' => 'password',
        ]);

        $response->assertRedirect('/login');
        $response->assertSessionHas('error', 'Your account has been deactivated. Please contact administrator.');
        $this->assertGuest();
    }

    public function test_invalid_credentials_show_error()
    {
        $response = $this->post('/login', [
            'email' => '<EMAIL>',
            'password' => 'wrongpassword',
        ]);

        $response->assertRedirect('/login');
        $response->assertSessionHas('error', 'Invalid email address or password. Please try again.');
        $this->assertGuest();
    }

    public function test_role_middleware_redirects_unauthorized_users()
    {
        $member = User::factory()->create([
            'role' => 'member',
            'is_active' => true,
        ]);

        $this->actingAs($member);

        // Member trying to access admin dashboard
        $response = $this->get('/admin/dashboard');
        
        $response->assertRedirect('/member/dashboard');
        $response->assertSessionHas('warning', 'You do not have permission to access the requested page.');
    }

    public function test_dashboard_redirects_to_role_specific_dashboard()
    {
        $admin = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $this->actingAs($admin);

        $response = $this->get('/dashboard');
        
        $response->assertRedirect('/admin/dashboard');
    }

    public function test_logout_works_correctly()
    {
        $user = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $this->actingAs($user);

        $response = $this->post('/logout');

        $response->assertRedirect('/login');
        $response->assertSessionHas('success', 'You have been successfully logged out.');
        $this->assertGuest();
    }

    public function test_remember_me_functionality()
    {
        $user = User::factory()->create([
            'role' => 'admin',
            'is_active' => true,
        ]);

        $response = $this->post('/login', [
            'email' => $user->email,
            'password' => 'password',
            'remember' => true,
        ]);

        $response->assertRedirect('/admin/dashboard');
        $this->assertAuthenticatedAs($user);
        
        // Check if remember token is set
        $this->assertNotNull($user->fresh()->remember_token);
    }

    public function test_guest_redirected_to_login()
    {
        $response = $this->get('/admin/dashboard');
        
        $response->assertRedirect('/login');
    }

    public function test_root_redirects_to_login()
    {
        $response = $this->get('/');
        
        $response->assertRedirect('/login');
    }
}
