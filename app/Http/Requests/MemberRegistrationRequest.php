<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Rules\BangladeshNidRule;
use App\Rules\BangladeshPhoneRule;
use App\Rules\AgeRestrictionRule;

class MemberRegistrationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               (auth()->user()->role === 'field_officer' || auth()->user()->role === 'manager' || auth()->user()->role === 'admin');
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-zA-Z\s\.\-\']+$/'
            ],
            'father_or_husband_name' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-zA-Z\s\.\-\']+$/'
            ],
            'mother_name' => [
                'required',
                'string',
                'min:2',
                'max:100',
                'regex:/^[a-zA-Z\s\.\-\']+$/'
            ],
            'date_of_birth' => [
                'required',
                'date',
                'before:today',
                new AgeRestrictionRule(18, 80)
            ],
            'nid_number' => [
                'required',
                'string',
                new BangladeshNidRule(),
                Rule::unique('members', 'nid_number')->ignore($this->member)
            ],
            'phone_number' => [
                'required',
                'string',
                new BangladeshPhoneRule(),
                Rule::unique('members', 'phone_number')->ignore($this->member)
            ],
            'present_address' => [
                'required',
                'string',
                'min:10',
                'max:500'
            ],
            'permanent_address' => [
                'required',
                'string',
                'min:10',
                'max:500'
            ],
            'occupation' => [
                'required',
                'string',
                'max:100'
            ],
            'religion' => [
                'required',
                'in:islam,hinduism,christianity,buddhism,other'
            ],
            'blood_group' => [
                'nullable',
                'in:A+,A-,B+,B-,AB+,AB-,O+,O-'
            ],
            'reference_member_id' => [
                'nullable',
                'exists:members,id'
            ],
            'photo' => [
                'nullable',
                'image',
                'mimes:jpeg,png,jpg',
                'max:2048', // 2MB
                'dimensions:min_width=200,min_height=200,max_width=2000,max_height=2000'
            ],
            'monthly_income' => [
                'required',
                'numeric',
                'min:0',
                'max:999999.99'
            ],
            'family_members' => [
                'required',
                'integer',
                'min:1',
                'max:20'
            ],
            'marital_status' => [
                'required',
                'in:single,married,divorced,widowed'
            ],
            'education_level' => [
                'nullable',
                'in:illiterate,primary,secondary,higher_secondary,graduate,post_graduate'
            ],
            'emergency_contact_name' => [
                'nullable',
                'string',
                'max:100'
            ],
            'emergency_contact_phone' => [
                'nullable',
                'string',
                new BangladeshPhoneRule()
            ],
            'emergency_contact_relation' => [
                'nullable',
                'string',
                'max:50'
            ]
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Member name is required.',
            'name.regex' => 'Member name can only contain letters, spaces, dots, hyphens, and apostrophes.',
            'father_or_husband_name.required' => 'Father or husband name is required.',
            'father_or_husband_name.regex' => 'Father or husband name can only contain letters, spaces, dots, hyphens, and apostrophes.',
            'mother_name.required' => 'Mother name is required.',
            'mother_name.regex' => 'Mother name can only contain letters, spaces, dots, hyphens, and apostrophes.',
            'date_of_birth.required' => 'Date of birth is required.',
            'date_of_birth.before' => 'Date of birth must be before today.',
            'nid_number.required' => 'National ID number is required.',
            'nid_number.unique' => 'This National ID number is already registered.',
            'phone_number.required' => 'Phone number is required.',
            'phone_number.unique' => 'This phone number is already registered.',
            'present_address.required' => 'Present address is required.',
            'present_address.min' => 'Present address must be at least 10 characters.',
            'permanent_address.required' => 'Permanent address is required.',
            'permanent_address.min' => 'Permanent address must be at least 10 characters.',
            'occupation.required' => 'Occupation is required.',
            'religion.required' => 'Religion is required.',
            'religion.in' => 'Please select a valid religion.',
            'blood_group.in' => 'Please select a valid blood group.',
            'photo.image' => 'Photo must be an image file.',
            'photo.mimes' => 'Photo must be a JPEG, PNG, or JPG file.',
            'photo.max' => 'Photo size must not exceed 2MB.',
            'photo.dimensions' => 'Photo dimensions must be between 200x200 and 2000x2000 pixels.',
            'monthly_income.required' => 'Monthly income is required.',
            'monthly_income.numeric' => 'Monthly income must be a valid number.',
            'monthly_income.min' => 'Monthly income cannot be negative.',
            'family_members.required' => 'Number of family members is required.',
            'family_members.integer' => 'Number of family members must be a whole number.',
            'family_members.min' => 'At least 1 family member is required.',
            'family_members.max' => 'Maximum 20 family members allowed.',
            'marital_status.required' => 'Marital status is required.',
            'marital_status.in' => 'Please select a valid marital status.',
            'education_level.in' => 'Please select a valid education level.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'nid_number' => 'National ID number',
            'phone_number' => 'phone number',
            'father_or_husband_name' => 'father or husband name',
            'mother_name' => 'mother name',
            'date_of_birth' => 'date of birth',
            'present_address' => 'present address',
            'permanent_address' => 'permanent address',
            'monthly_income' => 'monthly income',
            'family_members' => 'family members',
            'marital_status' => 'marital status',
            'education_level' => 'education level',
            'blood_group' => 'blood group',
            'reference_member_id' => 'reference member',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'emergency_contact_relation' => 'emergency contact relation'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => trim($this->name),
            'father_or_husband_name' => trim($this->father_or_husband_name),
            'mother_name' => trim($this->mother_name),
            'present_address' => trim($this->present_address),
            'permanent_address' => trim($this->permanent_address),
            'occupation' => trim($this->occupation),
            'nid_number' => preg_replace('/\D/', '', $this->nid_number), // Remove non-digits
            'phone_number' => preg_replace('/\D/', '', $this->phone_number), // Remove non-digits
        ]);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new \Illuminate\Http\Exceptions\HttpResponseException(
                response()->json([
                    'success' => false,
                    'message' => 'Validation failed',
                    'errors' => $validator->errors()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }
}
