<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use App\Rules\BangladeshPhoneRule;

class ProfileUpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $user = auth()->user();
        $updateType = $this->input('update_type', 'profile');

        $rules = [];

        // Basic profile update rules
        if ($updateType === 'profile') {
            $rules = [
                'name' => [
                    'required',
                    'string',
                    'min:2',
                    'max:255',
                    'regex:/^[a-zA-Z\s\.\-\']+$/'
                ],
                'email' => [
                    'required',
                    'string',
                    'email:rfc,dns',
                    'max:255',
                    Rule::unique('users', 'email')->ignore($user->id)
                ],
                'phone_number' => [
                    'nullable',
                    'string',
                    new BangladeshPhoneRule(),
                    Rule::unique('users', 'phone_number')->ignore($user->id)
                ],
                'avatar' => [
                    'nullable',
                    'image',
                    'mimes:jpeg,png,jpg',
                    'max:2048',
                    'dimensions:min_width=100,min_height=100,max_width=1000,max_height=1000'
                ]
            ];
        }

        // Password change rules
        if ($updateType === 'password') {
            $rules = [
                'current_password' => [
                    'required',
                    'string',
                    function ($attribute, $value, $fail) use ($user) {
                        if (!\Hash::check($value, $user->password)) {
                            $fail('Current password is incorrect.');
                        }
                    }
                ],
                'password' => [
                    'required',
                    'string',
                    Password::min(8)
                        ->letters()
                        ->mixedCase()
                        ->numbers()
                        ->symbols()
                        ->uncompromised(),
                    'confirmed',
                    'different:current_password'
                ]
            ];
        }

        // Member profile update (for member users)
        if ($updateType === 'member_profile' && $user->role === 'member' && $user->member) {
            $rules = [
                'present_address' => [
                    'required',
                    'string',
                    'min:10',
                    'max:500'
                ],
                'permanent_address' => [
                    'required',
                    'string',
                    'min:10',
                    'max:500'
                ],
                'phone_number' => [
                    'required',
                    'string',
                    new BangladeshPhoneRule(),
                    Rule::unique('members', 'phone_number')->ignore($user->member->id)
                ],
                'occupation' => [
                    'required',
                    'string',
                    'max:100'
                ],
                'monthly_income' => [
                    'required',
                    'numeric',
                    'min:0',
                    'max:999999.99'
                ],
                'family_members' => [
                    'required',
                    'integer',
                    'min:1',
                    'max:20'
                ],
                'marital_status' => [
                    'required',
                    'in:single,married,divorced,widowed'
                ],
                'education_level' => [
                    'nullable',
                    'in:illiterate,primary,secondary,higher_secondary,graduate,post_graduate'
                ],
                'emergency_contact_name' => [
                    'nullable',
                    'string',
                    'max:100'
                ],
                'emergency_contact_phone' => [
                    'nullable',
                    'string',
                    new BangladeshPhoneRule()
                ],
                'emergency_contact_relation' => [
                    'nullable',
                    'string',
                    'max:50'
                ],
                'photo' => [
                    'nullable',
                    'image',
                    'mimes:jpeg,png,jpg',
                    'max:2048',
                    'dimensions:min_width=200,min_height=200,max_width=2000,max_height=2000'
                ]
            ];
        }

        // Employee profile update (for staff users)
        if ($updateType === 'employee_profile' && in_array($user->role, ['admin', 'manager', 'field_officer'])) {
            $rules = [
                'phone_number' => [
                    'nullable',
                    'string',
                    new BangladeshPhoneRule(),
                    Rule::unique('users', 'phone_number')->ignore($user->id)
                ],
                'employee_id' => [
                    'nullable',
                    'string',
                    'max:20',
                    'alpha_num',
                    Rule::unique('users', 'employee_id')->ignore($user->id)
                ],
                'designation' => [
                    'nullable',
                    'string',
                    'max:100'
                ],
                'avatar' => [
                    'nullable',
                    'image',
                    'mimes:jpeg,png,jpg',
                    'max:2048',
                    'dimensions:min_width=100,min_height=100,max_width=1000,max_height=1000'
                ]
            ];
        }

        return $rules;
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'name.regex' => 'Name can only contain letters, spaces, dots, hyphens, and apostrophes.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already in use.',
            'phone_number.unique' => 'This phone number is already in use.',
            'current_password.required' => 'Current password is required.',
            'password.required' => 'New password is required.',
            'password.confirmed' => 'Password confirmation does not match.',
            'password.different' => 'New password must be different from current password.',
            'avatar.image' => 'Avatar must be an image file.',
            'avatar.mimes' => 'Avatar must be a JPEG, PNG, or JPG file.',
            'avatar.max' => 'Avatar size must not exceed 2MB.',
            'avatar.dimensions' => 'Avatar dimensions must be between 100x100 and 1000x1000 pixels.',
            'photo.image' => 'Photo must be an image file.',
            'photo.mimes' => 'Photo must be a JPEG, PNG, or JPG file.',
            'photo.max' => 'Photo size must not exceed 2MB.',
            'photo.dimensions' => 'Photo dimensions must be between 200x200 and 2000x2000 pixels.',
            'present_address.required' => 'Present address is required.',
            'present_address.min' => 'Present address must be at least 10 characters.',
            'permanent_address.required' => 'Permanent address is required.',
            'permanent_address.min' => 'Permanent address must be at least 10 characters.',
            'occupation.required' => 'Occupation is required.',
            'monthly_income.required' => 'Monthly income is required.',
            'monthly_income.numeric' => 'Monthly income must be a valid number.',
            'monthly_income.min' => 'Monthly income cannot be negative.',
            'family_members.required' => 'Number of family members is required.',
            'family_members.integer' => 'Number of family members must be a whole number.',
            'family_members.min' => 'At least 1 family member is required.',
            'family_members.max' => 'Maximum 20 family members allowed.',
            'marital_status.required' => 'Marital status is required.',
            'marital_status.in' => 'Please select a valid marital status.',
            'education_level.in' => 'Please select a valid education level.',
            'employee_id.unique' => 'This employee ID is already in use.',
            'employee_id.alpha_num' => 'Employee ID can only contain letters and numbers.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'phone_number' => 'phone number',
            'current_password' => 'current password',
            'present_address' => 'present address',
            'permanent_address' => 'permanent address',
            'monthly_income' => 'monthly income',
            'family_members' => 'family members',
            'marital_status' => 'marital status',
            'education_level' => 'education level',
            'emergency_contact_name' => 'emergency contact name',
            'emergency_contact_phone' => 'emergency contact phone',
            'emergency_contact_relation' => 'emergency contact relation',
            'employee_id' => 'employee ID'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $cleanData = [];

        if ($this->name) {
            $cleanData['name'] = trim($this->name);
        }

        if ($this->email) {
            $cleanData['email'] = strtolower(trim($this->email));
        }

        if ($this->phone_number) {
            $cleanData['phone_number'] = preg_replace('/\D/', '', $this->phone_number);
        }

        if ($this->present_address) {
            $cleanData['present_address'] = trim($this->present_address);
        }

        if ($this->permanent_address) {
            $cleanData['permanent_address'] = trim($this->permanent_address);
        }

        if ($this->occupation) {
            $cleanData['occupation'] = trim($this->occupation);
        }

        if ($this->employee_id) {
            $cleanData['employee_id'] = strtoupper(trim($this->employee_id));
        }

        if ($this->designation) {
            $cleanData['designation'] = trim($this->designation);
        }

        if ($this->emergency_contact_name) {
            $cleanData['emergency_contact_name'] = trim($this->emergency_contact_name);
        }

        if ($this->emergency_contact_phone) {
            $cleanData['emergency_contact_phone'] = preg_replace('/\D/', '', $this->emergency_contact_phone);
        }

        if ($this->emergency_contact_relation) {
            $cleanData['emergency_contact_relation'] = trim($this->emergency_contact_relation);
        }

        $this->merge($cleanData);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new \Illuminate\Http\Exceptions\HttpResponseException(
                response()->json([
                    'success' => false,
                    'message' => 'Profile update validation failed',
                    'errors' => $validator->errors()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }
}
