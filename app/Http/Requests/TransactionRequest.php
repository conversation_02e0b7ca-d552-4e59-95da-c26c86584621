<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\SavingAccount;
use App\Models\Loan;
use App\Models\Member;
use App\Rules\FinancialAmountRule;

class TransactionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               in_array(auth()->user()->role, ['field_officer', 'manager', 'admin']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $transactionType = $this->input('transaction_type');
        $user = auth()->user();

        return [
            'transaction_type' => [
                'required',
                'string',
                'in:deposit,withdrawal,loan_payment,loan_disbursement,fee_collection,penalty,interest_payment'
            ],
            'amount' => [
                'required',
                new FinancialAmountRule(1, 1000000),
                function ($attribute, $value, $fail) use ($transactionType) {
                    // Minimum amount validation based on transaction type
                    $minimums = [
                        'deposit' => 10,
                        'withdrawal' => 10,
                        'loan_payment' => 1,
                        'loan_disbursement' => 1000,
                        'fee_collection' => 1,
                        'penalty' => 1,
                        'interest_payment' => 1
                    ];

                    if (isset($minimums[$transactionType]) && $value < $minimums[$transactionType]) {
                        $fail("Minimum amount for {$transactionType} is ৳{$minimums[$transactionType]}.");
                    }
                }
            ],
            'member_id' => [
                'required_unless:transaction_type,loan_disbursement',
                'integer',
                'exists:members,id',
                function ($attribute, $value, $fail) use ($user) {
                    if ($value) {
                        $member = Member::find($value);
                        if ($member) {
                            // Field officers can only transact for their own members
                            if ($user->role === 'field_officer' && $member->created_by !== $user->id) {
                                $fail('You can only create transactions for members you registered.');
                            }

                            // Branch access validation for managers
                            if ($user->role === 'manager' && $member->branch_id !== $user->branch_id) {
                                $fail('You can only create transactions for members in your branch.');
                            }
                        }
                    }
                }
            ],
            'saving_account_id' => [
                'required_if:transaction_type,deposit,withdrawal',
                'integer',
                'exists:saving_accounts,id',
                function ($attribute, $value, $fail) {
                    if ($value && $this->member_id) {
                        $savingAccount = SavingAccount::find($value);
                        if ($savingAccount && $savingAccount->member_id != $this->member_id) {
                            $fail('Selected saving account does not belong to the selected member.');
                        }

                        // Check account status
                        if ($savingAccount && $savingAccount->status !== 'active') {
                            $fail('Cannot perform transactions on inactive saving account.');
                        }

                        // Withdrawal validation
                        if ($this->transaction_type === 'withdrawal' && $savingAccount) {
                            $availableBalance = $savingAccount->balance;
                            if ($this->amount > $availableBalance) {
                                $fail("Insufficient balance. Available balance: ৳" . number_format($availableBalance, 2));
                            }

                            // Minimum balance requirement
                            $minimumBalance = 100; // Minimum ৳100 must remain
                            if (($availableBalance - $this->amount) < $minimumBalance) {
                                $fail("Minimum balance of ৳{$minimumBalance} must be maintained.");
                            }
                        }
                    }
                }
            ],
            'loan_id' => [
                'required_if:transaction_type,loan_payment,loan_disbursement',
                'integer',
                'exists:loans,id',
                function ($attribute, $value, $fail) {
                    if ($value) {
                        $loan = Loan::find($value);
                        if ($loan) {
                            // Validate loan belongs to member
                            if ($this->member_id && $loan->member_id != $this->member_id) {
                                $fail('Selected loan does not belong to the selected member.');
                            }

                            // Loan payment validation
                            if ($this->transaction_type === 'loan_payment') {
                                if ($loan->status !== 'active') {
                                    $fail('Cannot make payments to inactive loans.');
                                }

                                $remainingAmount = $loan->remaining_amount;
                                if ($this->amount > $remainingAmount) {
                                    $fail("Payment amount cannot exceed remaining loan amount: ৳" . number_format($remainingAmount, 2));
                                }
                            }

                            // Loan disbursement validation
                            if ($this->transaction_type === 'loan_disbursement') {
                                if ($loan->status !== 'approved') {
                                    $fail('Can only disburse approved loans.');
                                }

                                if ($loan->disbursed_amount > 0) {
                                    $fail('This loan has already been disbursed.');
                                }
                            }
                        }
                    }
                }
            ],
            'transaction_date' => [
                'required',
                'date',
                'before_or_equal:today',
                'after:' . now()->subDays(7)->format('Y-m-d') // Max 7 days back
            ],
            'description' => [
                'nullable',
                'string',
                'max:500'
            ],
            'reference_number' => [
                'nullable',
                'string',
                'max:50',
                'alpha_num'
            ],
            'payment_method' => [
                'required',
                'string',
                'in:cash,bank_transfer,mobile_banking,check'
            ],
            'bank_details' => [
                'required_if:payment_method,bank_transfer,check',
                'nullable',
                'string',
                'max:200'
            ],
            'mobile_banking_details' => [
                'required_if:payment_method,mobile_banking',
                'nullable',
                'string',
                'max:200'
            ],
            'check_number' => [
                'required_if:payment_method,check',
                'nullable',
                'string',
                'max:50'
            ],
            'collected_by' => [
                'nullable',
                'string',
                'max:100'
            ],
            'notes' => [
                'nullable',
                'string',
                'max:1000'
            ]
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'transaction_type.required' => 'Transaction type is required.',
            'transaction_type.in' => 'Please select a valid transaction type.',
            'amount.required' => 'Transaction amount is required.',
            'member_id.required_unless' => 'Member selection is required for this transaction type.',
            'member_id.exists' => 'Selected member does not exist.',
            'saving_account_id.required_if' => 'Saving account is required for deposit/withdrawal transactions.',
            'saving_account_id.exists' => 'Selected saving account does not exist.',
            'loan_id.required_if' => 'Loan selection is required for loan-related transactions.',
            'loan_id.exists' => 'Selected loan does not exist.',
            'transaction_date.required' => 'Transaction date is required.',
            'transaction_date.before_or_equal' => 'Transaction date cannot be in the future.',
            'transaction_date.after' => 'Transaction date cannot be more than 7 days old.',
            'payment_method.required' => 'Payment method is required.',
            'payment_method.in' => 'Please select a valid payment method.',
            'bank_details.required_if' => 'Bank details are required for bank transfer/check payments.',
            'mobile_banking_details.required_if' => 'Mobile banking details are required for mobile banking payments.',
            'check_number.required_if' => 'Check number is required for check payments.',
            'reference_number.alpha_num' => 'Reference number can only contain letters and numbers.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'transaction_type' => 'transaction type',
            'member_id' => 'member',
            'saving_account_id' => 'saving account',
            'loan_id' => 'loan',
            'transaction_date' => 'transaction date',
            'payment_method' => 'payment method',
            'reference_number' => 'reference number',
            'bank_details' => 'bank details',
            'mobile_banking_details' => 'mobile banking details',
            'check_number' => 'check number',
            'collected_by' => 'collected by'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'description' => $this->description ? trim($this->description) : null,
            'reference_number' => $this->reference_number ? strtoupper(trim($this->reference_number)) : null,
            'bank_details' => $this->bank_details ? trim($this->bank_details) : null,
            'mobile_banking_details' => $this->mobile_banking_details ? trim($this->mobile_banking_details) : null,
            'check_number' => $this->check_number ? trim($this->check_number) : null,
            'collected_by' => $this->collected_by ? trim($this->collected_by) : null,
            'notes' => $this->notes ? trim($this->notes) : null,
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Daily transaction limit validation
            $user = auth()->user();
            $transactionDate = $this->input('transaction_date');
            $amount = $this->input('amount');

            // Field officers have daily transaction limits
            if ($user->role === 'field_officer') {
                $dailyLimit = 100000; // ৳1,00,000 daily limit
                
                // Calculate today's transactions by this user
                $todayTransactions = \App\Models\SavingTransaction::where('created_by', $user->id)
                    ->whereDate('transaction_date', $transactionDate)
                    ->sum('amount');

                if (($todayTransactions + $amount) > $dailyLimit) {
                    $remaining = $dailyLimit - $todayTransactions;
                    $validator->errors()->add('amount', "Daily transaction limit exceeded. Remaining limit: ৳" . number_format($remaining, 2));
                }
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new \Illuminate\Http\Exceptions\HttpResponseException(
                response()->json([
                    'success' => false,
                    'message' => 'Transaction validation failed',
                    'errors' => $validator->errors()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }
}
