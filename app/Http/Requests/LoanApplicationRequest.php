<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Models\Member;
use App\Models\Loan;
use App\Rules\FinancialAmountRule;

class LoanApplicationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check() && 
               in_array(auth()->user()->role, ['field_officer', 'manager', 'admin']);
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $user = auth()->user();
        $memberIds = [];

        // Field officers can only apply for their own members
        if ($user->role === 'field_officer') {
            $memberIds = Member::where('created_by', $user->id)->pluck('id')->toArray();
        } else {
            // Managers and admins can apply for any member in their branch/system
            $memberIds = Member::when($user->role === 'manager', function ($query) use ($user) {
                return $query->where('branch_id', $user->branch_id);
            })->pluck('id')->toArray();
        }

        return [
            'member_id' => [
                'required',
                'integer',
                Rule::in($memberIds),
                function ($attribute, $value, $fail) {
                    $member = Member::find($value);
                    if ($member) {
                        // Check if member has any active loans
                        $activeLoan = Loan::where('member_id', $value)
                            ->whereIn('status', ['active', 'approved'])
                            ->exists();
                        
                        if ($activeLoan) {
                            $fail('This member already has an active loan.');
                        }

                        // Check member eligibility based on age
                        $age = now()->diffInYears($member->date_of_birth);
                        if ($age < 18 || $age > 65) {
                            $fail('Member must be between 18 and 65 years old to apply for a loan.');
                        }

                        // Check if member has been with us for at least 3 months
                        if ($member->created_at->diffInMonths(now()) < 3) {
                            $fail('Member must be registered for at least 3 months before applying for a loan.');
                        }
                    }
                }
            ],
            'applied_amount' => [
                'required',
                new FinancialAmountRule(1000, 500000),
                function ($attribute, $value, $fail) {
                    if ($this->member_id) {
                        $member = Member::find($this->member_id);
                        if ($member) {
                            // Maximum loan amount based on monthly income (10x monthly income)
                            $maxLoanAmount = $member->monthly_income * 10;
                            if ($value > $maxLoanAmount) {
                                $fail("Maximum loan amount for this member is ৳" . number_format($maxLoanAmount) . " (10x monthly income).");
                            }

                            // Minimum loan amount validation
                            if ($value < 1000) {
                                $fail('Minimum loan amount is ৳1,000.');
                            }
                        }
                    }
                }
            ],
            'requested_amount' => [
                'nullable',
                new FinancialAmountRule(1000, 500000),
                'lte:applied_amount'
            ],
            'reason' => [
                'required',
                'string',
                'min:10',
                'max:1000'
            ],
            'purpose' => [
                'required',
                'string',
                'in:business,agriculture,education,medical,house_repair,emergency,other'
            ],
            'loan_cycle_number' => [
                'required',
                'integer',
                'min:1',
                'max:10'
            ],
            'recommender' => [
                'nullable',
                'string',
                'max:100'
            ],
            'advance_payment' => [
                'nullable',
                new FinancialAmountRule(0, null),
                function ($attribute, $value, $fail) {
                    if ($value && $this->applied_amount) {
                        $maxAdvance = $this->applied_amount * 0.2; // Max 20% advance
                        if ($value > $maxAdvance) {
                            $fail("Maximum advance payment is 20% of loan amount (৳" . number_format($maxAdvance) . ").");
                        }
                    }
                }
            ],
            'business_plan' => [
                'nullable',
                'string',
                'max:1000'
            ],
            'collateral_description' => [
                'nullable',
                'string',
                'max:500'
            ],
            'guarantor_name' => [
                'nullable',
                'string',
                'max:255',
                'regex:/^[a-zA-Z\s\.\-\']+$/'
            ],
            'guarantor_phone' => [
                'nullable',
                'string',
                'regex:/^(\+88)?01[3-9]\d{8}$/'
            ],
            'guarantor_address' => [
                'nullable',
                'string',
                'max:500'
            ],
            'guarantor_nid' => [
                'nullable',
                'string',
                'regex:/^(\d{10}|\d{13}|\d{17})$/'
            ],
            'loan_duration_months' => [
                'required',
                'integer',
                'min:3',
                'max:60'
            ],
            'repayment_method' => [
                'required',
                'in:weekly,bi_weekly,monthly'
            ],
            'interest_rate' => [
                'nullable',
                'numeric',
                'min:0',
                'max:50'
            ],
            'application_date' => [
                'required',
                'date',
                'before_or_equal:today',
                'after:' . now()->subMonths(1)->format('Y-m-d')
            ]
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'member_id.required' => 'Please select a member.',
            'member_id.in' => 'Selected member is not valid or not accessible.',
            'applied_amount.required' => 'Loan amount is required.',
            'requested_amount.lte' => 'Requested amount cannot be greater than applied amount.',
            'reason.required' => 'Loan reason is required.',
            'reason.min' => 'Loan reason must be at least 10 characters.',
            'purpose.required' => 'Loan purpose is required.',
            'purpose.in' => 'Please select a valid loan purpose.',
            'loan_cycle_number.required' => 'Loan cycle number is required.',
            'loan_cycle_number.min' => 'Loan cycle number must be at least 1.',
            'loan_cycle_number.max' => 'Maximum 10 loan cycles allowed.',
            'guarantor_name.regex' => 'Guarantor name can only contain letters, spaces, dots, hyphens, and apostrophes.',
            'guarantor_phone.regex' => 'Please enter a valid Bangladesh phone number.',
            'guarantor_nid.regex' => 'Please enter a valid National ID number.',
            'loan_duration_months.required' => 'Loan duration is required.',
            'loan_duration_months.min' => 'Minimum loan duration is 3 months.',
            'loan_duration_months.max' => 'Maximum loan duration is 60 months.',
            'repayment_method.required' => 'Repayment method is required.',
            'repayment_method.in' => 'Please select a valid repayment method.',
            'interest_rate.numeric' => 'Interest rate must be a valid number.',
            'interest_rate.min' => 'Interest rate cannot be negative.',
            'interest_rate.max' => 'Interest rate cannot exceed 50%.',
            'application_date.required' => 'Application date is required.',
            'application_date.before_or_equal' => 'Application date cannot be in the future.',
            'application_date.after' => 'Application date cannot be more than 1 month old.',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'member_id' => 'member',
            'applied_amount' => 'loan amount',
            'requested_amount' => 'requested amount',
            'loan_cycle_number' => 'loan cycle number',
            'advance_payment' => 'advance payment',
            'business_plan' => 'business plan',
            'collateral_description' => 'collateral description',
            'guarantor_name' => 'guarantor name',
            'guarantor_phone' => 'guarantor phone',
            'guarantor_address' => 'guarantor address',
            'guarantor_nid' => 'guarantor NID',
            'loan_duration_months' => 'loan duration',
            'repayment_method' => 'repayment method',
            'interest_rate' => 'interest rate',
            'application_date' => 'application date'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'reason' => trim($this->reason),
            'business_plan' => trim($this->business_plan),
            'collateral_description' => trim($this->collateral_description),
            'guarantor_name' => trim($this->guarantor_name),
            'guarantor_address' => trim($this->guarantor_address),
            'guarantor_phone' => preg_replace('/\D/', '', $this->guarantor_phone),
            'guarantor_nid' => preg_replace('/\D/', '', $this->guarantor_nid),
        ]);
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new \Illuminate\Http\Exceptions\HttpResponseException(
                response()->json([
                    'success' => false,
                    'message' => 'Loan application validation failed',
                    'errors' => $validator->errors()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }
}
