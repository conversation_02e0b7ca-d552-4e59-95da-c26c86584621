<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Illuminate\Validation\Rules\Password;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;

class UserCreationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        $user = auth()->user();
        
        // Only admins and managers can create users
        if (!in_array($user->role, ['admin', 'manager'])) {
            return false;
        }

        // Managers can only create field officers in their branch
        if ($user->role === 'manager') {
            $requestedRole = $this->input('role');
            return in_array($requestedRole, ['field_officer']);
        }

        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $user = auth()->user();
        $userId = $this->route('user') ? $this->route('user')->id : null;

        return [
            'name' => [
                'required',
                'string',
                'min:2',
                'max:255',
                'regex:/^[a-zA-Z\s\.\-\']+$/'
            ],
            'email' => [
                'required',
                'string',
                'email:rfc,dns',
                'max:255',
                Rule::unique('users', 'email')->ignore($userId)
            ],
            'password' => [
                $userId ? 'nullable' : 'required',
                'string',
                Password::min(8)
                    ->letters()
                    ->mixedCase()
                    ->numbers()
                    ->symbols()
                    ->uncompromised(),
                'confirmed'
            ],
            'role' => [
                'required',
                'string',
                function ($attribute, $value, $fail) use ($user) {
                    $allowedRoles = [];
                    
                    if ($user->role === 'admin') {
                        $allowedRoles = ['admin', 'manager', 'field_officer', 'member'];
                    } elseif ($user->role === 'manager') {
                        $allowedRoles = ['field_officer'];
                    }

                    if (!in_array($value, $allowedRoles)) {
                        $fail('You are not authorized to create users with this role.');
                    }
                }
            ],
            'branch_id' => [
                function ($attribute, $value, $fail) use ($user) {
                    $role = $this->input('role');
                    
                    // Branch assignment rules
                    if (in_array($role, ['manager', 'field_officer'])) {
                        if (!$value) {
                            $fail('Branch assignment is required for managers and field officers.');
                            return;
                        }

                        $branch = Branch::find($value);
                        if (!$branch) {
                            $fail('Selected branch does not exist.');
                            return;
                        }

                        // Managers can only assign to their own branch
                        if ($user->role === 'manager' && $value != $user->branch_id) {
                            $fail('You can only assign users to your own branch.');
                        }

                        // Check if branch already has a manager (for manager role)
                        if ($role === 'manager') {
                            $existingManager = User::where('role', 'manager')
                                ->where('branch_id', $value)
                                ->where('id', '!=', $this->route('user') ? $this->route('user')->id : null)
                                ->exists();
                            
                            if ($existingManager) {
                                $fail('This branch already has a manager assigned.');
                            }
                        }
                    } elseif ($role === 'admin') {
                        // Admins don't need branch assignment
                        if ($value) {
                            $fail('Admin users should not be assigned to a specific branch.');
                        }
                    }
                }
            ],
            'member_id' => [
                function ($attribute, $value, $fail) {
                    $role = $this->input('role');
                    
                    if ($role === 'member') {
                        if (!$value) {
                            $fail('Member selection is required for member role.');
                            return;
                        }

                        $member = Member::find($value);
                        if (!$member) {
                            $fail('Selected member does not exist.');
                            return;
                        }

                        // Check if member already has a user account
                        $existingUser = User::where('member_id', $value)
                            ->where('id', '!=', $this->route('user') ? $this->route('user')->id : null)
                            ->exists();
                        
                        if ($existingUser) {
                            $fail('This member already has a user account.');
                        }
                    } else {
                        // Non-member roles should not have member_id
                        if ($value) {
                            $fail('Only member role users can be linked to a member.');
                        }
                    }
                }
            ],
            'is_active' => [
                'boolean'
            ],
            'phone_number' => [
                'nullable',
                'string',
                'regex:/^(\+88)?01[3-9]\d{8}$/',
                Rule::unique('users', 'phone_number')->ignore($userId)
            ],
            'employee_id' => [
                'nullable',
                'string',
                'max:20',
                'alpha_num',
                Rule::unique('users', 'employee_id')->ignore($userId)
            ],
            'joining_date' => [
                'nullable',
                'date',
                'before_or_equal:today',
                'after:2020-01-01'
            ],
            'salary' => [
                'nullable',
                'numeric',
                'min:0',
                'max:999999.99'
            ],
            'designation' => [
                'nullable',
                'string',
                'max:100'
            ]
        ];
    }

    /**
     * Get custom error messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name.required' => 'Name is required.',
            'name.regex' => 'Name can only contain letters, spaces, dots, hyphens, and apostrophes.',
            'email.required' => 'Email address is required.',
            'email.email' => 'Please enter a valid email address.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required.',
            'password.confirmed' => 'Password confirmation does not match.',
            'role.required' => 'User role is required.',
            'phone_number.regex' => 'Please enter a valid Bangladesh phone number.',
            'phone_number.unique' => 'This phone number is already registered.',
            'employee_id.unique' => 'This employee ID is already in use.',
            'employee_id.alpha_num' => 'Employee ID can only contain letters and numbers.',
            'joining_date.before_or_equal' => 'Joining date cannot be in the future.',
            'joining_date.after' => 'Joining date must be after January 1, 2020.',
            'salary.numeric' => 'Salary must be a valid number.',
            'salary.min' => 'Salary cannot be negative.',
            'designation.max' => 'Designation cannot exceed 100 characters.'
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'member_id' => 'member',
            'branch_id' => 'branch',
            'is_active' => 'active status',
            'phone_number' => 'phone number',
            'employee_id' => 'employee ID',
            'joining_date' => 'joining date'
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation(): void
    {
        $this->merge([
            'name' => trim($this->name),
            'email' => strtolower(trim($this->email)),
            'phone_number' => $this->phone_number ? preg_replace('/\D/', '', $this->phone_number) : null,
            'employee_id' => $this->employee_id ? strtoupper(trim($this->employee_id)) : null,
            'designation' => $this->designation ? trim($this->designation) : null,
            'is_active' => $this->has('is_active') ? (bool) $this->is_active : true,
        ]);
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            // Additional business logic validation
            $role = $this->input('role');
            $branchId = $this->input('branch_id');
            
            // Validate branch capacity for field officers
            if ($role === 'field_officer' && $branchId) {
                $fieldOfficerCount = User::where('role', 'field_officer')
                    ->where('branch_id', $branchId)
                    ->where('is_active', true)
                    ->where('id', '!=', $this->route('user') ? $this->route('user')->id : null)
                    ->count();
                
                if ($fieldOfficerCount >= 10) { // Max 10 field officers per branch
                    $validator->errors()->add('branch_id', 'This branch already has the maximum number of field officers (10).');
                }
            }
        });
    }

    /**
     * Handle a failed validation attempt.
     */
    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        if ($this->expectsJson()) {
            throw new \Illuminate\Http\Exceptions\HttpResponseException(
                response()->json([
                    'success' => false,
                    'message' => 'User creation validation failed',
                    'errors' => $validator->errors()
                ], 422)
            );
        }

        parent::failedValidation($validator);
    }
}
