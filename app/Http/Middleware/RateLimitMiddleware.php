<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\RateLimiter;
use Symfony\Component\HttpFoundation\Response;

class RateLimitMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next, string $type = 'general'): Response
    {
        $key = $this->resolveRequestSignature($request, $type);
        $maxAttempts = $this->getMaxAttempts($type);
        $decayMinutes = $this->getDecayMinutes($type);

        if (RateLimiter::tooManyAttempts($key, $maxAttempts)) {
            $seconds = RateLimiter::availableIn($key);
            
            // Log suspicious activity
            $this->logSuspiciousActivity($request, $type);
            
            return $this->buildResponse($request, $seconds, $type);
        }

        RateLimiter::hit($key, $decayMinutes * 60);

        $response = $next($request);

        return $this->addHeaders(
            $response,
            $maxAttempts,
            RateLimiter::attempts($key),
            RateLimiter::availableIn($key)
        );
    }

    /**
     * Resolve request signature for rate limiting
     */
    protected function resolveRequestSignature(Request $request, string $type): string
    {
        $ip = $request->ip();
        $userAgent = $request->userAgent();
        
        switch ($type) {
            case 'login':
                return 'login_attempts:' . $ip . ':' . ($request->input('email') ?? 'unknown');
            
            case 'api':
                $userId = auth()->id() ?? 'guest';
                return 'api_requests:' . $ip . ':' . $userId;
            
            case 'password_reset':
                return 'password_reset:' . $ip . ':' . ($request->input('email') ?? 'unknown');
            
            case 'registration':
                return 'registration:' . $ip;
            
            case 'file_upload':
                $userId = auth()->id() ?? 'guest';
                return 'file_upload:' . $ip . ':' . $userId;
            
            case 'transaction':
                $userId = auth()->id() ?? 'guest';
                return 'transaction:' . $ip . ':' . $userId;
            
            default:
                return 'general:' . $ip . ':' . md5($userAgent);
        }
    }

    /**
     * Get maximum attempts for rate limit type
     */
    protected function getMaxAttempts(string $type): int
    {
        return match ($type) {
            'login' => 5,           // 5 login attempts
            'api' => 100,           // 100 API requests
            'password_reset' => 3,   // 3 password reset attempts
            'registration' => 3,     // 3 registration attempts
            'file_upload' => 10,     // 10 file uploads
            'transaction' => 50,     // 50 transactions
            default => 60,          // 60 general requests
        };
    }

    /**
     * Get decay minutes for rate limit type
     */
    protected function getDecayMinutes(string $type): int
    {
        return match ($type) {
            'login' => 15,          // 15 minutes lockout
            'api' => 1,             // 1 minute window
            'password_reset' => 60,  // 1 hour lockout
            'registration' => 60,    // 1 hour lockout
            'file_upload' => 10,     // 10 minutes window
            'transaction' => 5,      // 5 minutes window
            default => 1,           // 1 minute window
        };
    }

    /**
     * Build rate limit exceeded response
     */
    protected function buildResponse(Request $request, int $retryAfter, string $type): Response
    {
        $message = $this->getErrorMessage($type);
        
        if ($request->expectsJson()) {
            return response()->json([
                'success' => false,
                'message' => $message,
                'retry_after' => $retryAfter,
                'type' => $type
            ], 429);
        }

        return response()->view('errors.429', [
            'message' => $message,
            'retryAfter' => $retryAfter,
            'type' => $type
        ], 429);
    }

    /**
     * Get error message for rate limit type
     */
    protected function getErrorMessage(string $type): string
    {
        return match ($type) {
            'login' => 'Too many login attempts. Please try again later.',
            'api' => 'Too many API requests. Please slow down.',
            'password_reset' => 'Too many password reset attempts. Please try again later.',
            'registration' => 'Too many registration attempts. Please try again later.',
            'file_upload' => 'Too many file upload attempts. Please try again later.',
            'transaction' => 'Too many transaction attempts. Please try again later.',
            default => 'Too many requests. Please try again later.',
        };
    }

    /**
     * Add rate limit headers to response
     */
    protected function addHeaders(Response $response, int $maxAttempts, int $attempts, int $retryAfter): Response
    {
        $response->headers->add([
            'X-RateLimit-Limit' => $maxAttempts,
            'X-RateLimit-Remaining' => max(0, $maxAttempts - $attempts),
            'X-RateLimit-Reset' => now()->addSeconds($retryAfter)->timestamp,
        ]);

        return $response;
    }

    /**
     * Log suspicious activity
     */
    protected function logSuspiciousActivity(Request $request, string $type): void
    {
        $data = [
            'ip' => $request->ip(),
            'user_agent' => $request->userAgent(),
            'url' => $request->fullUrl(),
            'method' => $request->method(),
            'type' => $type,
            'timestamp' => now(),
            'user_id' => auth()->id(),
        ];

        // Log to file
        \Log::warning('Rate limit exceeded', $data);

        // Store in cache for monitoring
        $cacheKey = 'suspicious_activity:' . $request->ip();
        $activities = Cache::get($cacheKey, []);
        $activities[] = $data;
        
        // Keep only last 10 activities
        $activities = array_slice($activities, -10);
        
        Cache::put($cacheKey, $activities, now()->addHours(24));

        // Check for potential attacks
        $this->checkForAttacks($request, $activities);
    }

    /**
     * Check for potential attacks and take action
     */
    protected function checkForAttacks(Request $request, array $activities): void
    {
        $ip = $request->ip();
        $recentActivities = collect($activities)
            ->where('timestamp', '>', now()->subMinutes(30))
            ->count();

        // If more than 20 rate limit violations in 30 minutes, temporarily block IP
        if ($recentActivities > 20) {
            $blockKey = 'blocked_ip:' . $ip;
            Cache::put($blockKey, true, now()->addHours(1));
            
            \Log::critical('IP temporarily blocked due to excessive rate limit violations', [
                'ip' => $ip,
                'violations' => $recentActivities,
                'blocked_until' => now()->addHours(1)
            ]);
        }
    }
}
