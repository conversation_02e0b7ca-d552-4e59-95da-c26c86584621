<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Symfony\Component\HttpFoundation\Response;

class FileUploadSecurityMiddleware
{
    /**
     * Handle an incoming request.
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Check if request has files
        if ($request->hasFile('photo') || $request->hasFile('avatar') || $this->hasAnyFile($request)) {
            $this->validateFileUploads($request);
        }

        return $next($request);
    }

    /**
     * Check if request has any file uploads
     */
    private function hasAnyFile(Request $request): bool
    {
        foreach ($request->allFiles() as $file) {
            if ($file instanceof UploadedFile) {
                return true;
            }
        }
        return false;
    }

    /**
     * Validate all file uploads in the request
     */
    private function validateFileUploads(Request $request): void
    {
        foreach ($request->allFiles() as $key => $file) {
            if ($file instanceof UploadedFile) {
                $this->validateSingleFile($file, $key);
            } elseif (is_array($file)) {
                foreach ($file as $index => $singleFile) {
                    if ($singleFile instanceof UploadedFile) {
                        $this->validateSingleFile($singleFile, "{$key}.{$index}");
                    }
                }
            }
        }
    }

    /**
     * Validate a single uploaded file
     */
    private function validateSingleFile(UploadedFile $file, string $fieldName): void
    {
        // Check if file was uploaded successfully
        if (!$file->isValid()) {
            abort(422, "File upload failed for {$fieldName}.");
        }

        // Check file size (max 5MB for any file)
        $maxSize = 5 * 1024 * 1024; // 5MB in bytes
        if ($file->getSize() > $maxSize) {
            abort(422, "File size exceeds maximum allowed size of 5MB for {$fieldName}.");
        }

        // Check MIME type
        $this->validateMimeType($file, $fieldName);

        // Check file extension
        $this->validateFileExtension($file, $fieldName);

        // Check for malicious content
        $this->scanForMaliciousContent($file, $fieldName);

        // Validate image files specifically
        if ($this->isImageFile($file)) {
            $this->validateImageFile($file, $fieldName);
        }
    }

    /**
     * Validate MIME type
     */
    private function validateMimeType(UploadedFile $file, string $fieldName): void
    {
        $allowedMimeTypes = [
            // Images
            'image/jpeg',
            'image/png',
            'image/jpg',
            'image/gif',
            'image/webp',
            // Documents
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            // Text files
            'text/plain',
            'text/csv',
        ];

        $mimeType = $file->getMimeType();
        
        if (!in_array($mimeType, $allowedMimeTypes)) {
            abort(422, "File type not allowed for {$fieldName}. Detected MIME type: {$mimeType}");
        }
    }

    /**
     * Validate file extension
     */
    private function validateFileExtension(UploadedFile $file, string $fieldName): void
    {
        $allowedExtensions = [
            'jpg', 'jpeg', 'png', 'gif', 'webp',
            'pdf', 'doc', 'docx', 'xls', 'xlsx',
            'txt', 'csv'
        ];

        $extension = strtolower($file->getClientOriginalExtension());
        
        if (!in_array($extension, $allowedExtensions)) {
            abort(422, "File extension not allowed for {$fieldName}: .{$extension}");
        }

        // Double extension check
        $filename = $file->getClientOriginalName();
        if (substr_count($filename, '.') > 1) {
            abort(422, "Multiple file extensions not allowed for {$fieldName}.");
        }
    }

    /**
     * Scan for malicious content
     */
    private function scanForMaliciousContent(UploadedFile $file, string $fieldName): void
    {
        $content = file_get_contents($file->getPathname());
        
        // Check for common malicious patterns
        $maliciousPatterns = [
            '/<\?php/i',
            '/<script/i',
            '/javascript:/i',
            '/vbscript:/i',
            '/onload=/i',
            '/onerror=/i',
            '/eval\(/i',
            '/base64_decode/i',
            '/shell_exec/i',
            '/system\(/i',
            '/exec\(/i',
            '/passthru/i',
            '/file_get_contents/i',
            '/file_put_contents/i',
            '/fopen/i',
            '/fwrite/i',
            '/curl_exec/i',
        ];

        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                abort(422, "Potentially malicious content detected in {$fieldName}.");
            }
        }

        // Check for null bytes
        if (strpos($content, "\0") !== false) {
            abort(422, "Invalid file content detected in {$fieldName}.");
        }
    }

    /**
     * Check if file is an image
     */
    private function isImageFile(UploadedFile $file): bool
    {
        $imageMimeTypes = [
            'image/jpeg',
            'image/png',
            'image/jpg',
            'image/gif',
            'image/webp'
        ];

        return in_array($file->getMimeType(), $imageMimeTypes);
    }

    /**
     * Validate image file specifically
     */
    private function validateImageFile(UploadedFile $file, string $fieldName): void
    {
        // Get image info
        $imageInfo = getimagesize($file->getPathname());
        
        if ($imageInfo === false) {
            abort(422, "Invalid image file for {$fieldName}.");
        }

        [$width, $height, $type] = $imageInfo;

        // Check image dimensions
        $maxWidth = 4000;
        $maxHeight = 4000;
        $minWidth = 50;
        $minHeight = 50;

        if ($width > $maxWidth || $height > $maxHeight) {
            abort(422, "Image dimensions too large for {$fieldName}. Maximum: {$maxWidth}x{$maxHeight}px");
        }

        if ($width < $minWidth || $height < $minHeight) {
            abort(422, "Image dimensions too small for {$fieldName}. Minimum: {$minWidth}x{$minHeight}px");
        }

        // Check image type
        $allowedImageTypes = [
            IMAGETYPE_JPEG,
            IMAGETYPE_PNG,
            IMAGETYPE_GIF,
            IMAGETYPE_WEBP
        ];

        if (!in_array($type, $allowedImageTypes)) {
            abort(422, "Image type not supported for {$fieldName}.");
        }

        // Check for EXIF data that might contain malicious code
        if (function_exists('exif_read_data') && in_array($type, [IMAGETYPE_JPEG, IMAGETYPE_TIFF_II, IMAGETYPE_TIFF_MM])) {
            $exifData = @exif_read_data($file->getPathname());
            if ($exifData && isset($exifData['UserComment'])) {
                // Check for suspicious content in EXIF data
                $suspiciousPatterns = ['<script', 'javascript:', 'php', 'eval('];
                foreach ($suspiciousPatterns as $pattern) {
                    if (stripos($exifData['UserComment'], $pattern) !== false) {
                        abort(422, "Suspicious EXIF data detected in {$fieldName}.");
                    }
                }
            }
        }
    }
}
