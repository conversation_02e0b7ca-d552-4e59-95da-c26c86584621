<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Branch;

class BranchAccessMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login');
        }

        $user = auth()->user();

        // Admins can access all branches
        if ($user->isAdmin()) {
            return $next($request);
        }

        // Get branch ID from route parameter
        $branchId = $request->route('branch') ?? $request->route('branch_id') ?? $request->input('branch_id');

        if ($branchId) {
            $branch = Branch::find($branchId);
            
            if (!$branch) {
                abort(404, 'Branch not found.');
            }

            // Check if user can access this branch
            if (!$user->canAccessBranch($branch)) {
                abort(403, 'You do not have permission to access this branch data.');
            }
        } else {
            // If no specific branch is requested, ensure user has a branch assigned
            if (!$user->branch_id && !$user->isAdmin()) {
                abort(403, 'You are not assigned to any branch. Please contact administrator.');
            }
        }

        return $next($request);
    }
}
