<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class RoleMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next, ...$roles): Response
    {
        if (!auth()->check()) {
            return redirect()->route('login')->with('error', 'Please login to access this page.');
        }

        $user = auth()->user();

        // Check if user is active
        if (!$user->is_active) {
            auth()->logout();
            return redirect()->route('login')->with('error', 'Your account has been deactivated. Please contact administrator.');
        }

        // Check role-based access
        foreach ($roles as $role) {
            // Handle both direct role property and Spatie permission roles
            if ($user->role === $role || ($user->hasRole && $user->hasRole($role))) {
                return $next($request);
            }
        }

        // Redirect to appropriate dashboard if user has different role
        return $this->redirectToRoleDashboard($user);
    }

    /**
     * Redirect user to their appropriate dashboard
     */
    private function redirectToRoleDashboard($user): Response
    {
        $dashboardRoute = match($user->role) {
            'admin' => 'admin.dashboard',
            'manager' => 'manager.dashboard',
            'field_officer' => 'field-officer.dashboard',
            'member' => 'member.dashboard',
            default => 'dashboard'
        };

        return redirect()->route($dashboardRoute)->with('warning', 'You do not have permission to access the requested page.');
    }
}
