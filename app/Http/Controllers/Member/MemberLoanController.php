<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\LoanApplication;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class MemberLoanController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // Get all loans with their applications
        $loans = $member->loans()
            ->with(['loanApplication', 'installments'])
            ->latest('loan_date')
            ->paginate(10);

        // Get loan applications
        $applications = $member->loanApplications()
            ->latest('application_date')
            ->paginate(5);

        $stats = [
            'total_loans' => $member->loans()->count(),
            'active_loans' => $member->loans()->whereHas('installments', function($q) {
                $q->where('status', 'pending');
            })->count(),
            'completed_loans' => $member->loans()->whereDoesntHave('installments', function($q) {
                $q->where('status', 'pending');
            })->count(),
            'total_borrowed' => $member->loans()->sum('loan_amount'),
            'total_paid' => $member->getTotalPaidAmount(),
            'outstanding_amount' => $member->getTotalOutstandingAmount(),
        ];

        return view('member.loans.index', compact('loans', 'applications', 'stats', 'member'));
    }

    public function show(Loan $loan)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $loan->loanApplication->member_id !== $member->id) {
            abort(403, 'Unauthorized access to loan details.');
        }

        // Load loan with relationships
        $loan->load(['loanApplication.member', 'installments.collectedBy']);

        // Calculate loan statistics
        $totalInstallments = $loan->installments()->count();
        $paidInstallments = $loan->installments()->where('status', 'paid')->count();
        $pendingInstallments = $loan->installments()->where('status', 'pending')->count();
        $overdueInstallments = $loan->installments()
            ->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now())
            ->count();

        $totalAmount = $loan->installments()->sum('amount');
        $paidAmount = $loan->installments()->where('status', 'paid')->sum('amount');
        $remainingAmount = $totalAmount - $paidAmount;

        // Get next installment
        $nextInstallment = $loan->installments()
            ->where('status', 'pending')
            ->orderBy('installment_date')
            ->first();

        // Get recent payments
        $recentPayments = $loan->installments()
            ->where('status', 'paid')
            ->with('collectedBy')
            ->latest('collection_date')
            ->take(5)
            ->get();

        $loanStats = [
            'total_installments' => $totalInstallments,
            'paid_installments' => $paidInstallments,
            'pending_installments' => $pendingInstallments,
            'overdue_installments' => $overdueInstallments,
            'total_amount' => $totalAmount,
            'paid_amount' => $paidAmount,
            'remaining_amount' => $remainingAmount,
            'completion_percentage' => $totalInstallments > 0 ? round(($paidInstallments / $totalInstallments) * 100, 2) : 0,
            'next_installment' => $nextInstallment,
            'recent_payments' => $recentPayments,
        ];

        return view('member.loans.show', compact('loan', 'loanStats', 'member'));
    }

    public function schedule(Loan $loan)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $loan->loanApplication->member_id !== $member->id) {
            abort(403, 'Unauthorized access to loan schedule.');
        }

        $installments = $loan->installments()
            ->with('collectedBy')
            ->orderBy('installment_date')
            ->paginate(20);

        return view('member.loans.schedule', compact('loan', 'installments', 'member'));
    }

    public function downloadStatement(Loan $loan)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $loan->loanApplication->member_id !== $member->id) {
            abort(403, 'Unauthorized access to loan statement.');
        }

        $loan->load(['loanApplication.member.branch', 'installments.collectedBy']);

        $data = [
            'loan' => $loan,
            'member' => $member,
            'installments' => $loan->installments()->orderBy('installment_date')->get(),
            'generated_at' => Carbon::now(),
        ];

        $pdf = Pdf::loadView('member.loans.statement-pdf', $data);
        
        $filename = "loan-statement-{$loan->id}-" . Carbon::now()->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    public function applications()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        $applications = $member->loanApplications()
            ->with(['reviewedBy', 'loan'])
            ->latest('application_date')
            ->paginate(10);

        $stats = [
            'total_applications' => $member->loanApplications()->count(),
            'pending_applications' => $member->loanApplications()->where('status', 'pending')->count(),
            'approved_applications' => $member->loanApplications()->where('status', 'approved')->count(),
            'rejected_applications' => $member->loanApplications()->where('status', 'rejected')->count(),
        ];

        return view('member.loans.applications', compact('applications', 'stats', 'member'));
    }

    public function applicationShow(LoanApplication $application)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $application->member_id !== $member->id) {
            abort(403, 'Unauthorized access to loan application.');
        }

        $application->load(['member.branch', 'reviewedBy', 'loan.installments']);

        return view('member.loans.application-show', compact('application', 'member'));
    }
}
