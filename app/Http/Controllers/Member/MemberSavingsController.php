<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class MemberSavingsController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // Get all savings accounts
        $savingAccounts = $member->savingAccounts()
            ->with(['transactions' => function($q) {
                $q->latest('transaction_date')->take(5);
            }])
            ->get();

        // Calculate statistics
        $stats = [
            'total_accounts' => $savingAccounts->count(),
            'active_accounts' => $savingAccounts->where('status', 'active')->count(),
            'total_balance' => $savingAccounts->where('status', 'active')->sum('current_balance'),
            'total_deposits' => SavingTransaction::whereHas('savingAccount', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('transaction_type', 'deposit')->sum('amount'),
            'total_withdrawals' => SavingTransaction::whereHas('savingAccount', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('transaction_type', 'withdrawal')->sum('amount'),
            'monthly_deposits' => SavingTransaction::whereHas('savingAccount', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })
            ->where('transaction_type', 'deposit')
            ->whereMonth('transaction_date', Carbon::now()->month)
            ->whereYear('transaction_date', Carbon::now()->year)
            ->sum('amount'),
        ];

        // Group accounts by type
        $accountsByType = $savingAccounts->groupBy('account_type');

        return view('member.savings.index', compact('savingAccounts', 'stats', 'accountsByType', 'member'));
    }

    public function show(SavingAccount $savingAccount)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $savingAccount->member_id !== $member->id) {
            abort(403, 'Unauthorized access to savings account.');
        }

        // Load account with recent transactions
        $savingAccount->load(['member.branch', 'createdBy']);

        // Get transactions with pagination
        $transactions = $savingAccount->transactions()
            ->with('enteredBy')
            ->latest('transaction_date')
            ->paginate(20);

        // Calculate account statistics
        $accountStats = [
            'total_deposits' => $savingAccount->transactions()->deposits()->sum('amount'),
            'total_withdrawals' => $savingAccount->transactions()->withdrawals()->sum('amount'),
            'transaction_count' => $savingAccount->transactions()->count(),
            'average_deposit' => $savingAccount->transactions()->deposits()->avg('amount') ?? 0,
            'last_transaction_date' => $savingAccount->transactions()->latest('transaction_date')->first()?->transaction_date,
            'monthly_deposits' => $savingAccount->transactions()
                ->deposits()
                ->whereMonth('transaction_date', Carbon::now()->month)
                ->whereYear('transaction_date', Carbon::now()->year)
                ->sum('amount'),
            'yearly_deposits' => $savingAccount->transactions()
                ->deposits()
                ->whereYear('transaction_date', Carbon::now()->year)
                ->sum('amount'),
        ];

        // Calculate interest earned (if applicable)
        if ($savingAccount->interest_rate > 0) {
            $accountStats['estimated_yearly_interest'] = ($savingAccount->current_balance * $savingAccount->interest_rate) / 100;
            $accountStats['estimated_monthly_interest'] = $accountStats['estimated_yearly_interest'] / 12;
        }

        return view('member.savings.show', compact('savingAccount', 'transactions', 'accountStats', 'member'));
    }

    public function transactions(SavingAccount $savingAccount, Request $request)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $savingAccount->member_id !== $member->id) {
            abort(403, 'Unauthorized access to savings account.');
        }

        // Build query with filters
        $query = $savingAccount->transactions()->with('enteredBy');

        // Date range filter
        if ($request->filled('from_date')) {
            $query->where('transaction_date', '>=', Carbon::parse($request->from_date));
        }

        if ($request->filled('to_date')) {
            $query->where('transaction_date', '<=', Carbon::parse($request->to_date));
        }

        // Transaction type filter
        if ($request->filled('type') && in_array($request->type, ['deposit', 'withdrawal'])) {
            $query->where('transaction_type', $request->type);
        }

        // Amount range filter
        if ($request->filled('min_amount')) {
            $query->where('amount', '>=', $request->min_amount);
        }

        if ($request->filled('max_amount')) {
            $query->where('amount', '<=', $request->max_amount);
        }

        $transactions = $query->latest('transaction_date')->paginate(25);

        // Calculate filtered statistics
        $filteredStats = [
            'total_transactions' => $query->count(),
            'total_deposits' => $query->clone()->deposits()->sum('amount'),
            'total_withdrawals' => $query->clone()->withdrawals()->sum('amount'),
            'net_amount' => $query->clone()->deposits()->sum('amount') - $query->clone()->withdrawals()->sum('amount'),
        ];

        return view('member.savings.transactions', compact(
            'savingAccount', 
            'transactions', 
            'filteredStats', 
            'member'
        ));
    }

    public function downloadStatement(SavingAccount $savingAccount, Request $request)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $savingAccount->member_id !== $member->id) {
            abort(403, 'Unauthorized access to savings statement.');
        }

        // Get date range (default to last 6 months)
        $fromDate = $request->filled('from_date') 
            ? Carbon::parse($request->from_date) 
            : Carbon::now()->subMonths(6);
        
        $toDate = $request->filled('to_date') 
            ? Carbon::parse($request->to_date) 
            : Carbon::now();

        // Get transactions for the period
        $transactions = $savingAccount->transactions()
            ->with('enteredBy')
            ->where('transaction_date', '>=', $fromDate)
            ->where('transaction_date', '<=', $toDate)
            ->orderBy('transaction_date')
            ->get();

        $data = [
            'savingAccount' => $savingAccount,
            'member' => $member,
            'transactions' => $transactions,
            'from_date' => $fromDate,
            'to_date' => $toDate,
            'generated_at' => Carbon::now(),
            'summary' => [
                'opening_balance' => $savingAccount->getBalanceAsOf($fromDate),
                'closing_balance' => $savingAccount->current_balance,
                'total_deposits' => $transactions->where('transaction_type', 'deposit')->sum('amount'),
                'total_withdrawals' => $transactions->where('transaction_type', 'withdrawal')->sum('amount'),
                'transaction_count' => $transactions->count(),
            ]
        ];

        $pdf = Pdf::loadView('member.savings.statement-pdf', $data);
        
        $filename = "savings-statement-{$savingAccount->account_number}-" . 
                   $fromDate->format('Y-m-d') . "-to-" . 
                   $toDate->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    public function goals()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // This would be expanded with a savings goals feature
        // For now, show basic savings progress
        $savingAccounts = $member->savingAccounts()->where('status', 'active')->get();
        
        $goals = [
            'emergency_fund' => [
                'target' => 50000,
                'current' => $savingAccounts->where('account_type', 'general')->sum('current_balance'),
                'description' => 'Emergency Fund (6 months expenses)'
            ],
            'investment_fund' => [
                'target' => 100000,
                'current' => $savingAccounts->where('account_type', 'fixed')->sum('current_balance'),
                'description' => 'Investment Fund'
            ]
        ];

        return view('member.savings.goals', compact('goals', 'savingAccounts', 'member'));
    }
}
