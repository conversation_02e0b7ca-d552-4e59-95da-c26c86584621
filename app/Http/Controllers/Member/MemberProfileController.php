<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rules\Password;
use App\Models\Member;
use App\Models\User;

class MemberProfileController extends Controller
{
    public function index()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        $member->load(['branch', 'createdBy', 'reference']);

        // Get membership statistics
        $membershipStats = [
            'member_since' => $member->created_at,
            'total_loans' => $member->loans()->count(),
            'active_loans' => $member->loans()->whereHas('installments', function($q) {
                $q->where('status', 'pending');
            })->count(),
            'total_savings_accounts' => $member->savingAccounts()->count(),
            'current_loan_status' => $member->getCurrentLoanStatus(),
            'can_apply_for_loan' => $member->canApplyForLoan(),
            'next_loan_cycle' => $member->getNextLoanCycleNumber(),
        ];

        return view('member.profile.index', compact('member', 'membershipStats', 'user'));
    }

    public function edit()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        $member->load(['branch', 'reference']);

        return view('member.profile.edit', compact('member', 'user'));
    }

    public function update(Request $request)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // Validate the request
        $validated = $request->validate([
            'phone_number' => 'required|string|max:15',
            'present_address' => 'required|string|max:500',
            'permanent_address' => 'required|string|max:500',
            'occupation' => 'required|string|max:100',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        // Handle photo upload
        if ($request->hasFile('photo')) {
            // Delete old photo if exists
            if ($member->photo && Storage::disk('public')->exists($member->photo)) {
                Storage::disk('public')->delete($member->photo);
            }

            // Store new photo
            $photoPath = $request->file('photo')->store('member-photos', 'public');
            $validated['photo'] = $photoPath;
        }

        // Update member profile
        $member->update($validated);

        return redirect()->route('member.profile.index')
            ->with('success', 'Profile updated successfully.');
    }

    public function changePassword()
    {
        return view('member.profile.change-password');
    }

    public function updatePassword(Request $request)
    {
        $user = auth()->user();

        $validated = $request->validate([
            'current_password' => 'required',
            'password' => ['required', 'confirmed', Password::defaults()],
        ]);

        // Check if current password is correct
        if (!Hash::check($validated['current_password'], $user->password)) {
            return back()->withErrors([
                'current_password' => 'The current password is incorrect.'
            ]);
        }

        // Update password
        $user->update([
            'password' => Hash::make($validated['password'])
        ]);

        return redirect()->route('member.profile.index')
            ->with('success', 'Password changed successfully.');
    }

    public function emergencyContacts()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // For now, we'll show reference member as emergency contact
        // In a full implementation, you might have a separate emergency_contacts table
        $emergencyContacts = [];
        
        if ($member->reference) {
            $emergencyContacts[] = [
                'name' => $member->reference->name,
                'relationship' => 'Reference',
                'phone' => $member->reference->phone_number,
                'address' => $member->reference->present_address,
            ];
        }

        // Add field officer as emergency contact
        if ($member->createdBy) {
            $emergencyContacts[] = [
                'name' => $member->createdBy->name,
                'relationship' => 'Field Officer',
                'phone' => $member->createdBy->phone ?? 'N/A',
                'address' => $member->branch->address ?? 'N/A',
            ];
        }

        return view('member.profile.emergency-contacts', compact('member', 'emergencyContacts'));
    }

    public function membershipDetails()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        $member->load(['branch.manager', 'createdBy', 'reference']);

        // Get detailed membership information
        $membershipDetails = [
            'member_id' => $member->member_id,
            'joining_date' => $member->created_at,
            'branch_info' => [
                'name' => $member->branch->name,
                'address' => $member->branch->address,
                'manager' => $member->branch->manager->name ?? 'N/A',
            ],
            'field_officer' => [
                'name' => $member->createdBy->name ?? 'N/A',
                'email' => $member->createdBy->email ?? 'N/A',
            ],
            'reference_member' => $member->reference ? [
                'name' => $member->reference->name,
                'member_id' => $member->reference->member_id,
                'phone' => $member->reference->phone_number,
            ] : null,
            'loan_eligibility' => [
                'can_apply' => $member->canApplyForLoan(),
                'current_status' => $member->getCurrentLoanStatus(),
                'next_cycle' => $member->getNextLoanCycleNumber(),
                'total_loans_taken' => $member->getTotalLoans(),
            ],
            'financial_summary' => [
                'total_savings' => $member->getSavingsBalance(),
                'total_loan_amount' => $member->getTotalLoanAmount(),
                'total_paid' => $member->getTotalPaidAmount(),
                'outstanding_amount' => $member->getTotalOutstandingAmount(),
            ],
        ];

        return view('member.profile.membership-details', compact('member', 'membershipDetails'));
    }

    public function documents()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // Get member documents (this would be expanded with a documents table)
        $documents = [
            [
                'type' => 'Profile Photo',
                'status' => $member->photo ? 'Available' : 'Not Available',
                'file_path' => $member->photo,
                'uploaded_date' => $member->updated_at,
            ],
            [
                'type' => 'NID Copy',
                'status' => $member->nid_number ? 'Verified' : 'Not Provided',
                'file_path' => null,
                'uploaded_date' => $member->created_at,
            ],
        ];

        return view('member.profile.documents', compact('member', 'documents'));
    }

    public function downloadPhoto()
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || !$member->photo) {
            return redirect()->back()->with('error', 'Photo not found.');
        }

        if (!Storage::disk('public')->exists($member->photo)) {
            return redirect()->back()->with('error', 'Photo file not found.');
        }

        return Storage::disk('public')->download($member->photo, 'profile-photo.jpg');
    }
}
