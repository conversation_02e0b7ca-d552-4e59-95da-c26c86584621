<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Installment;
use App\Models\Loan;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class MemberInstallmentController extends Controller
{
    public function index(Request $request)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // Build query for installments
        $query = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })->with(['loan.loanApplication', 'collectedBy']);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('loan_id')) {
            $query->where('loan_id', $request->loan_id);
        }

        if ($request->filled('from_date')) {
            $query->where('installment_date', '>=', Carbon::parse($request->from_date));
        }

        if ($request->filled('to_date')) {
            $query->where('installment_date', '<=', Carbon::parse($request->to_date));
        }

        $installments = $query->latest('installment_date')->paginate(20);

        // Get member's loans for filter dropdown
        $memberLoans = $member->loans()->with('loanApplication')->get();

        // Calculate statistics
        $stats = [
            'total_installments' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->count(),
            'paid_installments' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'paid')->count(),
            'pending_installments' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'pending')->count(),
            'overdue_installments' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'pending')->where('installment_date', '<', Carbon::now())->count(),
            'total_paid_amount' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'paid')->sum('amount'),
            'total_pending_amount' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'pending')->sum('amount'),
            'total_late_fees' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'paid')->sum('late_fee'),
        ];

        return view('member.installments.index', compact(
            'installments', 
            'stats', 
            'memberLoans', 
            'member'
        ));
    }

    public function show(Installment $installment)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $installment->loan->loanApplication->member_id !== $member->id) {
            abort(403, 'Unauthorized access to installment details.');
        }

        $installment->load(['loan.loanApplication.member', 'collectedBy']);

        // Calculate late fee if applicable
        $lateFee = $installment->status === 'pending' ? $installment->calculateLateFee() : $installment->late_fee;
        $totalDue = $installment->amount + $lateFee;

        $installmentDetails = [
            'installment' => $installment,
            'late_fee' => $lateFee,
            'total_due' => $totalDue,
            'days_overdue' => $installment->status === 'pending' ? $installment->getDaysOverdue() : 0,
            'is_overdue' => $installment->status === 'pending' && $installment->isOverdue(),
        ];

        return view('member.installments.show', compact('installmentDetails', 'member'));
    }

    public function schedule(Request $request)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // Get active loans
        $activeLoans = $member->loans()->whereHas('installments', function($q) {
            $q->where('status', 'pending');
        })->with(['loanApplication', 'installments' => function($q) {
            $q->orderBy('installment_date');
        }])->get();

        // Get upcoming installments (next 30 days)
        $upcomingInstallments = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '>=', Carbon::now())
        ->where('installment_date', '<=', Carbon::now()->addDays(30))
        ->with(['loan.loanApplication'])
        ->orderBy('installment_date')
        ->get();

        // Get overdue installments
        $overdueInstallments = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', Carbon::now())
        ->with(['loan.loanApplication'])
        ->orderBy('installment_date')
        ->get();

        return view('member.installments.schedule', compact(
            'activeLoans',
            'upcomingInstallments',
            'overdueInstallments',
            'member'
        ));
    }

    public function history(Request $request)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return redirect()->route('member.dashboard')->with('error', 'Member profile not found.');
        }

        // Build query for paid installments
        $query = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'paid')
        ->with(['loan.loanApplication', 'collectedBy']);

        // Apply filters
        if ($request->filled('loan_id')) {
            $query->where('loan_id', $request->loan_id);
        }

        if ($request->filled('from_date')) {
            $query->where('collection_date', '>=', Carbon::parse($request->from_date));
        }

        if ($request->filled('to_date')) {
            $query->where('collection_date', '<=', Carbon::parse($request->to_date));
        }

        if ($request->filled('collected_by')) {
            $query->where('collected_by', $request->collected_by);
        }

        $paidInstallments = $query->latest('collection_date')->paginate(20);

        // Get member's loans for filter dropdown
        $memberLoans = $member->loans()->with('loanApplication')->get();

        // Get field officers who collected payments
        $collectors = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'paid')
        ->with('collectedBy')
        ->get()
        ->pluck('collectedBy')
        ->unique('id')
        ->filter();

        // Calculate payment statistics
        $paymentStats = [
            'total_payments' => $paidInstallments->total(),
            'total_amount_paid' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'paid')->sum('amount'),
            'total_late_fees_paid' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'paid')->sum('late_fee'),
            'average_payment_amount' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })->where('status', 'paid')->avg('amount'),
            'on_time_payments' => Installment::whereHas('loan', function($q) use ($member) {
                $q->where('member_id', $member->id);
            })
            ->where('status', 'paid')
            ->whereColumn('collection_date', '<=', 'installment_date')
            ->count(),
        ];

        return view('member.installments.history', compact(
            'paidInstallments',
            'paymentStats',
            'memberLoans',
            'collectors',
            'member'
        ));
    }

    public function downloadReceipt(Installment $installment)
    {
        $user = auth()->user();
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member || $installment->loan->loanApplication->member_id !== $member->id) {
            abort(403, 'Unauthorized access to installment receipt.');
        }

        if ($installment->status !== 'paid') {
            return redirect()->back()->with('error', 'Receipt is only available for paid installments.');
        }

        $installment->load(['loan.loanApplication.member.branch', 'collectedBy']);

        $data = [
            'installment' => $installment,
            'member' => $member,
            'generated_at' => Carbon::now(),
        ];

        $pdf = Pdf::loadView('member.installments.receipt-pdf', $data);
        
        $filename = "installment-receipt-{$installment->id}-" . 
                   $installment->collection_date->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }
}
