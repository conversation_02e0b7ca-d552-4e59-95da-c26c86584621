<?php

namespace App\Http\Controllers\Member;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Loan;
use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use App\Models\Installment;
use App\Models\LoanApplication;
use Carbon\Carbon;

class MemberDashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        // Find member record for this user using member_id relationship
        $member = $user->member_id ? Member::where('member_id', $user->member_id)->first() : null;

        if (!$member) {
            return view('member.no-profile', compact('user'));
        }

        // Get comprehensive dashboard data
        $stats = $this->getDashboardStats($member);
        $upcomingInstallments = $this->getUpcomingInstallments($member);
        $overdueInstallments = $this->getOverdueInstallments($member);
        $recentTransactions = $this->getRecentTransactions($member);
        $notifications = $this->getNotifications($member);

        return view('member.dashboard', compact(
            'stats',
            'user',
            'member',
            'upcomingInstallments',
            'overdueInstallments',
            'recentTransactions',
            'notifications'
        ));
    }

    private function getDashboardStats(Member $member): array
    {
        $activeLoans = $member->loans()->whereHas('installments', function($q) {
            $q->where('status', 'pending');
        })->get();

        $totalSavings = $member->savingAccounts()->where('status', 'active')->sum('current_balance');
        $totalLoanAmount = $activeLoans->sum('loan_amount');

        // Calculate monthly installment from active loans
        $monthlyInstallment = $activeLoans->sum(function($loan) {
            return $loan->installments()
                ->where('status', 'pending')
                ->where('installment_date', '>=', Carbon::now()->startOfMonth())
                ->where('installment_date', '<=', Carbon::now()->endOfMonth())
                ->sum('amount');
        });

        return [
            'total_savings' => $totalSavings,
            'active_loans' => $activeLoans->count(),
            'total_loan_amount' => $totalLoanAmount,
            'monthly_installment' => $monthlyInstallment,
            'savings_accounts' => $member->savingAccounts()->where('status', 'active')->get(),
            'loans' => $activeLoans,
            'pending_applications' => $member->loanApplications()->where('status', 'pending')->count(),
            'total_paid_amount' => $member->getTotalPaidAmount(),
            'outstanding_amount' => $member->getTotalOutstandingAmount(),
        ];
    }

    private function getUpcomingInstallments(Member $member, int $days = 7): \Illuminate\Database\Eloquent\Collection
    {
        return Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '>=', Carbon::now())
        ->where('installment_date', '<=', Carbon::now()->addDays($days))
        ->with(['loan'])
        ->orderBy('installment_date')
        ->take(5)
        ->get();
    }

    private function getOverdueInstallments(Member $member): \Illuminate\Database\Eloquent\Collection
    {
        return Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', Carbon::now())
        ->with(['loan'])
        ->orderBy('installment_date')
        ->take(5)
        ->get();
    }

    private function getRecentTransactions(Member $member): \Illuminate\Database\Eloquent\Collection
    {
        return SavingTransaction::whereHas('savingAccount', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->with(['savingAccount'])
        ->latest('transaction_date')
        ->take(10)
        ->get();
    }

    private function getNotifications(Member $member): array
    {
        $notifications = [];

        // Check for overdue payments
        $overdueCount = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', Carbon::now())
        ->count();

        if ($overdueCount > 0) {
            $notifications[] = [
                'type' => 'danger',
                'icon' => 'fas fa-exclamation-triangle',
                'title' => 'Overdue Payments',
                'message' => "You have {$overdueCount} overdue installment(s). Please make payment as soon as possible.",
                'action_url' => route('member.installments.index'),
                'action_text' => 'View Details'
            ];
        }

        // Check for upcoming payments (next 3 days)
        $upcomingCount = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '>=', Carbon::now())
        ->where('installment_date', '<=', Carbon::now()->addDays(3))
        ->count();

        if ($upcomingCount > 0) {
            $notifications[] = [
                'type' => 'warning',
                'icon' => 'fas fa-calendar-alt',
                'title' => 'Upcoming Payments',
                'message' => "You have {$upcomingCount} payment(s) due in the next 3 days.",
                'action_url' => route('member.installments.index'),
                'action_text' => 'View Schedule'
            ];
        }

        return $notifications;
    }
}
