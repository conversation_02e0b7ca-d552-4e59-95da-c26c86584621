<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use App\Services\FileManagerService;
use App\Services\DocumentManagerService;
use App\Services\SecurityService;

class FileController extends Controller
{
    protected FileManagerService $fileManager;
    protected DocumentManagerService $documentManager;
    protected SecurityService $securityService;

    public function __construct(
        FileManagerService $fileManager,
        DocumentManagerService $documentManager,
        SecurityService $securityService
    ) {
        $this->fileManager = $fileManager;
        $this->documentManager = $documentManager;
        $this->securityService = $securityService;
    }

    /**
     * Secure file download with access control
     */
    public function secureDownload(Request $request)
    {
        try {
            $disk = $request->get('disk');
            $encryptedPath = $request->get('path');
            $encryptedExpires = $request->get('expires');
            
            // Decrypt and validate parameters
            $filePath = decrypt($encryptedPath);
            
            if ($encryptedExpires) {
                $expiresAt = decrypt($encryptedExpires);
                if (now()->timestamp > $expiresAt) {
                    abort(410, 'Download link has expired');
                }
            }
            
            // Validate disk
            if (!in_array($disk, ['documents', 'local', 'backups', 'images'])) {
                abort(400, 'Invalid disk specified');
            }
            
            // Check if file exists
            if (!Storage::disk($disk)->exists($filePath)) {
                abort(404, 'File not found');
            }
            
            // Check user permissions
            if (!$this->fileManager->canAccessFile($filePath, $disk, auth()->user())) {
                abort(403, 'Access denied');
            }
            
            // Log download attempt
            Log::info('Secure file download', [
                'user_id' => auth()->id(),
                'file_path' => $filePath,
                'disk' => $disk,
                'ip' => $request->ip()
            ]);
            
            // Get file info
            $fileInfo = $this->fileManager->getFileInfo($filePath, $disk);
            
            // Return file response
            return Storage::disk($disk)->download($filePath, basename($filePath), [
                'Content-Type' => $fileInfo['mime_type'],
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Secure download failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'request_data' => $request->all()
            ]);
            
            abort(500, 'Download failed');
        }
    }

    /**
     * Secure document download with token validation
     */
    public function secureDocumentDownload(Request $request)
    {
        try {
            $token = $request->get('token');
            
            if (!$token) {
                abort(400, 'Invalid download token');
            }
            
            // Decrypt token
            $tokenData = decrypt($token);
            
            // Validate token structure
            if (!isset($tokenData['path'], $tokenData['user_id'], $tokenData['expires'])) {
                abort(400, 'Invalid token format');
            }
            
            // Check expiration
            if (now()->timestamp > $tokenData['expires']) {
                abort(410, 'Download token has expired');
            }
            
            // Validate user
            if (auth()->id() !== $tokenData['user_id']) {
                abort(403, 'Token not valid for current user');
            }
            
            $filePath = $tokenData['path'];
            
            // Check if file exists
            if (!Storage::disk('documents')->exists($filePath)) {
                abort(404, 'Document not found');
            }
            
            // Log download
            Log::info('Secure document download', [
                'user_id' => auth()->id(),
                'file_path' => $filePath,
                'ip' => $request->ip()
            ]);
            
            // Get file info
            $fileInfo = $this->fileManager->getFileInfo($filePath, 'documents');
            
            // Return file response
            return Storage::disk('documents')->download($filePath, basename($filePath), [
                'Content-Type' => $fileInfo['mime_type'],
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Secure document download failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'token' => $request->get('token')
            ]);
            
            abort(500, 'Download failed');
        }
    }

    /**
     * Generate thumbnail on-the-fly
     */
    public function thumbnail(Request $request)
    {
        try {
            $disk = $request->get('disk', 'images');
            $path = $request->get('path');
            $width = (int) $request->get('width', 150);
            $height = (int) $request->get('height', 150);
            
            // Validate parameters
            if (!$path || !Storage::disk($disk)->exists($path)) {
                abort(404, 'Image not found');
            }
            
            // Check if it's an image file
            $mimeType = Storage::disk($disk)->mimeType($path);
            if (!str_starts_with($mimeType, 'image/')) {
                abort(400, 'File is not an image');
            }
            
            // Check user permissions for private images
            if (in_array($disk, ['documents', 'local']) && 
                !$this->fileManager->canAccessFile($path, $disk, auth()->user())) {
                abort(403, 'Access denied');
            }
            
            // Generate thumbnail using ImageProcessingService
            $imageProcessor = app(\App\Services\ImageProcessingService::class);
            $thumbnailPath = $imageProcessor->generateThumbnail($path, $disk, $width, $height);
            
            // Return thumbnail
            $thumbnailContent = Storage::disk($disk)->get($thumbnailPath);
            
            return response($thumbnailContent, 200, [
                'Content-Type' => 'image/jpeg',
                'Cache-Control' => 'public, max-age=3600',
                'Expires' => gmdate('D, d M Y H:i:s', time() + 3600) . ' GMT'
            ]);
            
        } catch (\Exception $e) {
            Log::error('Thumbnail generation failed', [
                'error' => $e->getMessage(),
                'path' => $request->get('path'),
                'disk' => $request->get('disk')
            ]);
            
            // Return placeholder image or 404
            abort(404, 'Thumbnail generation failed');
        }
    }

    /**
     * Upload file via AJAX
     */
    public function upload(Request $request)
    {
        try {
            $request->validate([
                'file' => 'required|file|max:10240', // 10MB max
                'category' => 'required|string|in:members,advertisements,documents,reports',
                'options' => 'sometimes|array'
            ]);
            
            $file = $request->file('file');
            $category = $request->get('category');
            $options = $request->get('options', []);
            
            // Check user permissions
            if (!$this->canUploadToCategory($category, auth()->user())) {
                return response()->json(['error' => 'Permission denied'], 403);
            }
            
            // Upload file
            $result = $this->fileManager->uploadFile($file, $category, $options);
            
            return response()->json([
                'success' => true,
                'message' => 'File uploaded successfully',
                'data' => $result
            ]);
            
        } catch (\Exception $e) {
            Log::error('File upload failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'category' => $request->get('category')
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Upload failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Delete file
     */
    public function delete(Request $request)
    {
        try {
            $request->validate([
                'file_data' => 'required|array',
                'file_data.*.path' => 'required|string',
                'file_data.*.disk' => 'required|string'
            ]);
            
            $fileData = $request->get('file_data');
            
            // Check permissions
            foreach ($fileData as $file) {
                if (!$this->fileManager->canAccessFile($file['path'], $file['disk'], auth()->user())) {
                    return response()->json(['error' => 'Permission denied'], 403);
                }
            }
            
            // Delete files
            $deleted = $this->fileManager->deleteFile($fileData);
            
            if ($deleted) {
                return response()->json([
                    'success' => true,
                    'message' => 'File deleted successfully'
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Some files could not be deleted'
                ], 500);
            }
            
        } catch (\Exception $e) {
            Log::error('File deletion failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id(),
                'file_data' => $request->get('file_data')
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Deletion failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Get storage statistics (admin only)
     */
    public function storageStats(Request $request)
    {
        if (!auth()->user()->hasRole('admin')) {
            abort(403, 'Admin access required');
        }
        
        try {
            $stats = $this->fileManager->getStorageStats();
            
            return response()->json([
                'success' => true,
                'data' => $stats
            ]);
            
        } catch (\Exception $e) {
            Log::error('Storage stats retrieval failed', [
                'error' => $e->getMessage(),
                'user_id' => auth()->id()
            ]);
            
            return response()->json([
                'success' => false,
                'message' => 'Failed to retrieve storage statistics'
            ], 500);
        }
    }

    /**
     * Check if user can upload to category
     */
    protected function canUploadToCategory(string $category, $user): bool
    {
        if ($user->hasRole('admin')) {
            return true;
        }
        
        return match ($category) {
            'members' => $user->hasRole(['field-officer', 'manager']),
            'advertisements' => $user->hasRole(['manager', 'admin']),
            'documents' => $user->hasRole(['field-officer', 'manager']),
            'reports' => $user->hasRole(['manager', 'admin']),
            default => false
        };
    }
}
