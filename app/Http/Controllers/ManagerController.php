<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use App\Models\Installment;
use App\Models\ActivityLog;
use Carbon\Carbon;

class ManagerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:manager', 'branch.access']);
    }

    /**
     * Branch-specific dashboard data
     */
    public function dashboard()
    {
        try {
            $user = auth()->user();
            $branch = $user->branch;

            if (!$branch) {
                return view('manager.dashboard')->with('error', 'You are not assigned to any branch.');
            }

            // Branch statistics
            $stats = $this->getBranchStats($branch);
            
            // Performance metrics
            $performanceMetrics = $this->getBranchPerformanceMetrics($branch);
            
            // Field officer performance
            $fieldOfficerPerformance = $this->getFieldOfficerPerformance($branch);
            
            // Recent activities
            $recentActivities = $this->getBranchRecentActivities($branch);
            
            // Pending approvals
            $pendingApprovals = $this->getPendingApprovals($branch);
            
            // Financial summary
            $financialSummary = $this->getBranchFinancialSummary($branch);
            
            // Alerts
            $alerts = $this->getBranchAlerts($branch);

            return view('manager.dashboard', compact(
                'branch',
                'stats',
                'performanceMetrics',
                'fieldOfficerPerformance',
                'recentActivities',
                'pendingApprovals',
                'financialSummary',
                'alerts'
            ));
        } catch (\Exception $e) {
            \Log::error('Manager dashboard error: ' . $e->getMessage());
            return view('manager.dashboard')->with('error', 'Unable to load dashboard data.');
        }
    }

    /**
     * Loan application approval/rejection
     */
    public function loanApplications(Request $request)
    {
        $user = auth()->user();
        $branch = $user->branch;

        $query = LoanApplication::with(['member.branch', 'reviewedBy'])
            ->whereHas('member', function ($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            });

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('field_officer')) {
            $query->whereHas('member', function ($q) use ($request) {
                $q->where('created_by', $request->field_officer);
            });
        }

        if ($request->filled('date_from')) {
            $query->where('application_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('application_date', '<=', $request->date_to);
        }

        $applications = $query->latest('application_date')->paginate(20);
        $fieldOfficers = $branch->users()->where('role', 'field_officer')->get();

        return view('manager.loan-applications.index', compact('applications', 'fieldOfficers', 'branch'));
    }

    public function approveLoanApplication(Request $request, LoanApplication $application)
    {
        $user = auth()->user();
        
        // Verify branch access
        if ($application->member->branch_id !== $user->branch_id) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        if ($application->status !== 'pending') {
            return back()->with('error', 'This application has already been processed.');
        }

        $validator = Validator::make($request->all(), [
            'approved_amount' => 'required|numeric|min:1000|max:500000',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,bi_weekly,monthly',
            'advance_payment' => 'nullable|numeric|min:0',
            'first_installment_date' => 'required|date|after:today',
            'approval_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Calculate last installment date
            $firstInstallmentDate = Carbon::parse($request->first_installment_date);
            $lastInstallmentDate = $this->calculateLastInstallmentDate(
                $firstInstallmentDate,
                $request->loan_duration_months,
                $request->repayment_method
            );

            // Create loan
            $loan = $application->approve(auth()->user(), [
                'loan_amount' => $request->approved_amount,
                'interest_rate' => $request->interest_rate,
                'loan_duration_months' => $request->loan_duration_months,
                'repayment_method' => $request->repayment_method,
                'loan_date' => now(),
                'advance_payment' => $request->advance_payment ?? 0,
                'first_installment_date' => $firstInstallmentDate,
                'last_installment_date' => $lastInstallmentDate,
            ]);

            // Generate installments
            $loan->generateInstallments();

            // Update application with approval notes
            $application->update([
                'approval_notes' => $request->approval_notes,
                'approved_amount' => $request->approved_amount,
            ]);

            $this->logActivity(
                'loan_approved',
                "Loan application #{$application->id} approved for member {$application->member->name}",
                $application->member,
                [
                    'application_id' => $application->id,
                    'approved_amount' => $request->approved_amount,
                    'loan_id' => $loan->id,
                ]
            );

            DB::commit();

            return redirect()->route('manager.loan-applications.index')
                ->with('success', 'Loan application approved successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Loan approval failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to approve loan application. Please try again.')->withInput();
        }
    }

    public function rejectLoanApplication(Request $request, LoanApplication $application)
    {
        $user = auth()->user();
        
        // Verify branch access
        if ($application->member->branch_id !== $user->branch_id) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        if ($application->status !== 'pending') {
            return back()->with('error', 'This application has already been processed.');
        }

        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $application->reject(auth()->user(), $request->rejection_reason);

            $this->logActivity(
                'loan_rejected',
                "Loan application #{$application->id} rejected for member {$application->member->name}",
                $application->member,
                [
                    'application_id' => $application->id,
                    'rejection_reason' => $request->rejection_reason,
                ]
            );

            DB::commit();

            return redirect()->route('manager.loan-applications.index')
                ->with('success', 'Loan application rejected.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Loan rejection failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to reject loan application. Please try again.')->withInput();
        }
    }

    /**
     * Branch financial management
     */
    public function financialManagement(Request $request)
    {
        $user = auth()->user();
        $branch = $user->branch;

        $period = $request->get('period', 'monthly');
        $startDate = $request->get('start_date', now()->subMonths(6));
        $endDate = $request->get('end_date', now());

        $financialData = [
            'loan_portfolio' => $this->getBranchLoanPortfolio($branch, $startDate, $endDate),
            'collections' => $this->getBranchCollections($branch, $startDate, $endDate),
            'savings' => $this->getBranchSavings($branch, $startDate, $endDate),
            'performance_indicators' => $this->getBranchPerformanceIndicators($branch, $startDate, $endDate),
            'cash_flow' => $this->getBranchCashFlow($branch, $period, $startDate, $endDate),
        ];

        return view('manager.financial-management', compact('branch', 'financialData', 'period', 'startDate', 'endDate'));
    }

    /**
     * Field officer performance tracking
     */
    public function fieldOfficerPerformance(Request $request)
    {
        $user = auth()->user();
        $branch = $user->branch;

        $period = $request->get('period', 'monthly');
        $fieldOfficerId = $request->get('field_officer_id');

        $query = $branch->users()->where('role', 'field_officer');

        if ($fieldOfficerId) {
            $query->where('id', $fieldOfficerId);
        }

        $fieldOfficers = $query->get();

        $performanceData = $fieldOfficers->map(function ($officer) use ($period) {
            return [
                'officer' => $officer,
                'metrics' => $this->getFieldOfficerMetrics($officer, $period),
                'targets' => $this->getFieldOfficerTargets($officer, $period),
                'achievements' => $this->getFieldOfficerAchievements($officer, $period),
            ];
        });

        return view('manager.field-officer-performance', compact('branch', 'performanceData', 'period', 'fieldOfficers'));
    }

    /**
     * Branch reports generation
     */
    public function generateReport(Request $request)
    {
        $user = auth()->user();
        $branch = $user->branch;

        $reportType = $request->get('type', 'summary');
        $format = $request->get('format', 'pdf');
        $startDate = $request->get('start_date', now()->subMonth());
        $endDate = $request->get('end_date', now());

        try {
            switch ($reportType) {
                case 'summary':
                    return $this->generateBranchSummaryReport($branch, $format, $startDate, $endDate);
                case 'loans':
                    return $this->generateBranchLoansReport($branch, $format, $startDate, $endDate);
                case 'collections':
                    return $this->generateBranchCollectionsReport($branch, $format, $startDate, $endDate);
                case 'members':
                    return $this->generateBranchMembersReport($branch, $format, $startDate, $endDate);
                case 'performance':
                    return $this->generateBranchPerformanceReport($branch, $format, $startDate, $endDate);
                default:
                    return back()->with('error', 'Invalid report type.');
            }
        } catch (\Exception $e) {
            \Log::error('Report generation failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to generate report. Please try again.');
        }
    }

    /**
     * Helper methods for business logic
     */
    private function getBranchStats(Branch $branch): array
    {
        return [
            'total_members' => $branch->members()->count(),
            'active_members' => $branch->members()->where('status', 'active')->count(),
            'total_field_officers' => $branch->users()->where('role', 'field_officer')->where('is_active', true)->count(),
            'total_loans' => $branch->getTotalLoans(),
            'active_loans' => $branch->getTotalActiveLoans(),
            'total_loan_amount' => $branch->getTotalLoanAmount(),
            'total_collections' => $branch->getTotalCollections(),
            'total_savings' => $branch->getTotalSavings(),
            'pending_applications' => $branch->getPendingApplications(),
            'overdue_installments' => $branch->getOverdueInstallments(),
            'collection_rate' => $branch->getCollectionRate(),
        ];
    }

    private function getBranchPerformanceMetrics(Branch $branch): array
    {
        $currentMonth = now()->startOfMonth();
        $lastMonth = now()->subMonth()->startOfMonth();

        return [
            'monthly_disbursements' => [
                'current' => $branch->getMonthlyDisbursements($currentMonth),
                'previous' => $branch->getMonthlyDisbursements($lastMonth),
            ],
            'monthly_collections' => [
                'current' => $branch->getMonthlyCollections($currentMonth),
                'previous' => $branch->getMonthlyCollections($lastMonth),
            ],
            'monthly_new_members' => [
                'current' => $branch->getMonthlyNewMembers($currentMonth),
                'previous' => $branch->getMonthlyNewMembers($lastMonth),
            ],
            'portfolio_at_risk' => $branch->getPortfolioAtRisk(),
            'operational_self_sufficiency' => $branch->getOperationalSelfSufficiency(),
        ];
    }

    private function getFieldOfficerPerformance(Branch $branch): \Illuminate\Database\Eloquent\Collection
    {
        return $branch->users()
            ->where('role', 'field_officer')
            ->where('is_active', true)
            ->get()
            ->map(function ($officer) {
                return [
                    'officer' => $officer,
                    'total_members' => $officer->getCreatedMembersCount(),
                    'active_loans' => $officer->getActiveLoanCount(),
                    'collection_rate' => $officer->getCollectionRate(),
                    'monthly_target' => $officer->getMonthlyTarget(),
                    'monthly_achievement' => $officer->getMonthlyAchievement(),
                ];
            });
    }

    private function getBranchRecentActivities(Branch $branch, int $limit = 15): \Illuminate\Database\Eloquent\Collection
    {
        return ActivityLog::with('user')
            ->whereHas('user', function ($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })
            ->latest()
            ->take($limit)
            ->get();
    }

    private function getPendingApprovals(Branch $branch): \Illuminate\Database\Eloquent\Collection
    {
        return LoanApplication::with(['member'])
            ->whereHas('member', function ($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })
            ->where('status', 'pending')
            ->latest('application_date')
            ->take(10)
            ->get();
    }

    private function getBranchFinancialSummary(Branch $branch): array
    {
        return [
            'total_portfolio' => $branch->getTotalLoanAmount(),
            'outstanding_amount' => $branch->getOutstandingAmount(),
            'total_savings' => $branch->getTotalSavings(),
            'monthly_target' => $branch->getMonthlyTarget(),
            'monthly_achievement' => $branch->getMonthlyAchievement(),
            'profit_margin' => $branch->getProfitMargin(),
        ];
    }

    private function getBranchAlerts(Branch $branch): array
    {
        $alerts = [];

        // High overdue rate
        $overdueRate = $branch->getOverdueRate();
        if ($overdueRate > 15) {
            $alerts[] = [
                'type' => 'danger',
                'title' => 'High Overdue Rate',
                'message' => "Branch overdue rate is {$overdueRate}%. Immediate action required.",
            ];
        }

        // Low collection rate
        $collectionRate = $branch->getCollectionRate();
        if ($collectionRate < 85) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Low Collection Rate',
                'message' => "Collection rate has dropped to {$collectionRate}%.",
            ];
        }

        // Pending applications
        $pendingCount = $branch->getPendingApplications();
        if ($pendingCount > 20) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'Pending Applications',
                'message' => "{$pendingCount} loan applications are pending your review.",
            ];
        }

        return $alerts;
    }

    private function calculateLastInstallmentDate(Carbon $firstDate, int $durationMonths, string $repaymentMethod): Carbon
    {
        $installmentCount = $this->calculateInstallmentCount($durationMonths, $repaymentMethod);

        return match($repaymentMethod) {
            'weekly' => $firstDate->copy()->addWeeks($installmentCount - 1),
            'bi_weekly' => $firstDate->copy()->addWeeks(($installmentCount - 1) * 2),
            'monthly' => $firstDate->copy()->addMonths($installmentCount - 1),
            default => $firstDate->copy()->addMonths($durationMonths),
        };
    }

    private function calculateInstallmentCount(int $durationMonths, string $repaymentMethod): int
    {
        return match($repaymentMethod) {
            'weekly' => $durationMonths * 4,
            'bi_weekly' => $durationMonths * 2,
            'monthly' => $durationMonths,
            default => $durationMonths,
        };
    }

    /**
     * Log activity
     */
    private function logActivity(string $action, string $description, ?Member $member = null, array $metadata = []): void
    {
        try {
            ActivityLog::create([
                'user_id' => auth()->id(),
                'action' => $action,
                'description' => $description,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'metadata' => array_merge($metadata, [
                    'timestamp' => now()->toISOString(),
                    'branch_id' => auth()->user()->branch_id,
                    'member_id' => $member?->id,
                ]),
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }
}
