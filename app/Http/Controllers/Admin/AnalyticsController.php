<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\Branch;
use App\Models\User;
use Carbon\Carbon;

class AnalyticsController extends Controller
{
    /**
     * Display analytics dashboard
     */
    public function index(Request $request)
    {
        $period = $request->get('period', 'current_month');
        
        // Calculate date range
        switch ($period) {
            case 'last_month':
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                break;
            case 'last_3_months':
                $startDate = Carbon::now()->subMonths(3)->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;
            case 'current_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;
            default: // current_month
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
        }

        // Key metrics
        $metrics = [
            'total_members' => Member::count(),
            'new_members' => Member::whereBetween('created_at', [$startDate, $endDate])->count(),
            'total_loans' => Loan::count(),
            'active_loans' => Loan::where('status', 'active')->count(),
            'total_loan_amount' => Loan::sum('loan_amount'),
            'total_outstanding' => Loan::where('status', 'active')->sum('outstanding_amount'),
            'total_collected' => Installment::where('status', 'paid')->sum('amount'),
            'overdue_amount' => Installment::where('status', 'pending')
                ->where('installment_date', '<', Carbon::now())
                ->sum('amount'),
        ];

        // Branch performance
        $branchPerformance = Branch::withCount(['members', 'activeLoans'])
            ->with(['activeLoans' => function($q) {
                $q->select('branch_id', \DB::raw('SUM(loan_amount) as total_disbursed'))
                  ->groupBy('branch_id');
            }])
            ->get();

        return view('admin.analytics.index', compact('metrics', 'branchPerformance', 'period'));
    }

    /**
     * Get installment trends data
     */
    public function installmentTrends(Request $request)
    {
        $months = $request->get('months', 12);
        
        $trends = [];
        for ($i = $months - 1; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            
            $collected = Installment::where('status', 'paid')
                ->whereMonth('collection_date', $month->month)
                ->whereYear('collection_date', $month->year)
                ->sum('amount');
                
            $due = Installment::whereMonth('installment_date', $month->month)
                ->whereYear('installment_date', $month->year)
                ->sum('amount');
            
            $trends[] = [
                'month' => $month->format('M Y'),
                'collected' => $collected,
                'due' => $due,
                'collection_rate' => $due > 0 ? round(($collected / $due) * 100, 2) : 0,
            ];
        }

        return response()->json($trends);
    }

    /**
     * Get default rates data
     */
    public function defaultRates(Request $request)
    {
        $period = $request->get('period', 'current_year');
        
        switch ($period) {
            case 'last_year':
                $startDate = Carbon::now()->subYear()->startOfYear();
                $endDate = Carbon::now()->subYear()->endOfYear();
                break;
            default: // current_year
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
        }

        $totalLoans = Loan::whereBetween('disbursement_date', [$startDate, $endDate])->count();
        $defaultedLoans = Loan::where('status', 'defaulted')
            ->whereBetween('disbursement_date', [$startDate, $endDate])
            ->count();
        
        $defaultRate = $totalLoans > 0 ? round(($defaultedLoans / $totalLoans) * 100, 2) : 0;

        // Monthly breakdown
        $monthlyDefaults = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            
            $monthTotal = Loan::whereMonth('disbursement_date', $month->month)
                ->whereYear('disbursement_date', $month->year)
                ->count();
                
            $monthDefaulted = Loan::where('status', 'defaulted')
                ->whereMonth('disbursement_date', $month->month)
                ->whereYear('disbursement_date', $month->year)
                ->count();
            
            $monthlyDefaults[] = [
                'month' => $month->format('M Y'),
                'total_loans' => $monthTotal,
                'defaulted_loans' => $monthDefaulted,
                'default_rate' => $monthTotal > 0 ? round(($monthDefaulted / $monthTotal) * 100, 2) : 0,
            ];
        }

        return response()->json([
            'overall_default_rate' => $defaultRate,
            'monthly_breakdown' => $monthlyDefaults,
        ]);
    }

    /**
     * Get branch comparison data
     */
    public function branchComparison(Request $request)
    {
        $branches = Branch::with(['members', 'activeLoans'])
            ->get()
            ->map(function($branch) {
                $totalDisbursed = $branch->activeLoans->sum('loan_amount');
                $totalCollected = Installment::whereHas('loan', function($q) use ($branch) {
                    $q->whereHas('member', function($subQ) use ($branch) {
                        $subQ->where('branch_id', $branch->id);
                    });
                })->where('status', 'paid')->sum('amount');
                
                $totalDue = Installment::whereHas('loan', function($q) use ($branch) {
                    $q->whereHas('member', function($subQ) use ($branch) {
                        $subQ->where('branch_id', $branch->id);
                    });
                })->sum('amount');

                return [
                    'branch_name' => $branch->name,
                    'total_members' => $branch->members->count(),
                    'active_loans' => $branch->activeLoans->count(),
                    'total_disbursed' => $totalDisbursed,
                    'total_collected' => $totalCollected,
                    'collection_rate' => $totalDue > 0 ? round(($totalCollected / $totalDue) * 100, 2) : 0,
                ];
            });

        return response()->json($branches);
    }

    /**
     * Get financial summary
     */
    public function financialSummary(Request $request)
    {
        $period = $request->get('period', 'current_month');
        
        switch ($period) {
            case 'last_month':
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                break;
            case 'current_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;
            default: // current_month
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
        }

        $summary = [
            'total_disbursed' => Loan::whereBetween('disbursement_date', [$startDate, $endDate])
                ->sum('loan_amount'),
            'total_collected' => Installment::where('status', 'paid')
                ->whereBetween('collection_date', [$startDate, $endDate])
                ->sum('amount'),
            'interest_earned' => Installment::where('status', 'paid')
                ->whereBetween('collection_date', [$startDate, $endDate])
                ->sum('interest_amount'),
            'outstanding_amount' => Loan::where('status', 'active')->sum('outstanding_amount'),
            'overdue_amount' => Installment::where('status', 'pending')
                ->where('installment_date', '<', Carbon::now())
                ->sum('amount'),
        ];

        return response()->json($summary);
    }

    /**
     * Export analytics data
     */
    public function export(Request $request, $type)
    {
        $request->validate([
            'format' => 'required|in:pdf,excel',
        ]);

        switch ($type) {
            case 'branch-performance':
                return $this->exportBranchPerformance($request->format);
            case 'financial-summary':
                return $this->exportFinancialSummary($request->format);
            case 'member-statistics':
                return $this->exportMemberStatistics($request->format);
            default:
                return back()->with('error', 'Invalid export type.');
        }
    }

    private function exportBranchPerformance($format)
    {
        $branches = Branch::with(['members', 'activeLoans'])->get();
        
        if ($format === 'pdf') {
            $pdf = \PDF::loadView('admin.analytics.exports.branch-performance', compact('branches'));
            return $pdf->download('branch-performance-' . now()->format('Y-m-d') . '.pdf');
        }

        return back()->with('info', 'Excel export feature coming soon.');
    }

    private function exportFinancialSummary($format)
    {
        // Implementation for financial summary export
        return back()->with('info', 'Financial summary export feature coming soon.');
    }

    private function exportMemberStatistics($format)
    {
        // Implementation for member statistics export
        return back()->with('info', 'Member statistics export feature coming soon.');
    }
}
