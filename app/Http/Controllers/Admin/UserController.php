<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Illuminate\Validation\Rule;

class UserController extends Controller
{
    /**
     * Display a listing of users.
     */
    public function index(Request $request)
    {
        $query = User::with(['branch', 'member']);

        // Role filter
        if ($request->filled('role')) {
            $query->where('role', $request->role);
        }

        // Status filter
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhereHas('branch', function ($branchQuery) use ($search) {
                      $branchQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $users = $query->orderBy('created_at', 'desc')->paginate(15)->withQueryString();

        $stats = [
            'total_users' => User::count(),
            'active_users' => User::where('is_active', true)->count(),
            'admins' => User::where('role', 'admin')->count(),
            'managers' => User::where('role', 'manager')->count(),
            'field_officers' => User::where('role', 'field_officer')->count(),
            'members' => User::where('role', 'member')->count(),
        ];

        return view('admin.users.index', compact('users', 'stats'));
    }

    /**
     * Show the form for creating a new user.
     */
    public function create()
    {
        $branches = Branch::all();
        $availableMembers = Member::whereDoesntHave('user')->get();

        return view('admin.users.create', compact('branches', 'availableMembers'));
    }

    /**
     * Store a newly created user.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users',
            'password' => 'required|string|min:8|confirmed',
            'role' => 'required|in:admin,manager,field_officer,member',
            'branch_id' => 'nullable|exists:branches,id',
            'member_id' => 'nullable|exists:members,id',
            'is_active' => 'boolean',
        ], [
            'name.required' => 'Name is required.',
            'email.required' => 'Email address is required.',
            'email.unique' => 'This email address is already registered.',
            'password.required' => 'Password is required.',
            'password.min' => 'Password must be at least 8 characters.',
            'password.confirmed' => 'Password confirmation does not match.',
            'role.required' => 'User role is required.',
            'role.in' => 'Invalid user role selected.',
        ]);

        // Additional validation based on role
        if ($validated['role'] === 'member' && !$validated['member_id']) {
            return back()->withErrors(['member_id' => 'Member selection is required for member role.'])->withInput();
        }

        if (in_array($validated['role'], ['manager', 'field_officer']) && !$validated['branch_id']) {
            return back()->withErrors(['branch_id' => 'Branch assignment is required for this role.'])->withInput();
        }

        $validated['password'] = Hash::make($validated['password']);
        $validated['is_active'] = $request->boolean('is_active', true);

        DB::transaction(function () use ($validated) {
            $user = User::create($validated);

            // Update member's user association if member role
            if ($validated['role'] === 'member' && $validated['member_id']) {
                Member::where('id', $validated['member_id'])
                    ->update(['member_id' => $user->member_id ?? $user->id]);
            }
        });

        return redirect()->route('admin.users.index')
            ->with('success', 'User created successfully.');
    }

    /**
     * Display the specified user.
     */
    public function show(User $user)
    {
        $user->load(['branch', 'member', 'managedBranches', 'createdMembers']);

        $stats = [];
        
        if ($user->isManager()) {
            $stats = [
                'managed_branches' => $user->managedBranches->count(),
                'branch_members' => $user->branch ? $user->branch->members->count() : 0,
                'branch_loans' => $user->branch ? $user->branch->getTotalActiveLoans() : 0,
            ];
        } elseif ($user->isFieldOfficer()) {
            $stats = [
                'created_members' => $user->createdMembers->count(),
                'collected_installments' => $user->collectedInstallments->count(),
                'branch_name' => $user->branch ? $user->branch->name : 'Not assigned',
            ];
        } elseif ($user->isMember() && $user->member) {
            $stats = [
                'total_loans' => $user->member->getTotalLoans(),
                'savings_balance' => $user->member->getSavingsBalance(),
                'loan_status' => $user->member->getCurrentLoanStatus(),
            ];
        }

        return view('admin.users.show', compact('user', 'stats'));
    }

    /**
     * Show the form for editing the specified user.
     */
    public function edit(User $user)
    {
        $branches = Branch::all();
        $availableMembers = Member::whereDoesntHave('user')
            ->orWhere('id', $user->member_id)
            ->get();

        return view('admin.users.edit', compact('user', 'branches', 'availableMembers'));
    }

    /**
     * Update the specified user.
     */
    public function update(Request $request, User $user)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'email' => ['required', 'string', 'email', 'max:255', Rule::unique('users')->ignore($user->id)],
            'role' => 'required|in:admin,manager,field_officer,member',
            'branch_id' => 'nullable|exists:branches,id',
            'member_id' => 'nullable|exists:members,id',
            'is_active' => 'boolean',
        ]);

        // Additional validation based on role
        if ($validated['role'] === 'member' && !$validated['member_id']) {
            return back()->withErrors(['member_id' => 'Member selection is required for member role.'])->withInput();
        }

        if (in_array($validated['role'], ['manager', 'field_officer']) && !$validated['branch_id']) {
            return back()->withErrors(['branch_id' => 'Branch assignment is required for this role.'])->withInput();
        }

        $validated['is_active'] = $request->boolean('is_active', true);

        $user->update($validated);

        return redirect()->route('admin.users.index')
            ->with('success', 'User updated successfully.');
    }

    /**
     * Remove the specified user.
     */
    public function destroy(User $user)
    {
        // Prevent deletion of the last admin
        if ($user->isAdmin() && User::where('role', 'admin')->count() <= 1) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Cannot delete the last administrator.');
        }

        // Check if user is managing branches
        if ($user->managedBranches()->count() > 0) {
            return redirect()->route('admin.users.index')
                ->with('error', 'Cannot delete user who is managing branches. Please reassign branches first.');
        }

        $user->delete();

        return redirect()->route('admin.users.index')
            ->with('success', 'User deleted successfully.');
    }

    /**
     * Reset user password.
     */
    public function resetPassword(Request $request, User $user)
    {
        $validated = $request->validate([
            'password' => 'required|string|min:8|confirmed',
        ]);

        $user->update([
            'password' => Hash::make($validated['password']),
        ]);

        return response()->json(['success' => 'Password reset successfully.']);
    }

    /**
     * Toggle user active status.
     */
    public function toggleStatus(User $user)
    {
        // Prevent deactivating the last admin
        if ($user->isAdmin() && $user->is_active && User::where('role', 'admin')->where('is_active', true)->count() <= 1) {
            return response()->json(['error' => 'Cannot deactivate the last active administrator.'], 400);
        }

        $user->update(['is_active' => !$user->is_active]);

        $status = $user->is_active ? 'activated' : 'deactivated';
        return response()->json(['success' => "User {$status} successfully."]);
    }

    /**
     * Bulk actions for users.
     */
    public function bulkAction(Request $request)
    {
        $validated = $request->validate([
            'action' => 'required|in:activate,deactivate,delete',
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        $users = User::whereIn('id', $validated['user_ids']);

        switch ($validated['action']) {
            case 'activate':
                $users->update(['is_active' => true]);
                $message = 'Selected users activated successfully.';
                break;
            case 'deactivate':
                // Prevent deactivating all admins
                $adminCount = User::where('role', 'admin')->where('is_active', true)->count();
                $selectedAdmins = $users->where('role', 'admin')->where('is_active', true)->count();
                
                if ($adminCount <= $selectedAdmins) {
                    return response()->json(['error' => 'Cannot deactivate all administrators.'], 400);
                }
                
                $users->update(['is_active' => false]);
                $message = 'Selected users deactivated successfully.';
                break;
            case 'delete':
                // Prevent deleting all admins
                $adminCount = User::where('role', 'admin')->count();
                $selectedAdmins = $users->where('role', 'admin')->count();
                
                if ($adminCount <= $selectedAdmins) {
                    return response()->json(['error' => 'Cannot delete all administrators.'], 400);
                }
                
                $users->delete();
                $message = 'Selected users deleted successfully.';
                break;
        }

        return response()->json(['success' => $message]);
    }
}
