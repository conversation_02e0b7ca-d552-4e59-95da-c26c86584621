<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Installment;
use Carbon\Carbon;

class ActivityController extends Controller
{
    /**
     * Display system activities log
     */
    public function index(Request $request)
    {
        $activities = collect();

        // Get recent member registrations
        $recentMembers = Member::with(['createdBy', 'branch'])
            ->latest('created_at')
            ->take(20)
            ->get()
            ->map(function($member) {
                return [
                    'type' => 'member_registration',
                    'title' => 'New Member Registered',
                    'description' => "Member {$member->name} (ID: {$member->member_id}) was registered",
                    'user' => $member->createdBy->name ?? 'System',
                    'branch' => $member->branch->name ?? 'N/A',
                    'created_at' => $member->created_at,
                    'icon' => 'user-plus',
                    'color' => 'green',
                ];
            });

        // Get recent loan applications
        $recentApplications = LoanApplication::with(['member', 'reviewedBy'])
            ->latest('applied_at')
            ->take(20)
            ->get()
            ->map(function($application) {
                $statusColors = [
                    'pending' => 'yellow',
                    'approved' => 'green',
                    'rejected' => 'red',
                ];

                return [
                    'type' => 'loan_application',
                    'title' => 'Loan Application ' . ucfirst($application->status),
                    'description' => "Loan application for {$application->member->name} - Amount: ৳" . number_format($application->requested_amount),
                    'user' => $application->reviewedBy->name ?? 'Pending Review',
                    'branch' => $application->member->branch->name ?? 'N/A',
                    'created_at' => $application->reviewed_at ?? $application->applied_at,
                    'icon' => 'file-text',
                    'color' => $statusColors[$application->status] ?? 'gray',
                ];
            });

        // Get recent loan disbursements
        $recentLoans = Loan::with(['member', 'createdBy'])
            ->latest('disbursement_date')
            ->take(20)
            ->get()
            ->map(function($loan) {
                return [
                    'type' => 'loan_disbursement',
                    'title' => 'Loan Disbursed',
                    'description' => "Loan of ৳" . number_format($loan->loan_amount) . " disbursed to {$loan->member->name}",
                    'user' => $loan->createdBy->name ?? 'System',
                    'branch' => $loan->member->branch->name ?? 'N/A',
                    'created_at' => $loan->disbursement_date,
                    'icon' => 'dollar-sign',
                    'color' => 'blue',
                ];
            });

        // Get recent installment collections
        $recentCollections = Installment::where('status', 'paid')
            ->with(['loan.member', 'collectedBy'])
            ->latest('collection_date')
            ->take(20)
            ->get()
            ->map(function($installment) {
                return [
                    'type' => 'installment_collection',
                    'title' => 'Installment Collected',
                    'description' => "Installment of ৳" . number_format($installment->amount) . " collected from {$installment->loan->member->name}",
                    'user' => $installment->collectedBy->name ?? 'System',
                    'branch' => $installment->loan->member->branch->name ?? 'N/A',
                    'created_at' => $installment->collection_date,
                    'icon' => 'credit-card',
                    'color' => 'green',
                ];
            });

        // Get recent user activities
        $recentUsers = User::latest('created_at')
            ->take(10)
            ->get()
            ->map(function($user) {
                return [
                    'type' => 'user_registration',
                    'title' => 'New User Created',
                    'description' => "User {$user->name} ({$user->email}) was created with role: {$user->getRoleNames()->first()}",
                    'user' => 'Admin',
                    'branch' => $user->branch->name ?? 'N/A',
                    'created_at' => $user->created_at,
                    'icon' => 'user',
                    'color' => 'purple',
                ];
            });

        // Merge all activities
        $activities = $activities
            ->merge($recentMembers)
            ->merge($recentApplications)
            ->merge($recentLoans)
            ->merge($recentCollections)
            ->merge($recentUsers)
            ->sortByDesc('created_at');

        // Apply filters
        if ($request->filled('type')) {
            $activities = $activities->where('type', $request->type);
        }

        if ($request->filled('date_from')) {
            $dateFrom = Carbon::parse($request->date_from)->startOfDay();
            $activities = $activities->where('created_at', '>=', $dateFrom);
        }

        if ($request->filled('date_to')) {
            $dateTo = Carbon::parse($request->date_to)->endOfDay();
            $activities = $activities->where('created_at', '<=', $dateTo);
        }

        if ($request->filled('search')) {
            $search = strtolower($request->search);
            $activities = $activities->filter(function($activity) use ($search) {
                return str_contains(strtolower($activity['title']), $search) ||
                       str_contains(strtolower($activity['description']), $search) ||
                       str_contains(strtolower($activity['user']), $search);
            });
        }

        // Paginate manually
        $perPage = 20;
        $currentPage = $request->get('page', 1);
        $offset = ($currentPage - 1) * $perPage;
        
        $paginatedActivities = $activities->slice($offset, $perPage)->values();
        $total = $activities->count();

        // Calculate summary statistics
        $summary = [
            'total_activities' => $total,
            'today_activities' => $activities->where('created_at', '>=', Carbon::today())->count(),
            'this_week_activities' => $activities->where('created_at', '>=', Carbon::now()->startOfWeek())->count(),
            'this_month_activities' => $activities->where('created_at', '>=', Carbon::now()->startOfMonth())->count(),
        ];

        // Activity types for filter
        $activityTypes = [
            'member_registration' => 'Member Registrations',
            'loan_application' => 'Loan Applications',
            'loan_disbursement' => 'Loan Disbursements',
            'installment_collection' => 'Installment Collections',
            'user_registration' => 'User Registrations',
        ];

        return view('admin.activities.index', compact(
            'paginatedActivities',
            'summary',
            'activityTypes',
            'total',
            'currentPage',
            'perPage'
        ));
    }
}
