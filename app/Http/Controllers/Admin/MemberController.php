<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\SavingAccount;
use App\Models\Installment;
use App\Models\Branch;
use Carbon\Carbon;

class MemberController extends Controller
{
    /**
     * Display all members (admin has access to all)
     */
    public function index(Request $request)
    {
        $query = Member::with(['branch', 'createdBy']);

        // Search by name or member ID
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by loan status
        if ($request->filled('loan_status')) {
            switch ($request->loan_status) {
                case 'active':
                    $query->whereHas('loans', function($q) {
                        $q->where('status', 'active');
                    });
                    break;
                case 'no_loan':
                    $query->whereDoesntHave('loans', function($q) {
                        $q->where('status', 'active');
                    });
                    break;
                case 'overdue':
                    $query->whereHas('loans', function($q) {
                        $q->where('status', 'active')
                          ->whereHas('installments', function($subQ) {
                              $subQ->where('status', 'pending')
                                   ->where('installment_date', '<', Carbon::now());
                          });
                    });
                    break;
            }
        }

        $members = $query->latest('created_at')->paginate(15);
        $branches = Branch::all();

        // Calculate summary statistics
        $totalMembers = Member::count();
        $activeLoans = Loan::where('status', 'active')->count();
        
        $overdueMembers = Member::whereHas('loans', function($q) {
            $q->where('status', 'active')
              ->whereHas('installments', function($subQ) {
                  $subQ->where('status', 'pending')
                       ->where('installment_date', '<', Carbon::now());
              });
        })->count();

        $summary = [
            'total_members' => $totalMembers,
            'active_loans' => $activeLoans,
            'overdue_members' => $overdueMembers,
            'no_loan_members' => $totalMembers - $activeLoans,
        ];

        return view('admin.members.index', compact('members', 'branches', 'summary'));
    }

    /**
     * Show the form for creating a new member
     */
    public function create()
    {
        $branches = Branch::all();
        $existingMembers = Member::select('id', 'name', 'member_id')->get();

        return view('admin.members.create', compact('branches', 'existingMembers'));
    }

    /**
     * Store a newly created member
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:members',
            'nid_number' => 'required|string|max:20|unique:members',
            'address' => 'required|string',
            'branch_id' => 'required|exists:branches,id',
            'date_of_birth' => 'required|date',
            'gender' => 'required|in:male,female,other',
            'occupation' => 'required|string|max:255',
            'monthly_income' => 'required|numeric|min:0',
            'reference_member_id' => 'nullable|exists:members,id',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $memberData = $request->except(['photo']);
        $memberData['member_id'] = Member::generateMemberId();
        $memberData['created_by'] = auth()->id();

        // Handle photo upload if provided
        if ($request->hasFile('photo')) {
            // Simple photo handling for now
            $photoPath = $request->file('photo')->store('member-photos', 'public');
            $memberData['photo'] = json_encode(['original' => $photoPath]);
        }

        $member = Member::create($memberData);

        return redirect()->route('admin.members.index')
            ->with('success', 'Member created successfully.');
    }

    /**
     * Display the specified member
     */
    public function show(Member $member)
    {
        $member->load(['branch', 'createdBy', 'reference']);

        // Get member's loan history
        $loans = Loan::where('member_id', $member->id)
            ->with(['loanApplication', 'installments'])
            ->latest('disbursement_date')
            ->get();

        // Get member's savings accounts
        $savingAccounts = SavingAccount::where('member_id', $member->id)
            ->with(['createdBy'])
            ->latest('created_at')
            ->get();

        // Get member's loan applications
        $loanApplications = LoanApplication::where('member_id', $member->id)
            ->with(['reviewedBy'])
            ->latest('applied_at')
            ->get();

        // Calculate member statistics
        $stats = [
            'total_loans' => $loans->count(),
            'active_loans' => $loans->where('status', 'active')->count(),
            'completed_loans' => $loans->where('status', 'completed')->count(),
            'total_loan_amount' => $loans->sum('loan_amount'),
            'total_paid_amount' => $loans->sum(function($loan) {
                return $loan->installments->where('status', 'paid')->sum('amount');
            }),
            'total_outstanding' => $loans->where('status', 'active')->sum(function($loan) {
                return $loan->installments->where('status', 'pending')->sum('amount');
            }),
            'total_savings_balance' => $savingAccounts->sum('current_balance'),
            'pending_applications' => $loanApplications->where('status', 'pending')->count(),
        ];

        return view('admin.members.show', compact('member', 'loans', 'savingAccounts', 'loanApplications', 'stats'));
    }

    /**
     * Show the form for editing the specified member
     */
    public function edit(Member $member)
    {
        $branches = Branch::all();
        $existingMembers = Member::where('id', '!=', $member->id)
            ->select('id', 'name', 'member_id')
            ->get();

        return view('admin.members.edit', compact('member', 'branches', 'existingMembers'));
    }

    /**
     * Update the specified member
     */
    public function update(Request $request, Member $member)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'phone_number' => 'required|string|max:20|unique:members,phone_number,' . $member->id,
            'nid_number' => 'required|string|max:20|unique:members,nid_number,' . $member->id,
            'address' => 'required|string',
            'branch_id' => 'required|exists:branches,id',
            'date_of_birth' => 'required|date',
            'gender' => 'required|in:male,female,other',
            'occupation' => 'required|string|max:255',
            'monthly_income' => 'required|numeric|min:0',
            'reference_member_id' => 'nullable|exists:members,id',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ]);

        $updateData = $request->except(['photo']);

        // Handle photo upload if provided
        if ($request->hasFile('photo')) {
            $photoPath = $request->file('photo')->store('member-photos', 'public');
            $updateData['photo'] = json_encode(['original' => $photoPath]);
        }

        $member->update($updateData);

        return redirect()->route('admin.members.show', $member)
            ->with('success', 'Member updated successfully.');
    }

    /**
     * Remove the specified member
     */
    public function destroy(Member $member)
    {
        // Check if member has active loans
        if ($member->loans()->where('status', 'active')->exists()) {
            return back()->with('error', 'Cannot delete member with active loans.');
        }

        $member->delete();

        return redirect()->route('admin.members.index')
            ->with('success', 'Member deleted successfully.');
    }

    /**
     * Show member's loan history
     */
    public function loans(Member $member)
    {
        $loans = Loan::where('member_id', $member->id)
            ->with(['loanApplication', 'installments'])
            ->latest('disbursement_date')
            ->paginate(10);

        return view('admin.members.loans', compact('member', 'loans'));
    }

    /**
     * Show member's savings history
     */
    public function savings(Member $member)
    {
        $savingAccounts = SavingAccount::where('member_id', $member->id)
            ->with(['createdBy', 'transactions'])
            ->latest('created_at')
            ->paginate(10);

        return view('admin.members.savings', compact('member', 'savingAccounts'));
    }

    /**
     * Export member data to PDF
     */
    public function exportPdf(Request $request)
    {
        $query = Member::with(['branch', 'createdBy']);

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        $members = $query->get();

        $pdf = \PDF::loadView('admin.members.export-pdf', compact('members'));
        return $pdf->download('members-export-' . now()->format('Y-m-d') . '.pdf');
    }
}
