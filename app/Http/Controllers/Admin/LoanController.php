<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Member;
use App\Models\Installment;
use Carbon\Carbon;

class LoanController extends Controller
{
    /**
     * Show pending loan applications
     */
    public function pending(Request $request)
    {
        $query = LoanApplication::where('status', 'pending')
            ->with(['member', 'member.branch', 'reviewedBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('member', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%");
            });
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->whereHas('member', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
        }

        $applications = $query->latest('applied_at')->paginate(15);

        return view('admin.loans.pending', compact('applications'));
    }

    /**
     * Show active loans
     */
    public function active(Request $request)
    {
        $query = Loan::where('status', 'active')
            ->with(['member', 'member.branch', 'loanApplication']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('member', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%");
            });
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->whereHas('member', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
        }

        $loans = $query->latest('disbursement_date')->paginate(15);

        return view('admin.loans.active', compact('loans'));
    }

    /**
     * Show overdue loans
     */
    public function overdue(Request $request)
    {
        $query = Loan::where('status', 'active')
            ->whereHas('installments', function($q) {
                $q->where('status', 'pending')
                  ->where('installment_date', '<', Carbon::now());
            })
            ->with(['member', 'member.branch', 'installments' => function($q) {
                $q->where('status', 'pending')
                  ->where('installment_date', '<', Carbon::now())
                  ->orderBy('installment_date');
            }]);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('member', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%");
            });
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->whereHas('member', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
        }

        $overdueLoans = $query->get();

        return view('admin.loans.overdue', compact('overdueLoans'));
    }

    /**
     * Approve loan application
     */
    public function approve(Request $request, LoanApplication $application)
    {
        $request->validate([
            'approved_amount' => 'required|numeric|min:1000|max:' . $application->requested_amount,
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_term_months' => 'required|integer|min:1|max:60',
            'notes' => 'nullable|string|max:1000',
        ]);

        if ($application->status !== 'pending') {
            return back()->with('error', 'This application has already been processed.');
        }

        // Update application
        $application->update([
            'status' => 'approved',
            'approved_amount' => $request->approved_amount,
            'approved_interest_rate' => $request->interest_rate,
            'approved_loan_term' => $request->loan_term_months,
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now(),
            'review_notes' => $request->notes,
        ]);

        // Create loan record
        $loan = Loan::create([
            'member_id' => $application->member_id,
            'loan_application_id' => $application->id,
            'loan_amount' => $request->approved_amount,
            'interest_rate' => $request->interest_rate,
            'loan_term_months' => $request->loan_term_months,
            'disbursement_date' => now(),
            'status' => 'active',
            'created_by' => auth()->id(),
        ]);

        // Generate installment schedule
        $loan->generateInstallmentSchedule();

        return back()->with('success', 'Loan application approved successfully.');
    }

    /**
     * Reject loan application
     */
    public function reject(Request $request, LoanApplication $application)
    {
        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        if ($application->status !== 'pending') {
            return back()->with('error', 'This application has already been processed.');
        }

        $application->update([
            'status' => 'rejected',
            'reviewed_by' => auth()->id(),
            'reviewed_at' => now(),
            'review_notes' => $request->rejection_reason,
        ]);

        return back()->with('success', 'Loan application rejected.');
    }

    /**
     * Show loan calculator
     */
    public function calculator()
    {
        return view('admin.loans.calculator');
    }
}
