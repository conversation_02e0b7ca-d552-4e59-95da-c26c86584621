<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Advertisement;
use Illuminate\Support\Facades\Storage;

class AdvertisementController extends Controller
{
    /**
     * Display a listing of advertisements
     */
    public function index(Request $request)
    {
        $query = Advertisement::with(['createdBy']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('content', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by type
        if ($request->filled('type')) {
            $query->where('type', $request->type);
        }

        $advertisements = $query->latest('created_at')->paginate(15);

        return view('admin.advertisements.index', compact('advertisements'));
    }

    /**
     * Show the form for creating a new advertisement
     */
    public function create()
    {
        return view('admin.advertisements.create');
    }

    /**
     * Store a newly created advertisement
     */
    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:banner,popup,notification,news',
            'target_audience' => 'required|in:all,members,field_officers,managers',
            'priority' => 'required|in:low,medium,high',
            'start_date' => 'required|date|after_or_equal:today',
            'end_date' => 'required|date|after:start_date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link_url' => 'nullable|url',
            'is_active' => 'boolean',
        ]);

        $data = $request->except(['image']);
        $data['created_by'] = auth()->id();
        $data['status'] = $request->boolean('is_active') ? 'active' : 'draft';

        // Handle image upload
        if ($request->hasFile('image')) {
            $imagePath = $request->file('image')->store('advertisements', 'public');
            $data['image_path'] = $imagePath;
        }

        Advertisement::create($data);

        return redirect()->route('admin.advertisements.index')
            ->with('success', 'Advertisement created successfully.');
    }

    /**
     * Display the specified advertisement
     */
    public function show(Advertisement $advertisement)
    {
        $advertisement->load(['createdBy']);
        return view('admin.advertisements.show', compact('advertisement'));
    }

    /**
     * Show the form for editing the specified advertisement
     */
    public function edit(Advertisement $advertisement)
    {
        return view('admin.advertisements.edit', compact('advertisement'));
    }

    /**
     * Update the specified advertisement
     */
    public function update(Request $request, Advertisement $advertisement)
    {
        $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => 'required|in:banner,popup,notification,news',
            'target_audience' => 'required|in:all,members,field_officers,managers',
            'priority' => 'required|in:low,medium,high',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'link_url' => 'nullable|url',
            'is_active' => 'boolean',
        ]);

        $data = $request->except(['image']);
        $data['status'] = $request->boolean('is_active') ? 'active' : 'draft';

        // Handle image upload
        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($advertisement->image_path) {
                Storage::disk('public')->delete($advertisement->image_path);
            }
            
            $imagePath = $request->file('image')->store('advertisements', 'public');
            $data['image_path'] = $imagePath;
        }

        $advertisement->update($data);

        return redirect()->route('admin.advertisements.show', $advertisement)
            ->with('success', 'Advertisement updated successfully.');
    }

    /**
     * Remove the specified advertisement
     */
    public function destroy(Advertisement $advertisement)
    {
        // Delete associated image
        if ($advertisement->image_path) {
            Storage::disk('public')->delete($advertisement->image_path);
        }

        $advertisement->delete();

        return redirect()->route('admin.advertisements.index')
            ->with('success', 'Advertisement deleted successfully.');
    }

    /**
     * Toggle advertisement status
     */
    public function toggleStatus(Advertisement $advertisement)
    {
        $newStatus = $advertisement->status === 'active' ? 'inactive' : 'active';
        $advertisement->update(['status' => $newStatus]);

        $message = $newStatus === 'active' ? 'Advertisement activated.' : 'Advertisement deactivated.';
        
        return back()->with('success', $message);
    }

    /**
     * Preview advertisement
     */
    public function preview(Advertisement $advertisement)
    {
        return view('admin.advertisements.preview', compact('advertisement'));
    }
}
