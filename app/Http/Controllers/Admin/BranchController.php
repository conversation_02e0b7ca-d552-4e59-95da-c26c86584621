<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Branch;
use App\Models\User;
use Illuminate\Support\Facades\DB;

class BranchController extends Controller
{
    /**
     * Display a listing of branches.
     */
    public function index(Request $request)
    {
        $query = Branch::with(['manager', 'users', 'members']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('address', 'like', "%{$search}%")
                  ->orWhereHas('manager', function ($managerQuery) use ($search) {
                      $managerQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        $branches = $query->paginate(10)->withQueryString();

        return view('admin.branches.index', compact('branches'));
    }

    /**
     * Show the form for creating a new branch.
     */
    public function create()
    {
        $availableManagers = User::where('role', 'manager')
            ->where('is_active', true)
            ->whereDoesntHave('managedBranches')
            ->get();

        return view('admin.branches.create', compact('availableManagers'));
    }

    /**
     * Store a newly created branch.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:branches,name',
            'address' => 'required|string|max:500',
            'manager_id' => 'nullable|exists:users,id',
        ], [
            'name.required' => 'Branch name is required.',
            'name.unique' => 'A branch with this name already exists.',
            'address.required' => 'Branch address is required.',
            'manager_id.exists' => 'Selected manager is invalid.',
        ]);

        DB::transaction(function () use ($validated) {
            $branch = Branch::create($validated);

            // Update manager's branch assignment if manager is selected
            if ($validated['manager_id']) {
                User::where('id', $validated['manager_id'])
                    ->update(['branch_id' => $branch->id]);
            }
        });

        return redirect()->route('admin.branches.index')
            ->with('success', 'Branch created successfully.');
    }

    /**
     * Display the specified branch.
     */
    public function show(Branch $branch)
    {
        $branch->load(['manager', 'users', 'members.loans', 'members.savingAccounts']);

        $stats = [
            'total_members' => $branch->members->count(),
            'active_loans' => $branch->getTotalActiveLoans(),
            'total_disbursed' => $branch->getMonthlyLoanDisbursement(),
            'total_collections' => $branch->getTotalCollections(),
            'savings_balance' => $branch->getTotalSavingsBalance(),
            'field_officers' => $branch->getFieldOfficers()->count(),
            'overdue_loans' => $branch->getOverdueLoansCount(),
        ];

        return view('admin.branches.show', compact('branch', 'stats'));
    }

    /**
     * Show the form for editing the specified branch.
     */
    public function edit(Branch $branch)
    {
        $availableManagers = User::where('role', 'manager')
            ->where('is_active', true)
            ->where(function ($query) use ($branch) {
                $query->whereDoesntHave('managedBranches')
                      ->orWhere('id', $branch->manager_id);
            })
            ->get();

        return view('admin.branches.edit', compact('branch', 'availableManagers'));
    }

    /**
     * Update the specified branch.
     */
    public function update(Request $request, Branch $branch)
    {
        $validated = $request->validate([
            'name' => 'required|string|max:255|unique:branches,name,' . $branch->id,
            'address' => 'required|string|max:500',
            'manager_id' => 'nullable|exists:users,id',
        ], [
            'name.required' => 'Branch name is required.',
            'name.unique' => 'A branch with this name already exists.',
            'address.required' => 'Branch address is required.',
            'manager_id.exists' => 'Selected manager is invalid.',
        ]);

        DB::transaction(function () use ($branch, $validated) {
            // Remove previous manager's branch assignment
            if ($branch->manager_id) {
                User::where('id', $branch->manager_id)
                    ->update(['branch_id' => null]);
            }

            // Update branch
            $branch->update($validated);

            // Assign new manager to branch
            if ($validated['manager_id']) {
                User::where('id', $validated['manager_id'])
                    ->update(['branch_id' => $branch->id]);
            }
        });

        return redirect()->route('admin.branches.index')
            ->with('success', 'Branch updated successfully.');
    }

    /**
     * Remove the specified branch.
     */
    public function destroy(Branch $branch)
    {
        // Check if branch has members or active loans
        if ($branch->members()->count() > 0) {
            return redirect()->route('admin.branches.index')
                ->with('error', 'Cannot delete branch with existing members.');
        }

        if ($branch->loans()->active()->count() > 0) {
            return redirect()->route('admin.branches.index')
                ->with('error', 'Cannot delete branch with active loans.');
        }

        DB::transaction(function () use ($branch) {
            // Remove manager's branch assignment
            if ($branch->manager_id) {
                User::where('id', $branch->manager_id)
                    ->update(['branch_id' => null]);
            }

            // Remove all users' branch assignments
            User::where('branch_id', $branch->id)
                ->update(['branch_id' => null]);

            $branch->delete();
        });

        return redirect()->route('admin.branches.index')
            ->with('success', 'Branch deleted successfully.');
    }

    /**
     * Get branch statistics for AJAX requests.
     */
    public function statistics(Branch $branch)
    {
        $stats = [
            'total_members' => $branch->members->count(),
            'active_loans' => $branch->getTotalActiveLoans(),
            'total_disbursed' => $branch->getMonthlyLoanDisbursement(),
            'total_collections' => $branch->getTotalCollections(),
            'savings_balance' => $branch->getTotalSavingsBalance(),
            'field_officers' => $branch->getFieldOfficers()->count(),
            'overdue_loans' => $branch->getOverdueLoansCount(),
        ];

        return response()->json($stats);
    }

    /**
     * Assign manager to branch.
     */
    public function assignManager(Request $request, Branch $branch)
    {
        $validated = $request->validate([
            'manager_id' => 'required|exists:users,id',
        ]);

        $manager = User::findOrFail($validated['manager_id']);

        if ($manager->role !== 'manager') {
            return response()->json(['error' => 'Selected user is not a manager.'], 400);
        }

        if ($manager->managedBranches()->count() > 0) {
            return response()->json(['error' => 'Manager is already assigned to another branch.'], 400);
        }

        DB::transaction(function () use ($branch, $manager) {
            // Remove previous manager
            if ($branch->manager_id) {
                User::where('id', $branch->manager_id)
                    ->update(['branch_id' => null]);
            }

            // Assign new manager
            $branch->update(['manager_id' => $manager->id]);
            $manager->update(['branch_id' => $branch->id]);
        });

        return response()->json(['success' => 'Manager assigned successfully.']);
    }
}
