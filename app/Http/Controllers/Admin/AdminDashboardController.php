<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\SavingAccount;
use App\Models\Branch;
use App\Models\BranchTransaction;
use App\Models\Installment;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AdminDashboardController extends Controller
{
    public function index()
    {
        // Basic Statistics
        $stats = [
            'total_users' => User::count(),
            'total_members' => Member::count(),
            'total_branches' => Branch::count(),
            'active_loans' => Loan::active()->count(),
            'pending_applications' => LoanApplication::pending()->count(),
            'total_loan_amount' => Loan::sum('loan_amount'),
            'total_savings_balance' => SavingAccount::sum('current_balance'),
            'overdue_loans' => Loan::overdue()->count(),
        ];

        // Monthly Loan Disbursement (Last 12 months)
        $monthlyDisbursement = Loan::select(
            DB::raw('YEAR(loan_date) as year'),
            DB::raw('MONTH(loan_date) as month'),
            DB::raw('SUM(loan_amount) as total_amount'),
            DB::raw('COUNT(*) as loan_count')
        )
        ->where('loan_date', '>=', Carbon::now()->subMonths(12))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        // Collection Trends (Last 12 months)
        $collectionTrends = Installment::select(
            DB::raw('YEAR(collection_date) as year'),
            DB::raw('MONTH(collection_date) as month'),
            DB::raw('SUM(amount) as total_collected'),
            DB::raw('COUNT(*) as installment_count')
        )
        ->where('status', 'paid')
        ->where('collection_date', '>=', Carbon::now()->subMonths(12))
        ->groupBy('year', 'month')
        ->orderBy('year', 'asc')
        ->orderBy('month', 'asc')
        ->get();

        // Branch Performance
        $branchPerformance = Branch::with(['loans', 'members'])
            ->get()
            ->map(function ($branch) {
                return [
                    'name' => $branch->name,
                    'total_members' => $branch->members->count(),
                    'active_loans' => $branch->loans()->active()->count(),
                    'total_disbursed' => $branch->loans()->sum('loan_amount'),
                    'collection_rate' => $this->calculateCollectionRate($branch),
                ];
            });

        // Recent Activities
        $recentActivities = collect();

        // Recent loan applications
        $recentApplications = LoanApplication::with(['member'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($app) {
                return [
                    'type' => 'loan_application',
                    'description' => "New loan application from {$app->member->name}",
                    'amount' => $app->applied_amount,
                    'date' => $app->applied_at,
                    'status' => $app->status,
                ];
            });

        // Recent loan approvals
        $recentLoans = Loan::with(['loanApplication.member'])
            ->latest()
            ->take(5)
            ->get()
            ->map(function ($loan) {
                return [
                    'type' => 'loan_approval',
                    'description' => "Loan approved for {$loan->loanApplication->member->name}",
                    'amount' => $loan->loan_amount,
                    'date' => $loan->loan_date,
                    'status' => 'approved',
                ];
            });

        $recentActivities = $recentApplications->concat($recentLoans)
            ->sortByDesc('date')
            ->take(10);

        // Quick Stats for Cards
        $quickStats = [
            'today_collections' => Installment::where('status', 'paid')
                ->whereDate('collection_date', today())
                ->sum('amount'),
            'this_month_disbursement' => Loan::whereMonth('loan_date', now()->month)
                ->whereYear('loan_date', now()->year)
                ->sum('loan_amount'),
            'new_members_this_month' => Member::whereMonth('created_at', now()->month)
                ->whereYear('created_at', now()->year)
                ->count(),
            'collection_rate' => $this->calculateOverallCollectionRate(),
        ];

        return view('admin.dashboard', compact(
            'stats',
            'monthlyDisbursement',
            'collectionTrends',
            'branchPerformance',
            'recentActivities',
            'quickStats'
        ));
    }

    private function calculateCollectionRate($branch)
    {
        $totalDue = $branch->loans()
            ->with('installments')
            ->get()
            ->sum(function ($loan) {
                return $loan->installments->where('installment_date', '<=', now())->sum('amount');
            });

        $totalCollected = $branch->loans()
            ->with('installments')
            ->get()
            ->sum(function ($loan) {
                return $loan->installments->where('status', 'paid')->sum('amount');
            });

        return $totalDue > 0 ? round(($totalCollected / $totalDue) * 100, 2) : 0;
    }

    private function calculateOverallCollectionRate()
    {
        $totalDue = Installment::where('installment_date', '<=', now())->sum('amount');
        $totalCollected = Installment::where('status', 'paid')->sum('amount');

        return $totalDue > 0 ? round(($totalCollected / $totalDue) * 100, 2) : 0;
    }
}
