<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;
use App\Models\User;
use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use App\Models\Installment;
use App\Models\ActivityLog;
use Carbon\Carbon;

class FieldOfficerController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth', 'role:field_officer', 'branch.access']);
    }

    /**
     * Personal dashboard metrics
     */
    public function dashboard()
    {
        try {
            $user = auth()->user();
            $branch = $user->branch;

            if (!$branch) {
                return view('field-officer.dashboard')->with('error', 'You are not assigned to any branch.');
            }

            // Personal performance metrics
            $personalMetrics = $this->getPersonalMetrics($user);
            
            // Member statistics
            $memberStats = $this->getMemberStats($user);
            
            // Loan statistics
            $loanStats = $this->getLoanStats($user);
            
            // Collection statistics
            $collectionStats = $this->getCollectionStats($user);
            
            // Recent activities
            $recentActivities = $this->getRecentActivities($user);
            
            // Pending tasks
            $pendingTasks = $this->getPendingTasks($user);
            
            // Performance targets
            $targets = $this->getPerformanceTargets($user);
            
            // Alerts
            $alerts = $this->getPersonalAlerts($user);

            return view('field-officer.dashboard', compact(
                'user',
                'branch',
                'personalMetrics',
                'memberStats',
                'loanStats',
                'collectionStats',
                'recentActivities',
                'pendingTasks',
                'targets',
                'alerts'
            ));
        } catch (\Exception $e) {
            \Log::error('Field Officer dashboard error: ' . $e->getMessage());
            return view('field-officer.dashboard')->with('error', 'Unable to load dashboard data.');
        }
    }

    /**
     * Member registration processing
     */
    public function members(Request $request)
    {
        $user = auth()->user();
        
        $query = Member::with(['branch', 'reference'])
            ->where('created_by', $user->id);

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        $members = $query->latest()->paginate(20);

        return view('field-officer.members.index', compact('members', 'user'));
    }

    public function createMember()
    {
        $user = auth()->user();
        $branch = $user->branch;
        
        // Get potential reference members from the same branch
        $referenceMembers = Member::where('branch_id', $branch->id)
            ->where('status', 'active')
            ->get();

        return view('field-officer.members.create', compact('branch', 'referenceMembers'));
    }

    public function storeMember(Request $request)
    {
        $user = auth()->user();
        
        $validated = $request->validate([
            'name' => 'required|string|max:255',
            'father_or_husband_name' => 'required|string|max:255',
            'mother_name' => 'required|string|max:255',
            'date_of_birth' => 'required|date|before:today',
            'nid_number' => 'required|string|max:20|unique:members,nid_number',
            'phone_number' => 'required|string|max:15',
            'present_address' => 'required|string|max:500',
            'permanent_address' => 'required|string|max:500',
            'occupation' => 'required|string|max:100',
            'religion' => 'required|string|max:50',
            'blood_group' => 'nullable|string|max:5',
            'reference_member_id' => 'nullable|exists:members,id',
            'photo' => 'nullable|image|mimes:jpeg,png,jpg|max:2048',
        ], [
            'name.required' => 'Member name is required.',
            'nid_number.unique' => 'This NID number is already registered.',
            'date_of_birth.before' => 'Date of birth must be before today.',
            'phone_number.required' => 'Phone number is required.',
        ]);

        try {
            DB::beginTransaction();

            // Generate unique member ID
            $memberIdPrefix = $user->branch->code ?? 'BR';
            $memberCount = Member::where('branch_id', $user->branch_id)->count() + 1;
            $memberId = $memberIdPrefix . str_pad($memberCount, 4, '0', STR_PAD_LEFT);

            // Handle photo upload
            $photoPath = null;
            if ($request->hasFile('photo')) {
                $photoPath = $request->file('photo')->store('member-photos', 'public');
            }

            $member = Member::create([
                'member_id' => $memberId,
                'name' => $validated['name'],
                'father_or_husband_name' => $validated['father_or_husband_name'],
                'mother_name' => $validated['mother_name'],
                'date_of_birth' => $validated['date_of_birth'],
                'nid_number' => $validated['nid_number'],
                'phone_number' => $validated['phone_number'],
                'present_address' => $validated['present_address'],
                'permanent_address' => $validated['permanent_address'],
                'occupation' => $validated['occupation'],
                'religion' => $validated['religion'],
                'blood_group' => $validated['blood_group'],
                'reference_member_id' => $validated['reference_member_id'],
                'photo' => $photoPath,
                'branch_id' => $user->branch_id,
                'created_by' => $user->id,
                'status' => 'active',
            ]);

            $this->logActivity(
                'member_created',
                "New member {$member->name} registered with ID {$member->member_id}",
                $member
            );

            DB::commit();

            return redirect()->route('field-officer.members.index')
                ->with('success', 'Member registered successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Member registration failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to register member. Please try again.')->withInput();
        }
    }

    /**
     * Loan application submission
     */
    public function loanApplications(Request $request)
    {
        $user = auth()->user();
        
        $query = LoanApplication::with(['member', 'reviewedBy'])
            ->whereHas('member', function ($q) use ($user) {
                $q->where('created_by', $user->id);
            });

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('member_id')) {
            $query->where('member_id', $request->member_id);
        }

        $applications = $query->latest('application_date')->paginate(20);
        $members = Member::where('created_by', $user->id)->where('status', 'active')->get();

        return view('field-officer.loan-applications.index', compact('applications', 'members'));
    }

    public function createLoanApplication()
    {
        $user = auth()->user();
        
        // Get eligible members (active members without pending applications)
        $eligibleMembers = Member::where('created_by', $user->id)
            ->where('status', 'active')
            ->whereDoesntHave('loanApplications', function ($q) {
                $q->where('status', 'pending');
            })
            ->get();

        return view('field-officer.loan-applications.create', compact('eligibleMembers'));
    }

    public function storeLoanApplication(Request $request)
    {
        $user = auth()->user();
        
        $validated = $request->validate([
            'member_id' => 'required|exists:members,id',
            'requested_amount' => 'required|numeric|min:1000|max:500000',
            'purpose' => 'required|string|max:500',
            'business_plan' => 'nullable|string|max:1000',
            'collateral_description' => 'nullable|string|max:500',
            'guarantor_name' => 'nullable|string|max:255',
            'guarantor_phone' => 'nullable|string|max:15',
            'guarantor_address' => 'nullable|string|max:500',
        ], [
            'member_id.required' => 'Please select a member.',
            'requested_amount.required' => 'Loan amount is required.',
            'requested_amount.min' => 'Minimum loan amount is ৳1,000.',
            'requested_amount.max' => 'Maximum loan amount is ৳5,00,000.',
            'purpose.required' => 'Loan purpose is required.',
        ]);

        try {
            DB::beginTransaction();

            // Verify member belongs to this field officer
            $member = Member::where('id', $validated['member_id'])
                ->where('created_by', $user->id)
                ->first();

            if (!$member) {
                return back()->with('error', 'Invalid member selection.')->withInput();
            }

            // Check if member has pending application
            $pendingApplication = $member->loanApplications()->where('status', 'pending')->first();
            if ($pendingApplication) {
                return back()->with('error', 'This member already has a pending loan application.')->withInput();
            }

            // Calculate loan cycle number
            $loanCycleNumber = $member->loanApplications()->where('status', 'approved')->count() + 1;

            $application = LoanApplication::create([
                'member_id' => $validated['member_id'],
                'requested_amount' => $validated['requested_amount'],
                'purpose' => $validated['purpose'],
                'business_plan' => $validated['business_plan'],
                'collateral_description' => $validated['collateral_description'],
                'guarantor_name' => $validated['guarantor_name'],
                'guarantor_phone' => $validated['guarantor_phone'],
                'guarantor_address' => $validated['guarantor_address'],
                'loan_cycle_number' => $loanCycleNumber,
                'application_date' => now(),
                'status' => 'pending',
                'submitted_by' => $user->id,
            ]);

            $this->logActivity(
                'loan_application_submitted',
                "Loan application submitted for member {$member->name} - Amount: ৳{$validated['requested_amount']}",
                $member,
                ['application_id' => $application->id]
            );

            DB::commit();

            return redirect()->route('field-officer.loan-applications.index')
                ->with('success', 'Loan application submitted successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Loan application submission failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to submit loan application. Please try again.')->withInput();
        }
    }

    /**
     * Installment collection handling
     */
    public function installmentCollection(Request $request)
    {
        $user = auth()->user();

        $query = Installment::with(['loan.loanApplication.member'])
            ->whereHas('loan.loanApplication.member', function ($q) use ($user) {
                $q->where('created_by', $user->id);
            });

        // Apply filters
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        if ($request->filled('member_id')) {
            $query->whereHas('loan.loanApplication', function ($q) use ($request) {
                $q->where('member_id', $request->member_id);
            });
        }

        if ($request->filled('date_from')) {
            $query->where('installment_date', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->where('installment_date', '<=', $request->date_to);
        }

        // Default to pending installments
        if (!$request->filled('status')) {
            $query->where('status', 'pending');
        }

        $installments = $query->orderBy('installment_date')->paginate(25);
        $members = Member::where('created_by', $user->id)->where('status', 'active')->get();

        return view('field-officer.installments.index', compact('installments', 'members'));
    }

    public function collectInstallment(Request $request, Installment $installment)
    {
        $user = auth()->user();

        // Verify access
        if ($installment->loan->loanApplication->member->created_by !== $user->id) {
            abort(403, 'Unauthorized access to this installment.');
        }

        if ($installment->status === 'paid') {
            return back()->with('error', 'This installment has already been collected.');
        }

        $validated = $request->validate([
            'collection_amount' => 'required|numeric|min:0',
            'late_fee' => 'nullable|numeric|min:0',
            'collection_date' => 'required|date|before_or_equal:today',
            'payment_method' => 'required|in:cash,bank_transfer,mobile_banking',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $lateFee = $validated['late_fee'] ?? $installment->calculateLateFee();
            $totalDue = $installment->amount + $lateFee;

            if ($validated['collection_amount'] < $totalDue) {
                return back()->with('error', "Insufficient payment. Total due: ৳{$totalDue}")->withInput();
            }

            $installment->update([
                'status' => 'paid',
                'collection_date' => $validated['collection_date'],
                'collected_by' => $user->id,
                'late_fee' => $lateFee,
                'payment_method' => $validated['payment_method'],
                'notes' => $validated['notes'],
            ]);

            $this->logActivity(
                'installment_collected',
                "Installment #{$installment->installment_no} collected for member {$installment->loan->loanApplication->member->name}",
                $installment->loan->loanApplication->member,
                [
                    'installment_id' => $installment->id,
                    'amount' => $validated['collection_amount'],
                    'late_fee' => $lateFee,
                ]
            );

            DB::commit();

            return redirect()->route('field-officer.installments.index')
                ->with('success', 'Installment collected successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Installment collection failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to collect installment. Please try again.')->withInput();
        }
    }

    /**
     * Performance tracking
     */
    public function performance(Request $request)
    {
        $user = auth()->user();
        $period = $request->get('period', 'monthly');

        $performanceData = [
            'personal_metrics' => $this->getDetailedPersonalMetrics($user, $period),
            'targets' => $this->getPerformanceTargets($user),
            'achievements' => $this->getAchievements($user, $period),
            'trends' => $this->getPerformanceTrends($user, $period),
            'ranking' => $this->getBranchRanking($user),
        ];

        return view('field-officer.performance', compact('performanceData', 'period'));
    }

    /**
     * Helper methods for business logic
     */
    private function getPersonalMetrics(User $user): array
    {
        $currentMonth = now()->startOfMonth();

        return [
            'total_members' => Member::where('created_by', $user->id)->count(),
            'active_members' => Member::where('created_by', $user->id)->where('status', 'active')->count(),
            'monthly_new_members' => Member::where('created_by', $user->id)
                ->where('created_at', '>=', $currentMonth)->count(),
            'total_loans' => $this->getTotalLoans($user),
            'active_loans' => $this->getActiveLoans($user),
            'monthly_disbursements' => $this->getMonthlyDisbursements($user, $currentMonth),
            'monthly_collections' => $this->getMonthlyCollections($user, $currentMonth),
            'collection_rate' => $this->getCollectionRate($user),
            'overdue_amount' => $this->getOverdueAmount($user),
        ];
    }

    private function getMemberStats(User $user): array
    {
        return [
            'total_members' => Member::where('created_by', $user->id)->count(),
            'active_members' => Member::where('created_by', $user->id)->where('status', 'active')->count(),
            'inactive_members' => Member::where('created_by', $user->id)->where('status', 'inactive')->count(),
            'new_this_month' => Member::where('created_by', $user->id)
                ->where('created_at', '>=', now()->startOfMonth())->count(),
        ];
    }

    private function getLoanStats(User $user): array
    {
        return [
            'total_applications' => $this->getTotalApplications($user),
            'pending_applications' => $this->getPendingApplications($user),
            'approved_applications' => $this->getApprovedApplications($user),
            'rejected_applications' => $this->getRejectedApplications($user),
            'total_disbursed' => $this->getTotalDisbursed($user),
            'active_loans' => $this->getActiveLoans($user),
        ];
    }

    private function getCollectionStats(User $user): array
    {
        $currentMonth = now()->startOfMonth();

        return [
            'monthly_target' => $this->getMonthlyCollectionTarget($user),
            'monthly_collected' => $this->getMonthlyCollections($user, $currentMonth),
            'pending_collections' => $this->getPendingCollections($user),
            'overdue_collections' => $this->getOverdueCollections($user),
            'collection_rate' => $this->getCollectionRate($user),
        ];
    }

    private function getRecentActivities(User $user, int $limit = 10): \Illuminate\Database\Eloquent\Collection
    {
        return ActivityLog::where('user_id', $user->id)
            ->latest()
            ->take($limit)
            ->get();
    }

    private function getPendingTasks(User $user): array
    {
        return [
            'pending_applications' => $this->getPendingApplications($user),
            'overdue_installments' => $this->getOverdueInstallmentsCount($user),
            'follow_up_members' => $this->getFollowUpMembersCount($user),
            'documentation_pending' => $this->getDocumentationPendingCount($user),
        ];
    }

    private function getPerformanceTargets(User $user): array
    {
        // These would typically come from a targets table or configuration
        return [
            'monthly_new_members' => 15,
            'monthly_disbursements' => 300000,
            'collection_rate' => 95,
            'monthly_collections' => 250000,
        ];
    }

    private function getPersonalAlerts(User $user): array
    {
        $alerts = [];

        // Low collection rate
        $collectionRate = $this->getCollectionRate($user);
        if ($collectionRate < 90) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Low Collection Rate',
                'message' => "Your collection rate is {$collectionRate}%. Target is 95%.",
            ];
        }

        // Overdue installments
        $overdueCount = $this->getOverdueInstallmentsCount($user);
        if ($overdueCount > 10) {
            $alerts[] = [
                'type' => 'danger',
                'title' => 'High Overdue Count',
                'message' => "You have {$overdueCount} overdue installments to collect.",
            ];
        }

        // Monthly target
        $monthlyTarget = $this->getMonthlyCollectionTarget($user);
        $monthlyCollected = $this->getMonthlyCollections($user, now()->startOfMonth());
        $achievementRate = $monthlyTarget > 0 ? ($monthlyCollected / $monthlyTarget) * 100 : 0;

        if ($achievementRate < 80 && now()->day > 20) {
            $alerts[] = [
                'type' => 'info',
                'title' => 'Monthly Target',
                'message' => "You've achieved {$achievementRate}% of your monthly collection target.",
            ];
        }

        return $alerts;
    }

    // Additional helper methods for calculations
    private function getTotalLoans(User $user): int
    {
        return Loan::whereHas('loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })->count();
    }

    private function getActiveLoans(User $user): int
    {
        return Loan::whereHas('loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })->active()->count();
    }

    private function getMonthlyDisbursements(User $user, Carbon $month): float
    {
        return Loan::whereHas('loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('loan_date', '>=', $month)
        ->where('loan_date', '<', $month->copy()->addMonth())
        ->sum('loan_amount');
    }

    private function getMonthlyCollections(User $user, Carbon $month): float
    {
        return Installment::whereHas('loan.loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('status', 'paid')
        ->where('collection_date', '>=', $month)
        ->where('collection_date', '<', $month->copy()->addMonth())
        ->sum('amount');
    }

    private function getCollectionRate(User $user): float
    {
        $totalDue = Installment::whereHas('loan.loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('installment_date', '<=', now())
        ->sum('amount');

        if ($totalDue === 0) return 100;

        $totalCollected = Installment::whereHas('loan.loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('status', 'paid')
        ->sum('amount');

        return round(($totalCollected / $totalDue) * 100, 2);
    }

    private function getOverdueAmount(User $user): float
    {
        return Installment::whereHas('loan.loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', now())
        ->sum('amount');
    }

    private function getOverdueInstallmentsCount(User $user): int
    {
        return Installment::whereHas('loan.loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', now())
        ->count();
    }

    private function getMonthlyCollectionTarget(User $user): float
    {
        // This would typically come from a targets table
        return 250000; // Default target
    }

    private function getTotalApplications(User $user): int
    {
        return LoanApplication::whereHas('member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })->count();
    }

    private function getPendingApplications(User $user): int
    {
        return LoanApplication::whereHas('member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })->where('status', 'pending')->count();
    }

    private function getApprovedApplications(User $user): int
    {
        return LoanApplication::whereHas('member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })->where('status', 'approved')->count();
    }

    private function getRejectedApplications(User $user): int
    {
        return LoanApplication::whereHas('member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })->where('status', 'rejected')->count();
    }

    private function getTotalDisbursed(User $user): float
    {
        return Loan::whereHas('loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })->sum('loan_amount');
    }

    private function getPendingCollections(User $user): float
    {
        return Installment::whereHas('loan.loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('status', 'pending')
        ->sum('amount');
    }

    private function getOverdueCollections(User $user): float
    {
        return Installment::whereHas('loan.loanApplication.member', function ($q) use ($user) {
            $q->where('created_by', $user->id);
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', now())
        ->sum('amount');
    }

    private function getFollowUpMembersCount(User $user): int
    {
        // Members with overdue payments need follow-up
        return Member::where('created_by', $user->id)
            ->whereHas('loanApplications.loan.installments', function ($q) {
                $q->where('status', 'pending')
                  ->where('installment_date', '<', now()->subDays(7));
            })
            ->count();
    }

    private function getDocumentationPendingCount(User $user): int
    {
        // This would check for incomplete documentation
        return Member::where('created_by', $user->id)
            ->where(function ($q) {
                $q->whereNull('photo')
                  ->orWhereNull('nid_number');
            })
            ->count();
    }

    /**
     * Log activity
     */
    private function logActivity(string $action, string $description, ?Member $member = null, array $metadata = []): void
    {
        try {
            ActivityLog::create([
                'user_id' => auth()->id(),
                'action' => $action,
                'description' => $description,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'metadata' => array_merge($metadata, [
                    'timestamp' => now()->toISOString(),
                    'branch_id' => auth()->user()->branch_id,
                    'member_id' => $member?->id,
                ]),
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }
}
