<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Branch;
use App\Models\BranchTransaction;
use App\Models\SavingAccount;
use App\Models\Installment;
use App\Models\User;
use Carbon\Carbon;

class ManagerDashboardController extends Controller
{
    public function index()
    {
        $user = auth()->user();

        // Get branches managed by this user
        $managedBranches = $user->managedBranches;
        $branchIds = $managedBranches->pluck('id');

        // Basic Statistics
        $totalMembers = Member::whereIn('branch_id', $branchIds)->count();
        $activeLoans = Loan::whereIn('branch_id', $branchIds)->where('status', 'active')->count();
        $pendingApplications = LoanApplication::whereHas('member', function($query) use ($branchIds) {
            $query->whereIn('branch_id', $branchIds);
        })->where('status', 'pending')->count();

        $totalSavingsBalance = SavingAccount::whereHas('member', function($query) use ($branchIds) {
            $query->whereIn('branch_id', $branchIds);
        })->sum('current_balance');

        // Field Officers in managed branches
        $fieldOfficers = User::whereIn('branch_id', $branchIds)
            ->where('role', 'field_officer')
            ->where('is_active', true)
            ->get();

        // Monthly Performance
        $currentMonth = Carbon::now();
        $monthlyDisbursements = Loan::whereIn('branch_id', $branchIds)
            ->where('status', 'active')
            ->whereMonth('disbursement_date', $currentMonth->month)
            ->whereYear('disbursement_date', $currentMonth->year)
            ->sum('loan_amount');

        $monthlyCollections = Installment::whereHas('loan', function($query) use ($branchIds) {
            $query->whereIn('branch_id', $branchIds);
        })->where('status', 'paid')
        ->whereMonth('collection_date', $currentMonth->month)
        ->whereYear('collection_date', $currentMonth->year)
        ->sum('amount');

        // Overdue Loans
        $overdueLoans = Loan::whereIn('branch_id', $branchIds)
            ->where('status', 'active')
            ->whereHas('installments', function($query) {
                $query->where('status', 'pending')
                      ->where('installment_date', '<', Carbon::now());
            })->count();

        // Recent Activities
        $recentApplications = LoanApplication::whereHas('member', function($query) use ($branchIds) {
            $query->whereIn('branch_id', $branchIds);
        })->where('status', 'pending')
        ->with(['member', 'member.branch'])
        ->latest('applied_at')
        ->take(5)
        ->get();

        $recentCollections = Installment::whereHas('loan', function($query) use ($branchIds) {
            $query->whereIn('branch_id', $branchIds);
        })->where('status', 'paid')
        ->with(['loan.member', 'collectedBy'])
        ->latest('collection_date')
        ->take(5)
        ->get();

        // Branch Financial Summary
        $monthlyIncome = BranchTransaction::whereIn('branch_id', $branchIds)
            ->where('transaction_type', 'income')
            ->whereMonth('transaction_date', $currentMonth->month)
            ->whereYear('transaction_date', $currentMonth->year)
            ->sum('amount');

        $monthlyExpense = BranchTransaction::whereIn('branch_id', $branchIds)
            ->where('transaction_type', 'expense')
            ->whereMonth('transaction_date', $currentMonth->month)
            ->whereYear('transaction_date', $currentMonth->year)
            ->sum('amount');

        $stats = [
            'total_members' => $totalMembers,
            'active_loans' => $activeLoans,
            'pending_applications' => $pendingApplications,
            'total_savings_balance' => $totalSavingsBalance,
            'total_branches' => $managedBranches->count(),
            'field_officers_count' => $fieldOfficers->count(),
            'monthly_disbursements' => $monthlyDisbursements,
            'monthly_collections' => $monthlyCollections,
            'overdue_loans' => $overdueLoans,
            'monthly_income' => $monthlyIncome,
            'monthly_expense' => $monthlyExpense,
            'net_income' => $monthlyIncome - $monthlyExpense,
            'recent_applications' => $recentApplications,
            'recent_collections' => $recentCollections,
            'field_officers' => $fieldOfficers,
            'managed_branches' => $managedBranches,
        ];

        return view('manager.dashboard', compact('stats', 'user'));
    }
}
