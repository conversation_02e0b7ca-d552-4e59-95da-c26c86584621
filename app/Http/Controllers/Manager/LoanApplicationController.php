<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\LoanApplication;
use App\Models\Member;
use App\Models\Loan;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class LoanApplicationController extends Controller
{
    /**
     * Display a listing of loan applications for manager's branches
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $query = LoanApplication::whereHas('member', function($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        })->with(['member', 'member.branch', 'reviewedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->whereHas('member', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('applied_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('applied_at', '<=', $request->date_to);
        }

        $applications = $query->latest('applied_at')->paginate(15);
        $branches = $user->managedBranches;

        return view('manager.loan-applications.index', compact('applications', 'branches'));
    }

    /**
     * Display the specified loan application for review
     */
    public function show(LoanApplication $loanApplication)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this application
        if (!$branchIds->contains($loanApplication->member->branch_id)) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        $loanApplication->load(['member', 'member.branch', 'member.loanApplications', 'reviewedBy']);
        
        // Calculate loan details
        $repaymentAmount = $loanApplication->calculateRepaymentAmount();
        $totalRepayment = $loanApplication->calculateTotalRepaymentAmount();
        $totalInterest = $loanApplication->calculateTotalInterest();

        return view('manager.loan-applications.show', compact(
            'loanApplication', 
            'repaymentAmount', 
            'totalRepayment', 
            'totalInterest'
        ));
    }

    /**
     * Approve a loan application
     */
    public function approve(Request $request, LoanApplication $loanApplication)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this application
        if (!$branchIds->contains($loanApplication->member->branch_id)) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        $request->validate([
            'loan_amount' => 'required|numeric|min:1000|max:' . $loanApplication->applied_amount,
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,monthly',
            'disbursement_date' => 'required|date|after_or_equal:today',
            'comments' => 'nullable|string|max:1000',
        ]);

        try {
            DB::beginTransaction();

            // Approve the application and create loan
            $loan = $loanApplication->approve($user, [
                'loan_amount' => $request->loan_amount,
                'interest_rate' => $request->interest_rate,
                'loan_duration_months' => $request->loan_duration_months,
                'repayment_method' => $request->repayment_method,
                'disbursement_date' => $request->disbursement_date,
                'comments' => $request->comments,
            ]);

            DB::commit();

            return redirect()->route('manager.loan-applications.index')
                ->with('success', 'Loan application approved successfully. Loan ID: ' . $loan->loan_id);

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to approve loan application: ' . $e->getMessage());
        }
    }

    /**
     * Reject a loan application
     */
    public function reject(Request $request, LoanApplication $loanApplication)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this application
        if (!$branchIds->contains($loanApplication->member->branch_id)) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        try {
            $loanApplication->reject($user, $request->rejection_reason);

            return redirect()->route('manager.loan-applications.index')
                ->with('success', 'Loan application rejected successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to reject loan application: ' . $e->getMessage());
        }
    }

    /**
     * Show loan calculator
     */
    public function calculator()
    {
        return view('manager.loan-applications.calculator');
    }

    /**
     * Calculate loan repayment details (AJAX)
     */
    public function calculateRepayment(Request $request)
    {
        $request->validate([
            'loan_amount' => 'required|numeric|min:1000',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,monthly',
        ]);

        $loanAmount = $request->loan_amount;
        $interestRate = $request->interest_rate;
        $durationMonths = $request->loan_duration_months;
        $repaymentMethod = $request->repayment_method;

        // Calculate based on repayment method
        if ($repaymentMethod === 'weekly') {
            $totalInstallments = $durationMonths * 4; // Approximate weeks in months
            $monthlyInterest = ($loanAmount * $interestRate / 100) / 12;
            $totalInterest = $monthlyInterest * $durationMonths;
            $totalRepayment = $loanAmount + $totalInterest;
            $installmentAmount = $totalRepayment / $totalInstallments;
        } else {
            $totalInstallments = $durationMonths;
            $monthlyInterest = ($loanAmount * $interestRate / 100) / 12;
            $totalInterest = $monthlyInterest * $durationMonths;
            $totalRepayment = $loanAmount + $totalInterest;
            $installmentAmount = $totalRepayment / $totalInstallments;
        }

        return response()->json([
            'loan_amount' => number_format($loanAmount, 2),
            'total_interest' => number_format($totalInterest, 2),
            'total_repayment' => number_format($totalRepayment, 2),
            'installment_amount' => number_format($installmentAmount, 2),
            'total_installments' => $totalInstallments,
            'repayment_method' => $repaymentMethod,
        ]);
    }

    /**
     * Get pending applications count for dashboard
     */
    public function getPendingCount()
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $count = LoanApplication::whereHas('member', function($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        })->where('status', 'pending')->count();

        return response()->json(['count' => $count]);
    }
}
