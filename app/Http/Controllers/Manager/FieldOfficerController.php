<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\LoanApplication;
use Carbon\Carbon;

class FieldOfficerController extends Controller
{
    /**
     * Display field officers in manager's branches
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $query = User::whereIn('branch_id', $branchIds)
            ->where('role', 'field_officer')
            ->with(['branch']);

        // Filter by status
        if ($request->filled('status')) {
            $isActive = $request->status === 'active';
            $query->where('is_active', $isActive);
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        $fieldOfficers = $query->get();
        $branches = $user->managedBranches;

        // Calculate performance metrics for each field officer
        $currentMonth = Carbon::now();
        foreach ($fieldOfficers as $officer) {
            // Members created by this officer
            $officer->members_count = Member::where('created_by', $officer->id)->count();
            
            // Monthly collections
            $officer->monthly_collections = Installment::where('collected_by', $officer->id)
                ->where('status', 'paid')
                ->whereMonth('collection_date', $currentMonth->month)
                ->whereYear('collection_date', $currentMonth->year)
                ->sum('amount');

            // Total collections
            $officer->total_collections = Installment::where('collected_by', $officer->id)
                ->where('status', 'paid')
                ->sum('amount');

            // Active loans under this officer's members
            $memberIds = Member::where('created_by', $officer->id)->pluck('id');
            $officer->active_loans_count = Loan::whereIn('member_id', $memberIds)
                ->where('status', 'active')
                ->count();

            // Overdue collections
            $officer->overdue_collections = Installment::whereHas('loan', function($query) use ($memberIds) {
                $query->whereIn('member_id', $memberIds);
            })->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now())
            ->count();

            // Collection rate (percentage of on-time collections)
            $totalDueInstallments = Installment::whereHas('loan', function($query) use ($memberIds) {
                $query->whereIn('member_id', $memberIds);
            })->where('installment_date', '<=', Carbon::now())->count();

            $paidOnTimeInstallments = Installment::whereHas('loan', function($query) use ($memberIds) {
                $query->whereIn('member_id', $memberIds);
            })->where('status', 'paid')
            ->whereRaw('collection_date <= installment_date')
            ->count();

            $officer->collection_rate = $totalDueInstallments > 0 
                ? round(($paidOnTimeInstallments / $totalDueInstallments) * 100, 2) 
                : 0;
        }

        return view('manager.field-officers.index', compact('fieldOfficers', 'branches'));
    }

    /**
     * Display detailed performance of a specific field officer
     */
    public function show(User $fieldOfficer)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this field officer
        if (!$branchIds->contains($fieldOfficer->branch_id) || $fieldOfficer->role !== 'field_officer') {
            abort(403, 'Unauthorized access to this field officer.');
        }

        $fieldOfficer->load(['branch']);

        // Get members created by this field officer
        $members = Member::where('created_by', $fieldOfficer->id)
            ->with(['branch', 'loans'])
            ->get();

        // Performance metrics
        $currentMonth = Carbon::now();
        $memberIds = $members->pluck('id');

        $performance = [
            'total_members' => $members->count(),
            'active_members' => $members->whereNotNull('id')->count(), // All members are considered active
            'total_loans' => Loan::whereIn('member_id', $memberIds)->count(),
            'active_loans' => Loan::whereIn('member_id', $memberIds)->where('status', 'active')->count(),
            'completed_loans' => Loan::whereIn('member_id', $memberIds)->where('status', 'completed')->count(),
            'overdue_loans' => Loan::whereIn('member_id', $memberIds)->where('status', 'active')
                ->whereHas('installments', function($query) {
                    $query->where('status', 'pending')
                          ->where('installment_date', '<', Carbon::now());
                })->count(),
        ];

        // Collection performance
        $collections = [
            'monthly_collections' => Installment::where('collected_by', $fieldOfficer->id)
                ->where('status', 'paid')
                ->whereMonth('collection_date', $currentMonth->month)
                ->whereYear('collection_date', $currentMonth->year)
                ->sum('amount'),
            'total_collections' => Installment::where('collected_by', $fieldOfficer->id)
                ->where('status', 'paid')
                ->sum('amount'),
            'pending_collections' => Installment::whereHas('loan', function($query) use ($memberIds) {
                $query->whereIn('member_id', $memberIds);
            })->where('status', 'pending')
            ->where('installment_date', '<=', Carbon::now())
            ->sum('amount'),
        ];

        // Recent activities
        $recentCollections = Installment::where('collected_by', $fieldOfficer->id)
            ->where('status', 'paid')
            ->with(['loan.member'])
            ->latest('collection_date')
            ->take(10)
            ->get();

        $recentMembers = Member::where('created_by', $fieldOfficer->id)
            ->with(['branch'])
            ->latest('created_at')
            ->take(10)
            ->get();

        // Monthly performance chart data (last 6 months)
        $monthlyData = [];
        for ($i = 5; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $monthlyCollections = Installment::where('collected_by', $fieldOfficer->id)
                ->where('status', 'paid')
                ->whereMonth('collection_date', $month->month)
                ->whereYear('collection_date', $month->year)
                ->sum('amount');
            
            $monthlyData[] = [
                'month' => $month->format('M Y'),
                'collections' => $monthlyCollections,
            ];
        }

        return view('manager.field-officers.show', compact(
            'fieldOfficer', 
            'members', 
            'performance', 
            'collections', 
            'recentCollections', 
            'recentMembers',
            'monthlyData'
        ));
    }

    /**
     * Show performance comparison between field officers
     */
    public function comparison(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $fieldOfficers = User::whereIn('branch_id', $branchIds)
            ->where('role', 'field_officer')
            ->where('is_active', true)
            ->with(['branch'])
            ->get();

        $period = $request->get('period', 'current_month');
        
        // Calculate date range based on period
        switch ($period) {
            case 'last_month':
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                break;
            case 'last_3_months':
                $startDate = Carbon::now()->subMonths(3)->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;
            case 'current_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;
            default: // current_month
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
        }

        // Calculate performance metrics for each officer
        foreach ($fieldOfficers as $officer) {
            $memberIds = Member::where('created_by', $officer->id)->pluck('id');

            // Collections in period
            $officer->period_collections = Installment::where('collected_by', $officer->id)
                ->where('status', 'paid')
                ->whereBetween('collection_date', [$startDate, $endDate])
                ->sum('amount');

            // Members added in period
            $officer->period_members = Member::where('created_by', $officer->id)
                ->whereBetween('created_at', [$startDate, $endDate])
                ->count();

            // Loans disbursed in period (for members under this officer)
            $officer->period_loans = Loan::whereIn('member_id', $memberIds)
                ->whereBetween('disbursement_date', [$startDate, $endDate])
                ->count();

            // Collection efficiency
            $totalDue = Installment::whereHas('loan', function($query) use ($memberIds) {
                $query->whereIn('member_id', $memberIds);
            })->where('installment_date', '<=', $endDate)
            ->whereBetween('installment_date', [$startDate, $endDate])
            ->sum('amount');

            $totalCollected = Installment::whereHas('loan', function($query) use ($memberIds) {
                $query->whereIn('member_id', $memberIds);
            })->where('status', 'paid')
            ->whereBetween('collection_date', [$startDate, $endDate])
            ->sum('amount');

            $officer->collection_efficiency = $totalDue > 0 
                ? round(($totalCollected / $totalDue) * 100, 2) 
                : 0;
        }

        // Sort by collections for ranking
        $fieldOfficers = $fieldOfficers->sortByDesc('period_collections')->values();

        return view('manager.field-officers.comparison', compact('fieldOfficers', 'period', 'startDate', 'endDate'));
    }

    /**
     * Assign targets to field officers
     */
    public function assignTargets(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        if ($request->isMethod('post')) {
            $request->validate([
                'targets' => 'required|array',
                'targets.*.officer_id' => 'required|exists:users,id',
                'targets.*.monthly_collection_target' => 'required|numeric|min:0',
                'targets.*.monthly_member_target' => 'required|integer|min:0',
                'targets.*.month' => 'required|date_format:Y-m',
            ]);

            // Here you would save targets to a targets table
            // For now, we'll just return success
            return back()->with('success', 'Targets assigned successfully.');
        }

        $fieldOfficers = User::whereIn('branch_id', $branchIds)
            ->where('role', 'field_officer')
            ->where('is_active', true)
            ->with(['branch'])
            ->get();

        return view('manager.field-officers.assign-targets', compact('fieldOfficers'));
    }

    /**
     * Get field officer performance data for AJAX
     */
    public function getPerformanceData(User $fieldOfficer)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check access
        if (!$branchIds->contains($fieldOfficer->branch_id) || $fieldOfficer->role !== 'field_officer') {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $memberIds = Member::where('created_by', $fieldOfficer->id)->pluck('id');
        $currentMonth = Carbon::now();

        $data = [
            'monthly_collections' => Installment::where('collected_by', $fieldOfficer->id)
                ->where('status', 'paid')
                ->whereMonth('collection_date', $currentMonth->month)
                ->whereYear('collection_date', $currentMonth->year)
                ->sum('amount'),
            'active_loans' => Loan::whereIn('member_id', $memberIds)->where('status', 'active')->count(),
            'overdue_collections' => Installment::whereHas('loan', function($query) use ($memberIds) {
                $query->whereIn('member_id', $memberIds);
            })->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now())
            ->count(),
        ];

        return response()->json($data);
    }
}
