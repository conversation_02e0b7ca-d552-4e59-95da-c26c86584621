<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\SavingAccount;
use App\Models\Installment;
use Carbon\Carbon;

class MemberController extends Controller
{
    /**
     * Display members in manager's branches
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $query = Member::whereIn('branch_id', $branchIds)
            ->with(['branch', 'createdBy']);

        // Search by name or member ID
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by loan status
        if ($request->filled('loan_status')) {
            switch ($request->loan_status) {
                case 'active':
                    $query->whereHas('loans', function($q) {
                        $q->where('status', 'active');
                    });
                    break;
                case 'no_loan':
                    $query->whereDoesntHave('loans', function($q) {
                        $q->where('status', 'active');
                    });
                    break;
                case 'overdue':
                    $query->whereHas('loans', function($q) {
                        $q->where('status', 'active')
                          ->whereHas('installments', function($subQ) {
                              $subQ->where('status', 'pending')
                                   ->where('installment_date', '<', Carbon::now());
                          });
                    });
                    break;
            }
        }

        $members = $query->latest('created_at')->paginate(15);
        $branches = $user->managedBranches;

        // Calculate summary statistics
        $totalMembers = Member::whereIn('branch_id', $branchIds)->count();
        $activeLoans = Loan::whereHas('member', function($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        })->where('status', 'active')->count();
        
        $overdueMembers = Member::whereIn('branch_id', $branchIds)
            ->whereHas('loans', function($q) {
                $q->where('status', 'active')
                  ->whereHas('installments', function($subQ) {
                      $subQ->where('status', 'pending')
                           ->where('installment_date', '<', Carbon::now());
                  });
            })->count();

        $summary = [
            'total_members' => $totalMembers,
            'active_loans' => $activeLoans,
            'overdue_members' => $overdueMembers,
            'no_loan_members' => $totalMembers - $activeLoans,
        ];

        return view('manager.members.index', compact('members', 'branches', 'summary'));
    }

    /**
     * Display the specified member
     */
    public function show(Member $member)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this member
        if (!$branchIds->contains($member->branch_id)) {
            abort(403, 'Unauthorized access to this member.');
        }

        $member->load(['branch', 'createdBy', 'reference']);

        // Get member's loan history
        $loans = Loan::where('member_id', $member->id)
            ->with(['loanApplication', 'installments'])
            ->latest('disbursement_date')
            ->get();

        // Get member's savings accounts
        $savingAccounts = SavingAccount::where('member_id', $member->id)
            ->with(['createdBy'])
            ->latest('created_at')
            ->get();

        // Get member's loan applications
        $loanApplications = LoanApplication::where('member_id', $member->id)
            ->with(['reviewedBy'])
            ->latest('applied_at')
            ->get();

        // Calculate member statistics
        $stats = [
            'total_loans' => $loans->count(),
            'active_loans' => $loans->where('status', 'active')->count(),
            'completed_loans' => $loans->where('status', 'completed')->count(),
            'total_loan_amount' => $loans->sum('loan_amount'),
            'total_paid_amount' => $loans->sum(function($loan) {
                return $loan->installments->where('status', 'paid')->sum('amount');
            }),
            'total_outstanding' => $loans->where('status', 'active')->sum(function($loan) {
                return $loan->installments->where('status', 'pending')->sum('amount');
            }),
            'total_savings_balance' => $savingAccounts->sum('current_balance'),
            'pending_applications' => $loanApplications->where('status', 'pending')->count(),
        ];

        // Get recent installment payments
        $recentPayments = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })->where('status', 'paid')
        ->with(['loan', 'collectedBy'])
        ->latest('collection_date')
        ->take(10)
        ->get();

        // Get upcoming installments
        $upcomingInstallments = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id)->where('status', 'active');
        })->where('status', 'pending')
        ->where('installment_date', '>=', Carbon::now())
        ->with(['loan'])
        ->orderBy('installment_date')
        ->take(5)
        ->get();

        // Get overdue installments
        $overdueInstallments = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id)->where('status', 'active');
        })->where('status', 'pending')
        ->where('installment_date', '<', Carbon::now())
        ->with(['loan'])
        ->orderBy('installment_date')
        ->get();

        return view('manager.members.show', compact(
            'member', 
            'loans', 
            'savingAccounts', 
            'loanApplications', 
            'stats', 
            'recentPayments', 
            'upcomingInstallments', 
            'overdueInstallments'
        ));
    }

    /**
     * Show member's loan history
     */
    public function loans(Member $member)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this member
        if (!$branchIds->contains($member->branch_id)) {
            abort(403, 'Unauthorized access to this member.');
        }

        $loans = Loan::where('member_id', $member->id)
            ->with(['loanApplication', 'installments'])
            ->latest('disbursement_date')
            ->paginate(10);

        return view('manager.members.loans', compact('member', 'loans'));
    }

    /**
     * Show member's savings history
     */
    public function savings(Member $member)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this member
        if (!$branchIds->contains($member->branch_id)) {
            abort(403, 'Unauthorized access to this member.');
        }

        $savingAccounts = SavingAccount::where('member_id', $member->id)
            ->with(['createdBy', 'transactions'])
            ->latest('created_at')
            ->paginate(10);

        return view('manager.members.savings', compact('member', 'savingAccounts'));
    }

    /**
     * Show member analytics
     */
    public function analytics(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $period = $request->get('period', 'current_month');
        
        // Calculate date range
        switch ($period) {
            case 'last_month':
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                break;
            case 'last_3_months':
                $startDate = Carbon::now()->subMonths(3)->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
                break;
            case 'current_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;
            default: // current_month
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
        }

        // Member statistics by branch
        $branchStats = [];
        foreach ($user->managedBranches as $branch) {
            $branchStats[] = [
                'branch_name' => $branch->name,
                'total_members' => Member::where('branch_id', $branch->id)->count(),
                'new_members' => Member::where('branch_id', $branch->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                'active_loans' => Loan::whereHas('member', function($q) use ($branch) {
                    $q->where('branch_id', $branch->id);
                })->where('status', 'active')->count(),
                'total_savings' => SavingAccount::whereHas('member', function($q) use ($branch) {
                    $q->where('branch_id', $branch->id);
                })->sum('current_balance'),
            ];
        }

        // Member growth over time (last 12 months)
        $memberGrowth = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = Carbon::now()->subMonths($i);
            $count = Member::whereIn('branch_id', $branchIds)
                ->whereMonth('created_at', $month->month)
                ->whereYear('created_at', $month->year)
                ->count();
            
            $memberGrowth[] = [
                'month' => $month->format('M Y'),
                'count' => $count,
            ];
        }

        // Loan status distribution
        $loanStatusDistribution = [
            'active' => Loan::whereHas('member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'active')->count(),
            'completed' => Loan::whereHas('member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'completed')->count(),
            'defaulted' => Loan::whereHas('member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'defaulted')->count(),
        ];

        return view('manager.members.analytics', compact(
            'branchStats', 
            'memberGrowth', 
            'loanStatusDistribution', 
            'period', 
            'startDate', 
            'endDate'
        ));
    }

    /**
     * Export member data
     */
    public function export(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $request->validate([
            'format' => 'required|in:pdf,excel',
            'branch_id' => 'nullable|in:' . $branchIds->implode(','),
        ]);

        $query = Member::whereIn('branch_id', $branchIds)
            ->with(['branch', 'createdBy']);

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        $members = $query->get();

        if ($request->format === 'pdf') {
            $pdf = \PDF::loadView('manager.members.export-pdf', compact('members'));
            return $pdf->download('members-export-' . now()->format('Y-m-d') . '.pdf');
        }

        // Excel export would be implemented here
        return back()->with('info', 'Excel export feature coming soon.');
    }

    /**
     * Get member statistics for dashboard
     */
    public function getStatistics()
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $stats = [
            'total_members' => Member::whereIn('branch_id', $branchIds)->count(),
            'new_members_this_month' => Member::whereIn('branch_id', $branchIds)
                ->whereMonth('created_at', Carbon::now()->month)
                ->whereYear('created_at', Carbon::now()->year)
                ->count(),
            'members_with_active_loans' => Member::whereIn('branch_id', $branchIds)
                ->whereHas('loans', function($q) {
                    $q->where('status', 'active');
                })->count(),
            'members_with_overdue_payments' => Member::whereIn('branch_id', $branchIds)
                ->whereHas('loans', function($q) {
                    $q->where('status', 'active')
                      ->whereHas('installments', function($subQ) {
                          $subQ->where('status', 'pending')
                               ->where('installment_date', '<', Carbon::now());
                      });
                })->count(),
        ];

        return response()->json($stats);
    }
}
