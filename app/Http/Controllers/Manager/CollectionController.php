<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Installment;
use App\Models\Loan;
use App\Models\Member;
use App\Models\User;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class CollectionController extends Controller
{
    /**
     * Display collection monitoring dashboard
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Filter parameters
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));
        $branchId = $request->get('branch_id');
        $officerId = $request->get('officer_id');
        $status = $request->get('status', 'all');

        // Base query for installments in manager's branches
        $query = Installment::whereHas('loan.member', function($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        })->with(['loan.member.branch', 'loan.member', 'collectedBy']);

        // Apply filters
        if ($branchId) {
            $query->whereHas('loan.member', function($q) use ($branchId) {
                $q->where('branch_id', $branchId);
            });
        }

        if ($officerId) {
            $query->where('collected_by', $officerId);
        }

        if ($status !== 'all') {
            $query->where('status', $status);
        }

        // Filter by date
        $query->whereDate('installment_date', $date);

        $installments = $query->orderBy('installment_date')->paginate(20);

        // Summary statistics for the selected date
        $summary = [
            'total_due' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->whereDate('installment_date', $date)->sum('amount'),
            
            'total_collected' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->whereDate('installment_date', $date)
            ->where('status', 'paid')->sum('amount'),
            
            'total_pending' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->whereDate('installment_date', $date)
            ->where('status', 'pending')->sum('amount'),
            
            'collection_count' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->whereDate('installment_date', $date)
            ->where('status', 'paid')->count(),
            
            'pending_count' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->whereDate('installment_date', $date)
            ->where('status', 'pending')->count(),
        ];

        $summary['collection_rate'] = $summary['total_due'] > 0 
            ? round(($summary['total_collected'] / $summary['total_due']) * 100, 2) 
            : 0;

        // Get branches and field officers for filters
        $branches = $user->managedBranches;
        $fieldOfficers = User::whereIn('branch_id', $branchIds)
            ->where('role', 'field_officer')
            ->where('is_active', true)
            ->get();

        return view('manager.collections.index', compact(
            'installments', 
            'summary', 
            'branches', 
            'fieldOfficers', 
            'date', 
            'branchId', 
            'officerId', 
            'status'
        ));
    }

    /**
     * Display overdue collections
     */
    public function overdue(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $query = Installment::whereHas('loan.member', function($q) use ($branchIds) {
            $q->whereIn('branch_id', $branchIds);
        })->where('status', 'pending')
        ->where('installment_date', '<', Carbon::now())
        ->with(['loan.member.branch', 'loan.member']);

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->whereHas('loan.member', function($q) use ($request) {
                $q->where('branch_id', $request->branch_id);
            });
        }

        // Filter by days overdue
        if ($request->filled('days_overdue')) {
            $daysAgo = Carbon::now()->subDays($request->days_overdue);
            $query->where('installment_date', '<=', $daysAgo);
        }

        $overdueInstallments = $query->orderBy('installment_date')->paginate(20);

        // Calculate overdue summary
        $overdueSummary = [
            'total_overdue_amount' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now())
            ->sum('amount'),
            
            'total_overdue_count' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now())
            ->count(),
            
            'members_with_overdue' => Member::whereIn('branch_id', $branchIds)
                ->whereHas('loans', function($q) {
                    $q->where('status', 'active')
                      ->whereHas('installments', function($subQ) {
                          $subQ->where('status', 'pending')
                               ->where('installment_date', '<', Carbon::now());
                      });
                })->count(),
        ];

        // Group overdue by days
        $overdueByDays = [
            '1-7' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'pending')
            ->whereBetween('installment_date', [Carbon::now()->subDays(7), Carbon::now()->subDay()])
            ->sum('amount'),
            
            '8-30' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'pending')
            ->whereBetween('installment_date', [Carbon::now()->subDays(30), Carbon::now()->subDays(8)])
            ->sum('amount'),
            
            '31-90' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'pending')
            ->whereBetween('installment_date', [Carbon::now()->subDays(90), Carbon::now()->subDays(31)])
            ->sum('amount'),
            
            '90+' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now()->subDays(90))
            ->sum('amount'),
        ];

        $branches = $user->managedBranches;

        return view('manager.collections.overdue', compact(
            'overdueInstallments', 
            'overdueSummary', 
            'overdueByDays', 
            'branches'
        ));
    }

    /**
     * Display collection reports
     */
    public function reports(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $period = $request->get('period', 'current_month');
        
        // Calculate date range
        switch ($period) {
            case 'today':
                $startDate = Carbon::today();
                $endDate = Carbon::today();
                break;
            case 'yesterday':
                $startDate = Carbon::yesterday();
                $endDate = Carbon::yesterday();
                break;
            case 'last_week':
                $startDate = Carbon::now()->subWeek()->startOfWeek();
                $endDate = Carbon::now()->subWeek()->endOfWeek();
                break;
            case 'last_month':
                $startDate = Carbon::now()->subMonth()->startOfMonth();
                $endDate = Carbon::now()->subMonth()->endOfMonth();
                break;
            case 'current_year':
                $startDate = Carbon::now()->startOfYear();
                $endDate = Carbon::now()->endOfYear();
                break;
            default: // current_month
                $startDate = Carbon::now()->startOfMonth();
                $endDate = Carbon::now()->endOfMonth();
        }

        // Collection statistics for the period
        $collectionStats = [
            'total_due' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->whereBetween('installment_date', [$startDate, $endDate])
            ->sum('amount'),
            
            'total_collected' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'paid')
            ->whereBetween('collection_date', [$startDate, $endDate])
            ->sum('amount'),
            
            'collection_efficiency' => 0,
        ];

        if ($collectionStats['total_due'] > 0) {
            $collectionStats['collection_efficiency'] = round(
                ($collectionStats['total_collected'] / $collectionStats['total_due']) * 100, 2
            );
        }

        // Branch-wise collection performance
        $branchPerformance = [];
        foreach ($user->managedBranches as $branch) {
            $branchDue = Installment::whereHas('loan.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->whereBetween('installment_date', [$startDate, $endDate])
            ->sum('amount');
            
            $branchCollected = Installment::whereHas('loan.member', function($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            })->where('status', 'paid')
            ->whereBetween('collection_date', [$startDate, $endDate])
            ->sum('amount');
            
            $branchPerformance[] = [
                'branch_name' => $branch->name,
                'due_amount' => $branchDue,
                'collected_amount' => $branchCollected,
                'collection_rate' => $branchDue > 0 ? round(($branchCollected / $branchDue) * 100, 2) : 0,
            ];
        }

        // Field officer performance
        $officerPerformance = [];
        $fieldOfficers = User::whereIn('branch_id', $branchIds)
            ->where('role', 'field_officer')
            ->where('is_active', true)
            ->get();

        foreach ($fieldOfficers as $officer) {
            $officerCollections = Installment::where('collected_by', $officer->id)
                ->where('status', 'paid')
                ->whereBetween('collection_date', [$startDate, $endDate])
                ->sum('amount');
            
            $officerPerformance[] = [
                'officer_name' => $officer->name,
                'branch_name' => $officer->branch->name,
                'collections' => $officerCollections,
            ];
        }

        // Sort by collections
        usort($officerPerformance, function($a, $b) {
            return $b['collections'] <=> $a['collections'];
        });

        // Daily collection trend (last 30 days)
        $dailyTrend = [];
        for ($i = 29; $i >= 0; $i--) {
            $date = Carbon::now()->subDays($i);
            $dailyCollection = Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'paid')
            ->whereDate('collection_date', $date)
            ->sum('amount');
            
            $dailyTrend[] = [
                'date' => $date->format('M d'),
                'amount' => $dailyCollection,
            ];
        }

        return view('manager.collections.reports', compact(
            'collectionStats', 
            'branchPerformance', 
            'officerPerformance', 
            'dailyTrend', 
            'period', 
            'startDate', 
            'endDate'
        ));
    }

    /**
     * Export collection report
     */
    public function exportReport(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'format' => 'required|in:pdf,excel',
            'branch_id' => 'nullable|in:' . $branchIds->implode(','),
        ]);

        $query = Installment::whereHas('loan.member', function($q) use ($branchIds, $request) {
            $q->whereIn('branch_id', $branchIds);
            if ($request->filled('branch_id')) {
                $q->where('branch_id', $request->branch_id);
            }
        })->whereBetween('installment_date', [$request->date_from, $request->date_to])
        ->with(['loan.member.branch', 'loan.member', 'collectedBy']);

        $installments = $query->orderBy('installment_date')->get();

        $summary = [
            'total_due' => $installments->sum('amount'),
            'total_collected' => $installments->where('status', 'paid')->sum('amount'),
            'total_pending' => $installments->where('status', 'pending')->sum('amount'),
            'period_from' => $request->date_from,
            'period_to' => $request->date_to,
        ];

        if ($request->format === 'pdf') {
            $pdf = Pdf::loadView('manager.collections.report-pdf', compact('installments', 'summary'));
            return $pdf->download('collection-report-' . $request->date_from . '-to-' . $request->date_to . '.pdf');
        }

        // Excel export would be implemented here
        return back()->with('info', 'Excel export feature coming soon.');
    }

    /**
     * Get collection statistics for AJAX
     */
    public function getStatistics(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');
        
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));

        $stats = [
            'daily_target' => 100000, // This would come from a targets table
            'daily_collected' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'paid')
            ->whereDate('collection_date', $date)
            ->sum('amount'),
            
            'monthly_target' => 3000000, // This would come from a targets table
            'monthly_collected' => Installment::whereHas('loan.member', function($q) use ($branchIds) {
                $q->whereIn('branch_id', $branchIds);
            })->where('status', 'paid')
            ->whereMonth('collection_date', Carbon::parse($date)->month)
            ->whereYear('collection_date', Carbon::parse($date)->year)
            ->sum('amount'),
        ];

        $stats['daily_achievement'] = $stats['daily_target'] > 0 
            ? round(($stats['daily_collected'] / $stats['daily_target']) * 100, 2) 
            : 0;
            
        $stats['monthly_achievement'] = $stats['monthly_target'] > 0 
            ? round(($stats['monthly_collected'] / $stats['monthly_target']) * 100, 2) 
            : 0;

        return response()->json($stats);
    }
}
