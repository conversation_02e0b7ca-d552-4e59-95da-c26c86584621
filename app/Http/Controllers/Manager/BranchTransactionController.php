<?php

namespace App\Http\Controllers\Manager;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\BranchTransaction;
use App\Models\Branch;
use Carbon\Carbon;
use Barryvdh\DomPDF\Facade\Pdf;

class BranchTransactionController extends Controller
{
    /**
     * Display financial transactions for manager's branches
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $query = BranchTransaction::whereIn('branch_id', $branchIds)
            ->with(['branch', 'enteredBy']);

        // Filter by transaction type
        if ($request->filled('transaction_type')) {
            $query->where('transaction_type', $request->transaction_type);
        }

        // Filter by branch
        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        // Filter by category
        if ($request->filled('category')) {
            $query->where('category', $request->category);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('transaction_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('transaction_date', '<=', $request->date_to);
        }

        $transactions = $query->latest('transaction_date')->paginate(15);
        $branches = $user->managedBranches;

        // Calculate totals for current filter
        $totalIncome = BranchTransaction::whereIn('branch_id', $branchIds)
            ->where('transaction_type', 'income');
        $totalExpense = BranchTransaction::whereIn('branch_id', $branchIds)
            ->where('transaction_type', 'expense');

        // Apply same filters to totals
        if ($request->filled('branch_id')) {
            $totalIncome->where('branch_id', $request->branch_id);
            $totalExpense->where('branch_id', $request->branch_id);
        }
        if ($request->filled('date_from')) {
            $totalIncome->whereDate('transaction_date', '>=', $request->date_from);
            $totalExpense->whereDate('transaction_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $totalIncome->whereDate('transaction_date', '<=', $request->date_to);
            $totalExpense->whereDate('transaction_date', '<=', $request->date_to);
        }

        $summary = [
            'total_income' => $totalIncome->sum('amount'),
            'total_expense' => $totalExpense->sum('amount'),
        ];
        $summary['net_income'] = $summary['total_income'] - $summary['total_expense'];

        return view('manager.transactions.index', compact('transactions', 'branches', 'summary'));
    }

    /**
     * Show the form for creating a new transaction
     */
    public function create()
    {
        $user = auth()->user();
        $branches = $user->managedBranches;

        return view('manager.transactions.create', compact('branches'));
    }

    /**
     * Store a newly created transaction
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $request->validate([
            'branch_id' => 'required|in:' . $branchIds->implode(','),
            'transaction_type' => 'required|in:income,expense',
            'category' => 'required|string|max:100',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:500',
            'transaction_date' => 'required|date|before_or_equal:today',
            'receipt_number' => 'nullable|string|max:50',
        ]);

        try {
            BranchTransaction::create([
                'branch_id' => $request->branch_id,
                'transaction_type' => $request->transaction_type,
                'category' => $request->category,
                'amount' => $request->amount,
                'description' => $request->description,
                'transaction_date' => $request->transaction_date,
                'receipt_number' => $request->receipt_number,
                'entered_by' => $user->id,
            ]);

            return redirect()->route('manager.transactions.index')
                ->with('success', 'Transaction recorded successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to record transaction: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified transaction
     */
    public function show(BranchTransaction $transaction)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this transaction
        if (!$branchIds->contains($transaction->branch_id)) {
            abort(403, 'Unauthorized access to this transaction.');
        }

        $transaction->load(['branch', 'enteredBy']);

        return view('manager.transactions.show', compact('transaction'));
    }

    /**
     * Show the form for editing the specified transaction
     */
    public function edit(BranchTransaction $transaction)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this transaction
        if (!$branchIds->contains($transaction->branch_id)) {
            abort(403, 'Unauthorized access to this transaction.');
        }

        $branches = $user->managedBranches;

        return view('manager.transactions.edit', compact('transaction', 'branches'));
    }

    /**
     * Update the specified transaction
     */
    public function update(Request $request, BranchTransaction $transaction)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this transaction
        if (!$branchIds->contains($transaction->branch_id)) {
            abort(403, 'Unauthorized access to this transaction.');
        }

        $request->validate([
            'branch_id' => 'required|in:' . $branchIds->implode(','),
            'transaction_type' => 'required|in:income,expense',
            'category' => 'required|string|max:100',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:500',
            'transaction_date' => 'required|date|before_or_equal:today',
            'receipt_number' => 'nullable|string|max:50',
        ]);

        try {
            $transaction->update([
                'branch_id' => $request->branch_id,
                'transaction_type' => $request->transaction_type,
                'category' => $request->category,
                'amount' => $request->amount,
                'description' => $request->description,
                'transaction_date' => $request->transaction_date,
                'receipt_number' => $request->receipt_number,
            ]);

            return redirect()->route('manager.transactions.index')
                ->with('success', 'Transaction updated successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update transaction: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified transaction
     */
    public function destroy(BranchTransaction $transaction)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        // Check if manager can access this transaction
        if (!$branchIds->contains($transaction->branch_id)) {
            abort(403, 'Unauthorized access to this transaction.');
        }

        try {
            $transaction->delete();

            return redirect()->route('manager.transactions.index')
                ->with('success', 'Transaction deleted successfully.');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete transaction: ' . $e->getMessage());
        }
    }

    /**
     * Generate financial report
     */
    public function report(Request $request)
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');

        $request->validate([
            'date_from' => 'required|date',
            'date_to' => 'required|date|after_or_equal:date_from',
            'branch_id' => 'nullable|in:' . $branchIds->implode(','),
            'format' => 'required|in:pdf,excel',
        ]);

        $query = BranchTransaction::whereIn('branch_id', $branchIds)
            ->whereBetween('transaction_date', [$request->date_from, $request->date_to])
            ->with(['branch', 'enteredBy']);

        if ($request->filled('branch_id')) {
            $query->where('branch_id', $request->branch_id);
        }

        $transactions = $query->orderBy('transaction_date')->get();
        
        $summary = [
            'total_income' => $transactions->where('transaction_type', 'income')->sum('amount'),
            'total_expense' => $transactions->where('transaction_type', 'expense')->sum('amount'),
            'period_from' => $request->date_from,
            'period_to' => $request->date_to,
        ];
        $summary['net_income'] = $summary['total_income'] - $summary['total_expense'];

        if ($request->format === 'pdf') {
            $pdf = Pdf::loadView('manager.transactions.report-pdf', compact('transactions', 'summary'));
            return $pdf->download('financial-report-' . $request->date_from . '-to-' . $request->date_to . '.pdf');
        }

        // Excel export would be implemented here
        return back()->with('info', 'Excel export feature coming soon.');
    }

    /**
     * Get monthly summary for dashboard
     */
    public function getMonthlySummary()
    {
        $user = auth()->user();
        $branchIds = $user->managedBranches->pluck('id');
        $currentMonth = Carbon::now();

        $income = BranchTransaction::whereIn('branch_id', $branchIds)
            ->where('transaction_type', 'income')
            ->whereMonth('transaction_date', $currentMonth->month)
            ->whereYear('transaction_date', $currentMonth->year)
            ->sum('amount');

        $expense = BranchTransaction::whereIn('branch_id', $branchIds)
            ->where('transaction_type', 'expense')
            ->whereMonth('transaction_date', $currentMonth->month)
            ->whereYear('transaction_date', $currentMonth->year)
            ->sum('amount');

        return response()->json([
            'income' => $income,
            'expense' => $expense,
            'net_income' => $income - $expense,
        ]);
    }
}
