<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ReportingService;
use App\Services\PDFReportService;
use App\Services\ExcelExportService;
use App\Models\Branch;
use App\Models\User;
use Carbon\Carbon;

class PerformanceReportController extends Controller
{
    protected $reportingService;
    protected $pdfService;
    protected $excelService;

    public function __construct(
        ReportingService $reportingService,
        PDFReportService $pdfService,
        ExcelExportService $excelService
    ) {
        $this->reportingService = $reportingService;
        $this->pdfService = $pdfService;
        $this->excelService = $excelService;
    }

    /**
     * Display performance reports dashboard
     */
    public function index()
    {
        $user = auth()->user();
        $branches = $this->getBranchesForUser($user);
        $fieldOfficers = $this->getFieldOfficersForUser($user);

        return view('reports.performance.index', compact('branches', 'fieldOfficers'));
    }

    /**
     * Generate field officer performance report
     */
    public function fieldOfficerPerformance(Request $request)
    {
        $request->validate([
            'field_officer_id' => 'nullable|exists:users,id',
            'branch_id' => 'nullable|exists:branches,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        $fieldOfficer = $request->field_officer_id ? User::find($request->field_officer_id) : null;
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check access permissions
        if (!$this->canAccessFieldOfficer($user, $fieldOfficer)) {
            abort(403, 'You do not have permission to access this field officer data.');
        }

        switch ($request->format) {
            case 'pdf':
                if (!$fieldOfficer) {
                    abort(400, 'Field officer must be selected for PDF export.');
                }
                return $this->pdfService->generateFieldOfficerPerformanceReport($fieldOfficer, $startDate, $endDate);
            
            case 'excel':
                return $this->excelService->exportPerformanceReport($fieldOfficer, $startDate, $endDate);
            
            default:
                if ($fieldOfficer) {
                    // Single officer performance
                    $performanceData = $this->reportingService->getFieldOfficerPerformance($fieldOfficer, $startDate, $endDate);
                    
                    return view('reports.performance.field-officer-single', compact(
                        'fieldOfficer', 'startDate', 'endDate', 'performanceData'
                    ));
                } else {
                    // All officers performance comparison
                    $fieldOfficers = $this->getFieldOfficersForUser($user, $branch);
                    $performanceData = [];

                    foreach ($fieldOfficers as $officer) {
                        $performanceData[] = $this->reportingService->getFieldOfficerPerformance($officer, $startDate, $endDate);
                    }

                    return view('reports.performance.field-officer-comparison', compact(
                        'branch', 'startDate', 'endDate', 'performanceData', 'fieldOfficers'
                    ));
                }
        }
    }

    /**
     * Generate branch comparison report
     */
    public function branchComparison(Request $request)
    {
        $request->validate([
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        
        // Only admins can access branch comparison
        if ($user->role !== 'admin') {
            abort(403, 'You do not have permission to access branch comparison data.');
        }

        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);
        $branches = Branch::where('is_active', true)->get();

        $comparisonData = [];
        foreach ($branches as $branch) {
            $financialData = $this->reportingService->getBranchFinancialSummary($branch, $startDate, $endDate);
            $portfolioData = $this->reportingService->getLoanPortfolioAnalysis($branch);
            $memberGrowth = $this->reportingService->getMemberGrowthAnalysis($branch, 12);

            $comparisonData[] = [
                'branch' => $branch,
                'financial' => $financialData,
                'portfolio' => $portfolioData,
                'member_growth' => $memberGrowth
            ];
        }

        switch ($request->format) {
            case 'pdf':
                // Would create a specific PDF for branch comparison
                return response()->json(['message' => 'PDF export for branch comparison not implemented yet'], 501);
            
            case 'excel':
                // Would create a specific Excel export for branch comparison
                return response()->json(['message' => 'Excel export for branch comparison not implemented yet'], 501);
            
            default:
                return view('reports.performance.branch-comparison', compact(
                    'startDate', 'endDate', 'comparisonData', 'branches'
                ));
        }
    }

    /**
     * Generate member growth report
     */
    public function memberGrowth(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'months' => 'nullable|integer|min:3|max:24',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $months = $request->months ?? 12;

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        $memberGrowthData = $this->reportingService->getMemberGrowthAnalysis($branch, $months);

        switch ($request->format) {
            case 'pdf':
                // Would create a specific PDF for member growth
                return response()->json(['message' => 'PDF export for member growth not implemented yet'], 501);
            
            case 'excel':
                return $this->excelService->exportMembers($branch);
            
            default:
                return view('reports.performance.member-growth', compact(
                    'branch', 'months', 'memberGrowthData'
                ));
        }
    }

    /**
     * Get branches accessible to user
     */
    private function getBranchesForUser($user)
    {
        if ($user->role === 'admin') {
            return Branch::where('is_active', true)->get();
        } elseif ($user->role === 'manager') {
            return Branch::where('id', $user->branch_id)->where('is_active', true)->get();
        }
        
        return collect();
    }

    /**
     * Get field officers accessible to user
     */
    private function getFieldOfficersForUser($user, $branch = null)
    {
        $query = User::where('role', 'field_officer')->where('is_active', true);

        if ($user->role === 'admin') {
            if ($branch) {
                $query->where('branch_id', $branch->id);
            }
        } elseif ($user->role === 'manager') {
            $query->where('branch_id', $user->branch_id);
        } else {
            return collect(); // Field officers can't access other officers' data
        }

        return $query->with('branch')->get();
    }

    /**
     * Check if user can access branch data
     */
    private function canAccessBranch($user, $branch)
    {
        if ($user->role === 'admin') {
            return true;
        } elseif ($user->role === 'manager') {
            return !$branch || $branch->id === $user->branch_id;
        }
        
        return false;
    }

    /**
     * Check if user can access field officer data
     */
    private function canAccessFieldOfficer($user, $fieldOfficer)
    {
        if (!$fieldOfficer) {
            return true; // Can access aggregate data
        }

        if ($user->role === 'admin') {
            return true;
        } elseif ($user->role === 'manager') {
            return $fieldOfficer->branch_id === $user->branch_id;
        }
        
        return false;
    }
}
