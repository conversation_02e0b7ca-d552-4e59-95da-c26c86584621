<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ReportingService;
use App\Services\PDFReportService;
use App\Services\ExcelExportService;
use App\Models\Branch;
use Carbon\Carbon;

class FinancialReportController extends Controller
{
    protected $reportingService;
    protected $pdfService;
    protected $excelService;

    public function __construct(
        ReportingService $reportingService,
        PDFReportService $pdfService,
        ExcelExportService $excelService
    ) {
        $this->reportingService = $reportingService;
        $this->pdfService = $pdfService;
        $this->excelService = $excelService;
    }

    /**
     * Display financial reports dashboard
     */
    public function index()
    {
        $user = auth()->user();
        $branches = $this->getBranchesForUser($user);

        return view('reports.financial.index', compact('branches'));
    }

    /**
     * Generate branch financial summary report
     */
    public function branchFinancialSummary(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        $financialData = $this->reportingService->getBranchFinancialSummary($branch, $startDate, $endDate);
        $portfolioData = $this->reportingService->getLoanPortfolioAnalysis($branch);
        $collectionData = $this->reportingService->getCollectionEfficiencyReport($branch, $startDate, $endDate);

        switch ($request->format) {
            case 'pdf':
                return $this->pdfService->generateBranchFinancialReport($branch, $startDate, $endDate);
            
            case 'excel':
                return $this->excelService->exportFinancialReport($branch, $startDate, $endDate);
            
            default:
                return view('reports.financial.branch-summary', compact(
                    'branch', 'startDate', 'endDate', 'financialData', 'portfolioData', 'collectionData'
                ));
        }
    }

    /**
     * Generate loan disbursement report
     */
    public function loanDisbursement(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        switch ($request->format) {
            case 'pdf':
                return $this->pdfService->generateLoanDisbursementReport($branch, $startDate, $endDate);
            
            case 'excel':
                return $this->excelService->exportLoans($branch, $startDate, $endDate);
            
            default:
                $query = \App\Models\Loan::with(['loanApplication.member.branch', 'loanApplication.reviewedBy']);
                
                if ($branch) {
                    $query->where('branch_id', $branch->id);
                }

                $loans = $query->whereBetween('loan_date', [$startDate, $endDate])
                    ->orderBy('loan_date', 'desc')
                    ->paginate(50);

                $summary = [
                    'total_loans' => $loans->total(),
                    'total_amount' => $query->sum('loan_amount'),
                    'average_loan_size' => $query->avg('loan_amount')
                ];

                return view('reports.financial.loan-disbursement', compact(
                    'branch', 'startDate', 'endDate', 'loans', 'summary'
                ));
        }
    }

    /**
     * Generate outstanding loans report
     */
    public function outstandingLoans(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        switch ($request->format) {
            case 'pdf':
                return $this->pdfService->generateOutstandingLoansReport($branch);
            
            case 'excel':
                return $this->excelService->exportLoans($branch, null, null, ['status' => 'active']);
            
            default:
                $query = \App\Models\Loan::with(['loanApplication.member.branch', 'installments'])
                    ->where('status', 'active');
                
                if ($branch) {
                    $query->where('branch_id', $branch->id);
                }

                $loans = $query->orderBy('loan_date', 'desc')->paginate(50);

                // Calculate overdue amounts
                $overdueLoans = $loans->getCollection()->filter(function ($loan) {
                    return $loan->installments->where('status', 'pending')
                        ->where('installment_date', '<', Carbon::now())
                        ->count() > 0;
                });

                $summary = [
                    'total_loans' => $loans->total(),
                    'total_outstanding' => $query->sum('remaining_amount'),
                    'overdue_loans' => $overdueLoans->count(),
                    'overdue_amount' => $overdueLoans->sum('remaining_amount')
                ];

                return view('reports.financial.outstanding-loans', compact(
                    'branch', 'loans', 'summary', 'overdueLoans'
                ));
        }
    }

    /**
     * Generate profit/loss statement
     */
    public function profitLoss(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        switch ($request->format) {
            case 'pdf':
                return $this->pdfService->generateProfitLossStatement($branch, $startDate, $endDate);
            
            case 'excel':
                return $this->excelService->exportFinancialReport($branch, $startDate, $endDate);
            
            default:
                $financialData = $this->reportingService->getBranchFinancialSummary($branch, $startDate, $endDate);

                // Calculate P&L components
                $revenue = [
                    'interest_income' => $financialData['interest_income'],
                    'service_charges' => 0, // Would be calculated from fee transactions
                    'other_income' => 0,
                    'total_revenue' => $financialData['interest_income']
                ];

                $expenses = [
                    'staff_salaries' => 0, // Would be calculated from payroll
                    'office_rent' => 0,
                    'utilities' => 0,
                    'administrative_expenses' => 0,
                    'loan_loss_provision' => 0,
                    'other_expenses' => 0,
                    'total_expenses' => 0
                ];

                $netIncome = $revenue['total_revenue'] - $expenses['total_expenses'];

                return view('reports.financial.profit-loss', compact(
                    'branch', 'startDate', 'endDate', 'revenue', 'expenses', 'netIncome', 'financialData'
                ));
        }
    }

    /**
     * Generate collection efficiency report
     */
    public function collectionEfficiency(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:view,pdf,excel'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        $collectionData = $this->reportingService->getCollectionEfficiencyReport($branch, $startDate, $endDate);

        switch ($request->format) {
            case 'pdf':
                // Would create a specific PDF for collection efficiency
                return $this->pdfService->generateBranchFinancialReport($branch, $startDate, $endDate);
            
            case 'excel':
                return $this->excelService->exportInstallments($branch, $startDate, $endDate);
            
            default:
                return view('reports.financial.collection-efficiency', compact(
                    'branch', 'startDate', 'endDate', 'collectionData'
                ));
        }
    }

    /**
     * Get branches accessible to user
     */
    private function getBranchesForUser($user)
    {
        if ($user->role === 'admin') {
            return Branch::where('is_active', true)->get();
        } elseif ($user->role === 'manager') {
            return Branch::where('id', $user->branch_id)->where('is_active', true)->get();
        } else {
            return collect(); // Field officers don't have access to financial reports
        }
    }

    /**
     * Check if user can access branch data
     */
    private function canAccessBranch($user, $branch)
    {
        if ($user->role === 'admin') {
            return true;
        } elseif ($user->role === 'manager') {
            return !$branch || $branch->id === $user->branch_id;
        }
        
        return false; // Field officers don't have access
    }
}
