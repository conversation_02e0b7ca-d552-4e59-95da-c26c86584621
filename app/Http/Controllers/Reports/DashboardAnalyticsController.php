<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ChartDataService;
use App\Services\ReportingService;
use App\Models\Branch;
use Carbon\Carbon;

class DashboardAnalyticsController extends Controller
{
    protected $chartService;
    protected $reportingService;

    public function __construct(ChartDataService $chartService, ReportingService $reportingService)
    {
        $this->chartService = $chartService;
        $this->reportingService = $reportingService;
    }

    /**
     * Get dashboard analytics data
     */
    public function getDashboardData(Request $request)
    {
        $user = auth()->user();
        $branch = $this->getBranchForUser($user, $request->branch_id);

        $data = [
            'summary_cards' => $this->getSummaryCards($branch),
            'charts' => [
                'loan_disbursement' => $this->chartService->getMonthlyLoanDisbursementData($branch, 12),
                'collection_efficiency' => $this->chartService->getCollectionEfficiencyTrendData($branch, 12),
                'loan_status_distribution' => $this->chartService->getLoanStatusDistributionData($branch),
                'member_growth' => $this->chartService->getMemberGrowthData($branch, 12),
                'portfolio_at_risk' => $this->chartService->getPortfolioAtRiskTrendData($branch, 12),
                'savings_trend' => $this->chartService->getSavingsTrendData($branch, 12)
            ],
            'recent_activities' => $this->getRecentActivities($branch),
            'alerts' => $this->getSystemAlerts($branch)
        ];

        return response()->json($data);
    }

    /**
     * Get branch comparison chart data
     */
    public function getBranchComparisonData(Request $request)
    {
        $user = auth()->user();
        
        // Only admins can access branch comparison
        if ($user->role !== 'admin') {
            abort(403, 'Unauthorized access to branch comparison data.');
        }

        $metrics = $request->input('metrics', ['loans', 'members', 'collections']);
        $data = $this->chartService->getBranchComparisonData($metrics);

        return response()->json($data);
    }

    /**
     * Get loan disbursement trend data
     */
    public function getLoanDisbursementTrend(Request $request)
    {
        $user = auth()->user();
        $branch = $this->getBranchForUser($user, $request->branch_id);
        $months = $request->input('months', 12);

        $data = $this->chartService->getMonthlyLoanDisbursementData($branch, $months);

        return response()->json($data);
    }

    /**
     * Get collection efficiency trend data
     */
    public function getCollectionEfficiencyTrend(Request $request)
    {
        $user = auth()->user();
        $branch = $this->getBranchForUser($user, $request->branch_id);
        $months = $request->input('months', 12);

        $data = $this->chartService->getCollectionEfficiencyTrendData($branch, $months);

        return response()->json($data);
    }

    /**
     * Get member growth trend data
     */
    public function getMemberGrowthTrend(Request $request)
    {
        $user = auth()->user();
        $branch = $this->getBranchForUser($user, $request->branch_id);
        $months = $request->input('months', 12);

        $data = $this->chartService->getMemberGrowthData($branch, $months);

        return response()->json($data);
    }

    /**
     * Get portfolio at risk trend data
     */
    public function getPortfolioAtRiskTrend(Request $request)
    {
        $user = auth()->user();
        $branch = $this->getBranchForUser($user, $request->branch_id);
        $months = $request->input('months', 12);

        $data = $this->chartService->getPortfolioAtRiskTrendData($branch, $months);

        return response()->json($data);
    }

    /**
     * Get savings trend data
     */
    public function getSavingsTrend(Request $request)
    {
        $user = auth()->user();
        $branch = $this->getBranchForUser($user, $request->branch_id);
        $months = $request->input('months', 12);

        $data = $this->chartService->getSavingsTrendData($branch, $months);

        return response()->json($data);
    }

    /**
     * Get real-time dashboard updates
     */
    public function getRealTimeUpdates(Request $request)
    {
        $user = auth()->user();
        $branch = $this->getBranchForUser($user, $request->branch_id);

        $data = [
            'summary_cards' => $this->getSummaryCards($branch),
            'recent_activities' => $this->getRecentActivities($branch, 5),
            'alerts' => $this->getSystemAlerts($branch),
            'last_updated' => Carbon::now()->format('Y-m-d H:i:s')
        ];

        return response()->json($data);
    }

    /**
     * Get summary cards data
     */
    private function getSummaryCards($branch)
    {
        $startDate = Carbon::now()->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        $financialData = $this->reportingService->getBranchFinancialSummary($branch, $startDate, $endDate);
        $portfolioData = $this->reportingService->getLoanPortfolioAnalysis($branch);
        $memberGrowth = $this->reportingService->getMemberGrowthAnalysis($branch, 1);

        return [
            'total_members' => [
                'value' => $memberGrowth['total_members'],
                'change' => $memberGrowth['monthly_growth']->last()['new_members'] ?? 0,
                'change_type' => 'increase',
                'icon' => 'users',
                'color' => 'blue'
            ],
            'active_loans' => [
                'value' => $portfolioData['active_loans'],
                'change' => 0, // Would calculate month-over-month change
                'change_type' => 'increase',
                'icon' => 'credit-card',
                'color' => 'green'
            ],
            'total_disbursed' => [
                'value' => $financialData['disbursements'],
                'change' => 0, // Would calculate month-over-month change
                'change_type' => 'increase',
                'icon' => 'trending-up',
                'color' => 'purple'
            ],
            'collection_rate' => [
                'value' => $this->reportingService->getCollectionEfficiencyReport($branch, $startDate, $endDate)['collection_rate'],
                'change' => 0, // Would calculate month-over-month change
                'change_type' => 'increase',
                'icon' => 'percent',
                'color' => 'orange'
            ],
            'outstanding_amount' => [
                'value' => $portfolioData['total_outstanding'],
                'change' => 0, // Would calculate month-over-month change
                'change_type' => 'decrease',
                'icon' => 'dollar-sign',
                'color' => 'red'
            ],
            'par_percentage' => [
                'value' => $portfolioData['par_percentage'],
                'change' => 0, // Would calculate month-over-month change
                'change_type' => 'decrease',
                'icon' => 'alert-triangle',
                'color' => 'yellow'
            ]
        ];
    }

    /**
     * Get recent activities
     */
    private function getRecentActivities($branch, $limit = 10)
    {
        $activities = [];

        // Recent loan disbursements
        $recentLoans = \App\Models\Loan::with(['loanApplication.member'])
            ->when($branch, function ($q) use ($branch) {
                return $q->where('branch_id', $branch->id);
            })
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit($limit / 2)
            ->get();

        foreach ($recentLoans as $loan) {
            $activities[] = [
                'type' => 'loan_disbursed',
                'title' => 'Loan Disbursed',
                'description' => "৳" . number_format($loan->loan_amount) . " disbursed to " . $loan->loanApplication->member->name,
                'time' => $loan->created_at->diffForHumans(),
                'icon' => 'credit-card',
                'color' => 'green'
            ];
        }

        // Recent member registrations
        $recentMembers = \App\Models\Member::with(['branch'])
            ->when($branch, function ($q) use ($branch) {
                return $q->where('branch_id', $branch->id);
            })
            ->where('created_at', '>=', Carbon::now()->subDays(7))
            ->orderBy('created_at', 'desc')
            ->limit($limit / 2)
            ->get();

        foreach ($recentMembers as $member) {
            $activities[] = [
                'type' => 'member_registered',
                'title' => 'New Member',
                'description' => $member->name . " registered as new member",
                'time' => $member->created_at->diffForHumans(),
                'icon' => 'user-plus',
                'color' => 'blue'
            ];
        }

        // Sort by time and limit
        usort($activities, function ($a, $b) {
            return strtotime($b['time']) - strtotime($a['time']);
        });

        return array_slice($activities, 0, $limit);
    }

    /**
     * Get system alerts
     */
    private function getSystemAlerts($branch)
    {
        $alerts = [];

        // Overdue loans alert
        $overdueLoans = \App\Models\Installment::whereHas('loan', function ($q) use ($branch) {
            if ($branch) {
                $q->where('branch_id', $branch->id);
            }
            $q->where('status', 'active');
        })
        ->where('status', 'pending')
        ->where('installment_date', '<', Carbon::now()->subDays(7))
        ->count();

        if ($overdueLoans > 0) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Overdue Loans',
                'message' => "{$overdueLoans} installments are overdue by more than 7 days",
                'action_url' => route('reports.financial.outstanding-loans'),
                'action_text' => 'View Details'
            ];
        }

        // High PAR alert
        $portfolioData = $this->reportingService->getLoanPortfolioAnalysis($branch);
        if ($portfolioData['par_percentage'] > 5) {
            $alerts[] = [
                'type' => 'danger',
                'title' => 'High Portfolio at Risk',
                'message' => "PAR is " . number_format($portfolioData['par_percentage'], 2) . "% (above 5% threshold)",
                'action_url' => route('reports.performance.loan-recovery-rate'),
                'action_text' => 'View Analysis'
            ];
        }

        // Low collection efficiency alert
        $collectionData = $this->reportingService->getCollectionEfficiencyReport($branch, Carbon::now()->startOfMonth(), Carbon::now());
        if ($collectionData['collection_rate'] < 90) {
            $alerts[] = [
                'type' => 'warning',
                'title' => 'Low Collection Efficiency',
                'message' => "Collection rate is " . number_format($collectionData['collection_rate'], 2) . "% this month",
                'action_url' => route('reports.financial.collection-efficiency'),
                'action_text' => 'View Report'
            ];
        }

        return $alerts;
    }

    /**
     * Get branch for user based on role and permissions
     */
    private function getBranchForUser($user, $branchId = null)
    {
        if ($user->role === 'admin') {
            return $branchId ? Branch::find($branchId) : null;
        } elseif (in_array($user->role, ['manager', 'field_officer'])) {
            return Branch::find($user->branch_id);
        }

        return null;
    }
}
