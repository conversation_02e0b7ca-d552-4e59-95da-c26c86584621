<?php

namespace App\Http\Controllers\Reports;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Services\ReportingService;
use App\Services\PDFReportService;
use App\Services\ExcelExportService;
use App\Models\Branch;
use App\Models\Member;
use App\Models\User;
use Carbon\Carbon;

class MemberReportController extends Controller
{
    protected $reportingService;
    protected $pdfService;
    protected $excelService;

    public function __construct(
        ReportingService $reportingService,
        PDFReportService $pdfService,
        ExcelExportService $excelService
    ) {
        $this->reportingService = $reportingService;
        $this->pdfService = $pdfService;
        $this->excelService = $excelService;
    }

    /**
     * Display member reports dashboard
     */
    public function index()
    {
        $user = auth()->user();
        $branches = $this->getBranchesForUser($user);

        return view('reports.members.index', compact('branches'));
    }

    /**
     * Generate member registration report
     */
    public function memberRegistration(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'religion' => 'nullable|in:islam,hinduism,christianity,buddhism,other',
            'blood_group' => 'nullable|in:A+,A-,B+,B-,AB+,AB-,O+,O-',
            'marital_status' => 'nullable|in:single,married,divorced,widowed',
            'format' => 'required|in:view,pdf,excel,csv'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        $filters = array_filter([
            'religion' => $request->religion,
            'blood_group' => $request->blood_group,
            'marital_status' => $request->marital_status,
            'date_from' => $startDate->format('Y-m-d'),
            'date_to' => $endDate->format('Y-m-d')
        ]);

        switch ($request->format) {
            case 'pdf':
                return $this->pdfService->generateMemberDirectoryReport($branch);
            
            case 'excel':
                return $this->excelService->exportMembers($branch, $filters);
            
            case 'csv':
                return $this->excelService->exportMembersCSV($branch, $filters);
            
            default:
                $query = Member::with(['branch', 'createdBy', 'loans', 'savingAccounts']);

                if ($branch) {
                    $query->where('branch_id', $branch->id);
                }

                $query->whereBetween('created_at', [$startDate, $endDate]);

                // Apply filters
                if ($request->religion) {
                    $query->where('religion', $request->religion);
                }
                if ($request->blood_group) {
                    $query->where('blood_group', $request->blood_group);
                }
                if ($request->marital_status) {
                    $query->where('marital_status', $request->marital_status);
                }

                $members = $query->orderBy('created_at', 'desc')->paginate(50);

                $summary = [
                    'total_members' => $members->total(),
                    'active_members' => $query->whereHas('loans', function ($q) {
                        $q->where('status', 'active');
                    })->count(),
                    'average_age' => $query->get()->avg(function ($member) {
                        return Carbon::parse($member->date_of_birth)->age;
                    }),
                    'average_income' => $query->avg('monthly_income')
                ];

                return view('reports.members.registration', compact(
                    'branch', 'startDate', 'endDate', 'members', 'summary', 'filters'
                ));
        }
    }

    /**
     * Generate member directory report
     */
    public function memberDirectory(Request $request)
    {
        $request->validate([
            'branch_id' => 'nullable|exists:branches,id',
            'status' => 'nullable|in:active,inactive,all',
            'format' => 'required|in:view,pdf,excel,csv'
        ]);

        $user = auth()->user();
        $branch = $request->branch_id ? Branch::find($request->branch_id) : null;
        $status = $request->status ?? 'all';

        // Check access permissions
        if (!$this->canAccessBranch($user, $branch)) {
            abort(403, 'You do not have permission to access this branch data.');
        }

        $filters = ['status' => $status];

        switch ($request->format) {
            case 'pdf':
                return $this->pdfService->generateMemberDirectoryReport($branch);
            
            case 'excel':
                return $this->excelService->exportMembers($branch, $filters);
            
            case 'csv':
                return $this->excelService->exportMembersCSV($branch, $filters);
            
            default:
                $query = Member::with(['branch', 'createdBy', 'loans', 'savingAccounts']);

                if ($branch) {
                    $query->where('branch_id', $branch->id);
                }

                // Apply status filter
                if ($status === 'active') {
                    $query->whereHas('loans', function ($q) {
                        $q->where('status', 'active');
                    });
                } elseif ($status === 'inactive') {
                    $query->whereDoesntHave('loans', function ($q) {
                        $q->where('status', 'active');
                    });
                }

                $members = $query->orderBy('member_id')->paginate(50);

                $summary = [
                    'total_members' => $members->total(),
                    'by_religion' => Member::when($branch, function ($q) use ($branch) {
                        return $q->where('branch_id', $branch->id);
                    })->groupBy('religion')->selectRaw('religion, count(*) as count')->pluck('count', 'religion'),
                    'by_blood_group' => Member::when($branch, function ($q) use ($branch) {
                        return $q->where('branch_id', $branch->id);
                    })->whereNotNull('blood_group')->groupBy('blood_group')->selectRaw('blood_group, count(*) as count')->pluck('count', 'blood_group')
                ];

                return view('reports.members.directory', compact(
                    'branch', 'members', 'summary', 'status'
                ));
        }
    }

    /**
     * Generate member loan history report
     */
    public function memberLoanHistory(Request $request)
    {
        $request->validate([
            'member_id' => 'required|exists:members,id',
            'format' => 'required|in:view,pdf'
        ]);

        $user = auth()->user();
        $member = Member::with(['branch', 'loans.loanApplication', 'loans.installments'])->findOrFail($request->member_id);

        // Check access permissions
        if (!$this->canAccessMember($user, $member)) {
            abort(403, 'You do not have permission to access this member data.');
        }

        switch ($request->format) {
            case 'pdf':
                return $this->pdfService->generateMemberLoanHistoryReport($member);
            
            default:
                $loans = $member->loans()->with(['loanApplication', 'installments.collectedBy'])->orderBy('loan_date', 'desc')->get();

                $summary = [
                    'total_loans' => $loans->count(),
                    'total_borrowed' => $loans->sum('loan_amount'),
                    'total_paid' => $loans->sum('paid_amount'),
                    'current_outstanding' => $loans->where('status', 'active')->sum('remaining_amount'),
                    'completed_loans' => $loans->where('status', 'completed')->count(),
                    'active_loans' => $loans->where('status', 'active')->count()
                ];

                return view('reports.members.loan-history', compact(
                    'member', 'loans', 'summary'
                ));
        }
    }

    /**
     * Generate savings account statement
     */
    public function savingsStatement(Request $request)
    {
        $request->validate([
            'member_id' => 'required|exists:members,id',
            'account_id' => 'nullable|exists:saving_accounts,id',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after_or_equal:start_date',
            'format' => 'required|in:view,pdf'
        ]);

        $user = auth()->user();
        $member = Member::with(['branch', 'savingAccounts.transactions'])->findOrFail($request->member_id);
        $startDate = Carbon::parse($request->start_date);
        $endDate = Carbon::parse($request->end_date);

        // Check access permissions
        if (!$this->canAccessMember($user, $member)) {
            abort(403, 'You do not have permission to access this member data.');
        }

        $savingAccount = $request->account_id 
            ? $member->savingAccounts()->findOrFail($request->account_id)
            : $member->savingAccounts()->first();

        if (!$savingAccount) {
            abort(404, 'No savings account found for this member.');
        }

        $transactions = $savingAccount->transactions()
            ->whereBetween('transaction_date', [$startDate, $endDate])
            ->orderBy('transaction_date', 'desc')
            ->get();

        $summary = [
            'opening_balance' => $savingAccount->transactions()
                ->where('transaction_date', '<', $startDate)
                ->sum(\DB::raw('CASE WHEN transaction_type = "deposit" THEN amount ELSE -amount END')),
            'total_deposits' => $transactions->where('transaction_type', 'deposit')->sum('amount'),
            'total_withdrawals' => $transactions->where('transaction_type', 'withdrawal')->sum('amount'),
            'closing_balance' => $savingAccount->current_balance
        ];

        switch ($request->format) {
            case 'pdf':
                // Would create a specific PDF for savings statement
                return response()->json(['message' => 'PDF export for savings statement not implemented yet'], 501);
            
            default:
                return view('reports.members.savings-statement', compact(
                    'member', 'savingAccount', 'transactions', 'summary', 'startDate', 'endDate'
                ));
        }
    }

    /**
     * Generate individual member profile report
     */
    public function memberProfile(Request $request)
    {
        $request->validate([
            'member_id' => 'required|exists:members,id',
            'format' => 'required|in:view,pdf'
        ]);

        $user = auth()->user();
        $member = Member::with([
            'branch', 
            'createdBy', 
            'loans.loanApplication', 
            'loans.installments',
            'savingAccounts.transactions'
        ])->findOrFail($request->member_id);

        // Check access permissions
        if (!$this->canAccessMember($user, $member)) {
            abort(403, 'You do not have permission to access this member data.');
        }

        $memberSummary = [
            'total_loans' => $member->loans->count(),
            'active_loans' => $member->loans->where('status', 'active')->count(),
            'total_borrowed' => $member->loans->sum('loan_amount'),
            'current_outstanding' => $member->loans->where('status', 'active')->sum('remaining_amount'),
            'savings_balance' => $member->savingAccounts->sum('current_balance'),
            'last_transaction_date' => $member->savingAccounts->flatMap->transactions->max('transaction_date')
        ];

        switch ($request->format) {
            case 'pdf':
                // Would create a specific PDF for member profile
                return response()->json(['message' => 'PDF export for member profile not implemented yet'], 501);
            
            default:
                return view('reports.members.profile', compact(
                    'member', 'memberSummary'
                ));
        }
    }

    /**
     * Get branches accessible to user
     */
    private function getBranchesForUser($user)
    {
        if ($user->role === 'admin') {
            return Branch::where('is_active', true)->get();
        } elseif ($user->role === 'manager') {
            return Branch::where('id', $user->branch_id)->where('is_active', true)->get();
        } elseif ($user->role === 'field_officer') {
            return Branch::where('id', $user->branch_id)->where('is_active', true)->get();
        }
        
        return collect();
    }

    /**
     * Check if user can access branch data
     */
    private function canAccessBranch($user, $branch)
    {
        if ($user->role === 'admin') {
            return true;
        } elseif (in_array($user->role, ['manager', 'field_officer'])) {
            return !$branch || $branch->id === $user->branch_id;
        }
        
        return false;
    }

    /**
     * Check if user can access member data
     */
    private function canAccessMember($user, $member)
    {
        if ($user->role === 'admin') {
            return true;
        } elseif ($user->role === 'manager') {
            return $member->branch_id === $user->branch_id;
        } elseif ($user->role === 'field_officer') {
            return $member->created_by === $user->id;
        }
        
        return false;
    }
}
