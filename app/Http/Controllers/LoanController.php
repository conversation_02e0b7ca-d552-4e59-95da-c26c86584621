<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Member;
use App\Models\Installment;
use App\Models\ActivityLog;
use Carbon\Carbon;

class LoanController extends Controller
{
    public function __construct()
    {
        $this->middleware(['auth']);
    }

    /**
     * Loan application processing
     */
    public function processApplication(Request $request, LoanApplication $application)
    {
        $user = auth()->user();
        
        // Check authorization based on role
        if (!$this->canProcessApplication($user, $application)) {
            abort(403, 'Unauthorized to process this loan application.');
        }

        if ($application->status !== 'pending') {
            return back()->with('error', 'This application has already been processed.');
        }

        $action = $request->get('action'); // 'approve' or 'reject'

        if ($action === 'approve') {
            return $this->approveApplication($request, $application);
        } elseif ($action === 'reject') {
            return $this->rejectApplication($request, $application);
        }

        return back()->with('error', 'Invalid action specified.');
    }

    /**
     * Loan approval workflow
     */
    private function approveApplication(Request $request, LoanApplication $application): \Illuminate\Http\RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'approved_amount' => 'required|numeric|min:1000|max:1000000',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,bi_weekly,monthly',
            'advance_payment' => 'nullable|numeric|min:0',
            'first_installment_date' => 'required|date|after:today',
            'approval_notes' => 'nullable|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            // Validate business rules
            $validationResult = $this->validateLoanApproval($application, $request->all());
            if (!$validationResult['valid']) {
                return back()->with('error', $validationResult['message'])->withInput();
            }

            // Calculate loan parameters
            $loanParams = $this->calculateLoanParameters($request->all());

            // Create loan
            $loan = $this->createLoan($application, $loanParams);

            // Generate installment schedule
            $this->generateInstallmentSchedule($loan, $loanParams);

            // Update application status
            $application->update([
                'status' => 'approved',
                'reviewed_by' => auth()->id(),
                'reviewed_at' => now(),
                'approval_notes' => $request->approval_notes,
                'approved_amount' => $request->approved_amount,
            ]);

            $this->logActivity(
                'loan_approved',
                "Loan application #{$application->id} approved for member {$application->member->name}",
                [
                    'application_id' => $application->id,
                    'loan_id' => $loan->id,
                    'approved_amount' => $request->approved_amount,
                    'member_id' => $application->member_id,
                ]
            );

            DB::commit();

            return redirect()->back()->with('success', 'Loan application approved successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Loan approval failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to approve loan application. Please try again.')->withInput();
        }
    }

    /**
     * Loan rejection workflow
     */
    private function rejectApplication(Request $request, LoanApplication $application): \Illuminate\Http\RedirectResponse
    {
        $validator = Validator::make($request->all(), [
            'rejection_reason' => 'required|string|max:1000',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $application->update([
                'status' => 'rejected',
                'reviewed_by' => auth()->id(),
                'reviewed_at' => now(),
                'rejection_reason' => $request->rejection_reason,
            ]);

            $this->logActivity(
                'loan_rejected',
                "Loan application #{$application->id} rejected for member {$application->member->name}",
                [
                    'application_id' => $application->id,
                    'rejection_reason' => $request->rejection_reason,
                    'member_id' => $application->member_id,
                ]
            );

            DB::commit();

            return redirect()->back()->with('success', 'Loan application rejected.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Loan rejection failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to reject loan application. Please try again.')->withInput();
        }
    }

    /**
     * Loan disbursement
     */
    public function disburseLoan(Request $request, Loan $loan)
    {
        $user = auth()->user();
        
        // Check authorization
        if (!$this->canDisburseLoan($user, $loan)) {
            abort(403, 'Unauthorized to disburse this loan.');
        }

        if ($loan->status !== 'approved') {
            return back()->with('error', 'Loan is not approved for disbursement.');
        }

        if ($loan->disbursement_date) {
            return back()->with('error', 'Loan has already been disbursed.');
        }

        $validator = Validator::make($request->all(), [
            'disbursement_date' => 'required|date|before_or_equal:today',
            'disbursement_method' => 'required|in:cash,bank_transfer,mobile_banking',
            'disbursement_notes' => 'nullable|string|max:500',
        ]);

        if ($validator->fails()) {
            return back()->withErrors($validator)->withInput();
        }

        try {
            DB::beginTransaction();

            $loan->update([
                'status' => 'active',
                'disbursement_date' => $request->disbursement_date,
                'disbursement_method' => $request->disbursement_method,
                'disbursement_notes' => $request->disbursement_notes,
                'disbursed_by' => $user->id,
            ]);

            $this->logActivity(
                'loan_disbursed',
                "Loan #{$loan->id} disbursed to member {$loan->loanApplication->member->name}",
                [
                    'loan_id' => $loan->id,
                    'amount' => $loan->loan_amount,
                    'member_id' => $loan->loanApplication->member_id,
                    'disbursement_method' => $request->disbursement_method,
                ]
            );

            DB::commit();

            return redirect()->back()->with('success', 'Loan disbursed successfully.');

        } catch (\Exception $e) {
            DB::rollBack();
            \Log::error('Loan disbursement failed: ' . $e->getMessage());
            return back()->with('error', 'Failed to disburse loan. Please try again.')->withInput();
        }
    }

    /**
     * Installment calculation
     */
    public function calculateInstallments(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'loan_amount' => 'required|numeric|min:1000',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,bi_weekly,monthly',
            'first_installment_date' => 'required|date',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        try {
            $params = $request->all();
            $schedule = $this->calculateInstallmentSchedule($params);

            return response()->json([
                'success' => true,
                'schedule' => $schedule,
                'summary' => [
                    'total_installments' => count($schedule),
                    'installment_amount' => $schedule[0]['amount'] ?? 0,
                    'total_amount' => array_sum(array_column($schedule, 'amount')),
                    'total_interest' => array_sum(array_column($schedule, 'amount')) - $params['loan_amount'],
                ]
            ]);

        } catch (\Exception $e) {
            \Log::error('Installment calculation failed: ' . $e->getMessage());
            return response()->json(['error' => 'Failed to calculate installments.'], 500);
        }
    }

    /**
     * Repayment tracking
     */
    public function trackRepayment(Loan $loan)
    {
        $user = auth()->user();
        
        // Check authorization
        if (!$this->canViewLoan($user, $loan)) {
            abort(403, 'Unauthorized to view this loan.');
        }

        $installments = $loan->installments()->orderBy('installment_date')->get();
        
        $repaymentData = [
            'loan' => $loan->load(['loanApplication.member', 'disbursedBy']),
            'installments' => $installments,
            'summary' => $this->calculateRepaymentSummary($loan, $installments),
            'performance' => $this->calculateLoanPerformance($loan, $installments),
        ];

        return view('loans.repayment-tracking', compact('repaymentData'));
    }

    /**
     * Business logic helper methods
     */
    private function canProcessApplication($user, LoanApplication $application): bool
    {
        // Admin can process any application
        if ($user->role === 'admin') {
            return true;
        }

        // Manager can process applications from their branch
        if ($user->role === 'manager') {
            return $application->member->branch_id === $user->branch_id;
        }

        return false;
    }

    private function canDisburseLoan($user, Loan $loan): bool
    {
        // Admin can disburse any loan
        if ($user->role === 'admin') {
            return true;
        }

        // Manager can disburse loans from their branch
        if ($user->role === 'manager') {
            return $loan->loanApplication->member->branch_id === $user->branch_id;
        }

        return false;
    }

    private function canViewLoan($user, Loan $loan): bool
    {
        // Admin can view any loan
        if ($user->role === 'admin') {
            return true;
        }

        // Manager and field officers can view loans from their branch
        if (in_array($user->role, ['manager', 'field_officer'])) {
            return $loan->loanApplication->member->branch_id === $user->branch_id;
        }

        // Members can view their own loans
        if ($user->role === 'member') {
            return $loan->loanApplication->member->user_id === $user->id;
        }

        return false;
    }

    private function validateLoanApproval(LoanApplication $application, array $data): array
    {
        $member = $application->member;

        // Check if member has active loans
        $activeLoans = $member->getActiveLoansCount();
        if ($activeLoans >= 3) { // Maximum 3 active loans
            return [
                'valid' => false,
                'message' => 'Member already has maximum number of active loans (3).'
            ];
        }

        // Check loan amount limits based on cycle
        $maxAmount = $this->getMaxLoanAmount($application->loan_cycle_number);
        if ($data['approved_amount'] > $maxAmount) {
            return [
                'valid' => false,
                'message' => "Maximum loan amount for cycle {$application->loan_cycle_number} is ৳{$maxAmount}."
            ];
        }

        // Check member's repayment history
        $repaymentScore = $member->getRepaymentScore();
        if ($repaymentScore < 70) { // Minimum 70% repayment score
            return [
                'valid' => false,
                'message' => 'Member does not meet minimum repayment score requirement (70%).'
            ];
        }

        return ['valid' => true];
    }

    private function getMaxLoanAmount(int $cycle): float
    {
        // Loan amount limits based on cycle
        return match($cycle) {
            1 => 50000,  // First loan: max 50k
            2 => 100000, // Second loan: max 100k
            3 => 200000, // Third loan: max 200k
            default => 300000, // Subsequent loans: max 300k
        };
    }

    private function calculateLoanParameters(array $data): array
    {
        $loanAmount = $data['approved_amount'];
        $interestRate = $data['interest_rate'];
        $durationMonths = $data['loan_duration_months'];
        $repaymentMethod = $data['repayment_method'];
        $firstInstallmentDate = Carbon::parse($data['first_installment_date']);

        // Calculate number of installments
        $installmentCount = $this->getInstallmentCount($durationMonths, $repaymentMethod);

        // Calculate installment amount using reducing balance method
        $monthlyRate = $interestRate / 100 / 12;
        $installmentAmount = $this->calculateInstallmentAmount($loanAmount, $monthlyRate, $installmentCount, $repaymentMethod);

        // Calculate last installment date
        $lastInstallmentDate = $this->calculateLastInstallmentDate($firstInstallmentDate, $installmentCount, $repaymentMethod);

        return [
            'loan_amount' => $loanAmount,
            'interest_rate' => $interestRate,
            'loan_duration_months' => $durationMonths,
            'repayment_method' => $repaymentMethod,
            'first_installment_date' => $firstInstallmentDate,
            'last_installment_date' => $lastInstallmentDate,
            'installment_count' => $installmentCount,
            'installment_amount' => $installmentAmount,
            'advance_payment' => $data['advance_payment'] ?? 0,
        ];
    }

    private function getInstallmentCount(int $durationMonths, string $repaymentMethod): int
    {
        return match($repaymentMethod) {
            'weekly' => $durationMonths * 4,
            'bi_weekly' => $durationMonths * 2,
            'monthly' => $durationMonths,
            default => $durationMonths,
        };
    }

    private function calculateInstallmentAmount(float $principal, float $monthlyRate, int $installments, string $method): float
    {
        if ($monthlyRate == 0) {
            return $principal / $installments;
        }

        // Adjust rate based on repayment frequency
        $rate = match($method) {
            'weekly' => $monthlyRate / 4,
            'bi_weekly' => $monthlyRate / 2,
            'monthly' => $monthlyRate,
            default => $monthlyRate,
        };

        // EMI calculation using reducing balance
        $emi = $principal * ($rate * pow(1 + $rate, $installments)) / (pow(1 + $rate, $installments) - 1);

        return round($emi, 2);
    }

    private function calculateLastInstallmentDate(Carbon $firstDate, int $installmentCount, string $repaymentMethod): Carbon
    {
        return match($repaymentMethod) {
            'weekly' => $firstDate->copy()->addWeeks($installmentCount - 1),
            'bi_weekly' => $firstDate->copy()->addWeeks(($installmentCount - 1) * 2),
            'monthly' => $firstDate->copy()->addMonths($installmentCount - 1),
            default => $firstDate->copy()->addMonths($installmentCount - 1),
        };
    }

    private function createLoan(LoanApplication $application, array $params): Loan
    {
        return Loan::create([
            'loan_application_id' => $application->id,
            'loan_amount' => $params['loan_amount'],
            'interest_rate' => $params['interest_rate'],
            'loan_duration_months' => $params['loan_duration_months'],
            'repayment_method' => $params['repayment_method'],
            'loan_date' => now(),
            'first_installment_date' => $params['first_installment_date'],
            'last_installment_date' => $params['last_installment_date'],
            'advance_payment' => $params['advance_payment'],
            'status' => 'approved',
            'approved_by' => auth()->id(),
        ]);
    }

    /**
     * Log activity
     */
    private function logActivity(string $action, string $description, array $metadata = []): void
    {
        try {
            ActivityLog::create([
                'user_id' => auth()->id(),
                'action' => $action,
                'description' => $description,
                'ip_address' => request()->ip(),
                'user_agent' => request()->userAgent(),
                'metadata' => array_merge($metadata, [
                    'timestamp' => now()->toISOString(),
                ]),
            ]);
        } catch (\Exception $e) {
            \Log::error('Failed to log activity: ' . $e->getMessage());
        }
    }
}
