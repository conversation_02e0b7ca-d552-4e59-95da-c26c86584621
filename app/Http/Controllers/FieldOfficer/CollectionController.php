<?php

namespace App\Http\Controllers\FieldOfficer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Installment;
use App\Models\Member;
use App\Models\Loan;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class CollectionController extends Controller
{
    /**
     * Display collection dashboard
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));

        // Today's collections due
        $todaysCollections = Installment::whereHas('loan', function($query) use ($memberIds) {
            $query->whereIn('member_id', $memberIds);
        })->whereDate('installment_date', $date)
        ->with(['loan.member'])
        ->get();

        // Overdue collections
        $overdueCollections = Installment::whereHas('loan', function($query) use ($memberIds) {
            $query->whereIn('member_id', $memberIds);
        })->where('status', 'pending')
        ->where('installment_date', '<', $date)
        ->with(['loan.member'])
        ->get();

        // Collections made today
        $collectionsMadeToday = Installment::where('collected_by', $user->id)
            ->where('status', 'paid')
            ->whereDate('collection_date', $date)
            ->with(['loan.member'])
            ->get();

        // Summary statistics
        $summary = [
            'todays_due_count' => $todaysCollections->count(),
            'todays_due_amount' => $todaysCollections->sum('amount'),
            'overdue_count' => $overdueCollections->count(),
            'overdue_amount' => $overdueCollections->sum('amount'),
            'collected_today_count' => $collectionsMadeToday->count(),
            'collected_today_amount' => $collectionsMadeToday->sum('amount'),
            'pending_today_count' => $todaysCollections->where('status', 'pending')->count(),
            'pending_today_amount' => $todaysCollections->where('status', 'pending')->sum('amount'),
        ];

        return view('field-officer.collections.index', compact(
            'todaysCollections', 
            'overdueCollections', 
            'collectionsMadeToday', 
            'summary', 
            'date'
        ));
    }

    /**
     * Show collection form for a specific member
     */
    public function collect(Request $request)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');

        if ($request->filled('member_id')) {
            $member = Member::where('id', $request->member_id)
                ->where('created_by', $user->id)
                ->with(['loans' => function($query) {
                    $query->where('status', 'active')->with(['installments' => function($subQuery) {
                        $subQuery->where('status', 'pending')->orderBy('installment_date');
                    }]);
                }])
                ->first();

            if (!$member) {
                return redirect()->route('field-officer.collections.index')
                    ->with('error', 'Member not found or not assigned to you.');
            }

            $currentLoan = $member->loans->first();
            $pendingInstallments = $currentLoan ? $currentLoan->installments : collect();

            return view('field-officer.collections.collect', compact('member', 'currentLoan', 'pendingInstallments'));
        }

        // Show member search form
        return view('field-officer.collections.search');
    }

    /**
     * Store a collection
     */
    public function store(Request $request)
    {
        $user = auth()->user();

        $request->validate([
            'installment_id' => 'required|exists:installments,id',
            'collection_amount' => 'required|numeric|min:0.01',
            'collection_date' => 'required|date|before_or_equal:today',
            'late_fee' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ]);

        try {
            DB::beginTransaction();

            $installment = Installment::with(['loan.member'])->find($request->installment_id);

            // Verify field officer can collect for this member
            if ($installment->loan->member->created_by !== $user->id) {
                abort(403, 'Unauthorized to collect for this member.');
            }

            // Check if installment is already paid
            if ($installment->status === 'paid') {
                return back()->with('error', 'This installment has already been paid.');
            }

            // Validate collection amount
            $expectedAmount = $installment->amount + ($request->late_fee ?? 0);
            if ($request->collection_amount != $expectedAmount) {
                return back()->with('error', 'Collection amount must equal installment amount plus late fee.')
                    ->withInput();
            }

            // Update installment
            $installment->update([
                'status' => 'paid',
                'collection_date' => $request->collection_date,
                'collected_by' => $user->id,
                'late_fee' => $request->late_fee ?? 0,
                'notes' => $request->notes,
            ]);

            // Check if all installments are paid to mark loan as completed
            $loan = $installment->loan;
            $remainingInstallments = $loan->installments()->where('status', 'pending')->count();
            
            if ($remainingInstallments === 0) {
                $loan->update(['status' => 'completed']);
            }

            DB::commit();

            return redirect()->route('field-officer.collections.receipt', $installment)
                ->with('success', 'Collection recorded successfully!');

        } catch (\Exception $e) {
            DB::rollBack();
            return back()->with('error', 'Failed to record collection: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Show collection receipt
     */
    public function receipt(Installment $installment)
    {
        $user = auth()->user();

        // Verify field officer collected this installment
        if ($installment->collected_by !== $user->id) {
            abort(403, 'Unauthorized access to this receipt.');
        }

        $installment->load(['loan.member', 'collectedBy']);

        return view('field-officer.collections.receipt', compact('installment'));
    }

    /**
     * Show collection history
     */
    public function history(Request $request)
    {
        $user = auth()->user();

        $query = Installment::where('collected_by', $user->id)
            ->where('status', 'paid')
            ->with(['loan.member']);

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('collection_date', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('collection_date', '<=', $request->date_to);
        }

        // Filter by member
        if ($request->filled('member_search')) {
            $search = $request->member_search;
            $query->whereHas('loan.member', function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%");
            });
        }

        $collections = $query->latest('collection_date')->paginate(20);

        // Calculate summary
        $totalCollected = $query->sum('amount');
        $totalLateFees = $query->sum('late_fee');

        $summary = [
            'total_collections' => $collections->total(),
            'total_amount' => $totalCollected,
            'total_late_fees' => $totalLateFees,
        ];

        return view('field-officer.collections.history', compact('collections', 'summary'));
    }

    /**
     * Show daily collection summary
     */
    public function dailySummary(Request $request)
    {
        $user = auth()->user();
        $date = $request->get('date', Carbon::today()->format('Y-m-d'));

        $collections = Installment::where('collected_by', $user->id)
            ->where('status', 'paid')
            ->whereDate('collection_date', $date)
            ->with(['loan.member'])
            ->get();

        $summary = [
            'date' => $date,
            'total_collections' => $collections->count(),
            'total_amount' => $collections->sum('amount'),
            'total_late_fees' => $collections->sum('late_fee'),
            'collections' => $collections,
        ];

        return view('field-officer.collections.daily-summary', compact('summary'));
    }

    /**
     * Get member collection details (AJAX)
     */
    public function getMemberCollectionDetails(Member $member)
    {
        $user = auth()->user();

        // Check if field officer can access this member
        if ($member->created_by !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $member->load(['loans' => function($query) {
            $query->where('status', 'active')->with(['installments' => function($subQuery) {
                $subQuery->where('status', 'pending')->orderBy('installment_date');
            }]);
        }]);

        $currentLoan = $member->loans->first();
        $pendingInstallments = $currentLoan ? $currentLoan->installments : collect();
        $nextInstallment = $pendingInstallments->first();

        $data = [
            'member' => [
                'id' => $member->id,
                'member_id' => $member->member_id,
                'name' => $member->name,
                'phone_number' => $member->phone_number,
            ],
            'current_loan' => $currentLoan ? [
                'loan_id' => $currentLoan->loan_id,
                'loan_amount' => $currentLoan->loan_amount,
                'outstanding_amount' => $pendingInstallments->sum('amount'),
                'total_installments' => $currentLoan->installments()->count(),
                'paid_installments' => $currentLoan->installments()->where('status', 'paid')->count(),
            ] : null,
            'next_installment' => $nextInstallment ? [
                'id' => $nextInstallment->id,
                'amount' => $nextInstallment->amount,
                'due_date' => $nextInstallment->installment_date->format('Y-m-d'),
                'installment_no' => $nextInstallment->installment_no,
                'is_overdue' => $nextInstallment->installment_date->isPast(),
                'days_overdue' => $nextInstallment->installment_date->isPast() 
                    ? $nextInstallment->installment_date->diffInDays(Carbon::now()) 
                    : 0,
            ] : null,
            'pending_installments' => $pendingInstallments->map(function($installment) {
                return [
                    'id' => $installment->id,
                    'amount' => $installment->amount,
                    'due_date' => $installment->installment_date->format('Y-m-d'),
                    'installment_no' => $installment->installment_no,
                    'is_overdue' => $installment->installment_date->isPast(),
                ];
            }),
        ];

        return response()->json($data);
    }

    /**
     * Calculate late fee (AJAX)
     */
    public function calculateLateFee(Request $request)
    {
        $request->validate([
            'installment_id' => 'required|exists:installments,id',
            'collection_date' => 'required|date',
        ]);

        $installment = Installment::find($request->installment_id);
        $collectionDate = Carbon::parse($request->collection_date);
        $dueDate = $installment->installment_date;

        $lateFee = 0;
        if ($collectionDate->gt($dueDate)) {
            $daysLate = $dueDate->diffInDays($collectionDate);
            // Calculate late fee: 1% per day, max 10% of installment amount
            $lateFee = min(
                $installment->amount * 0.01 * $daysLate,
                $installment->amount * 0.10
            );
        }

        return response()->json([
            'late_fee' => round($lateFee, 2),
            'days_late' => $collectionDate->gt($dueDate) ? $dueDate->diffInDays($collectionDate) : 0,
            'total_amount' => $installment->amount + $lateFee,
        ]);
    }

    /**
     * Search members for collection (AJAX)
     */
    public function searchMembers(Request $request)
    {
        $user = auth()->user();
        $query = $request->get('q');

        $members = Member::where('created_by', $user->id)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('member_id', 'like', "%{$query}%")
                  ->orWhere('phone_number', 'like', "%{$query}%");
            })
            ->whereHas('loans', function($q) {
                $q->where('status', 'active');
            })
            ->select('id', 'member_id', 'name', 'phone_number')
            ->limit(10)
            ->get();

        return response()->json($members);
    }
}
