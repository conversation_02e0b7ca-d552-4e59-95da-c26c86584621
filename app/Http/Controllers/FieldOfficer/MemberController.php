<?php

namespace App\Http\Controllers\FieldOfficer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\MemberRegistrationRequest;
use App\Services\BusinessRuleValidationService;
use App\Services\SecurityService;
use App\Services\FileManagerService;
use App\Services\ImageProcessingService;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\SavingAccount;
use App\Models\Installment;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class MemberController extends Controller
{
    /**
     * Display field officer's members
     */
    public function index(Request $request)
    {
        $user = auth()->user();

        $query = Member::where('created_by', $user->id)
            ->with(['branch', 'loans', 'savingAccounts']);

        // Search functionality
        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('member_id', 'like', "%{$search}%")
                  ->orWhere('phone_number', 'like', "%{$search}%")
                  ->orWhere('nid_number', 'like', "%{$search}%");
            });
        }

        // Filter by loan status
        if ($request->filled('loan_status')) {
            switch ($request->loan_status) {
                case 'active':
                    $query->whereHas('loans', function($q) {
                        $q->where('status', 'active');
                    });
                    break;
                case 'no_loan':
                    $query->whereDoesntHave('loans', function($q) {
                        $q->where('status', 'active');
                    });
                    break;
            }
        }

        $members = $query->latest('created_at')->paginate(15);

        return view('field-officer.members.index', compact('members'));
    }

    /**
     * Show the form for creating a new member
     */
    public function create()
    {
        $user = auth()->user();
        
        // Get existing members for reference selection
        $existingMembers = Member::where('created_by', $user->id)
            ->select('id', 'name', 'member_id')
            ->get();

        return view('field-officer.members.create', compact('existingMembers'));
    }

    /**
     * Store a newly created member
     */
    public function store(MemberRegistrationRequest $request)
    {
        $user = auth()->user();
        $businessRuleService = new BusinessRuleValidationService();
        $securityService = new SecurityService();

        // Additional business rule validation
        $memberData = $request->validated();
        $validation = $businessRuleService->validateMemberRegistration($memberData, $user);

        if (!$validation['valid']) {
            return back()->withErrors($validation['errors'])->withInput();
        }

        try {
            // Generate unique member ID
            $memberIdPrefix = 'M' . date('Y');
            $lastMember = Member::where('member_id', 'like', $memberIdPrefix . '%')
                ->orderBy('member_id', 'desc')
                ->first();
            
            if ($lastMember) {
                $lastNumber = (int) substr($lastMember->member_id, -4);
                $newNumber = str_pad($lastNumber + 1, 4, '0', STR_PAD_LEFT);
            } else {
                $newNumber = '0001';
            }
            
            $memberId = $memberIdPrefix . $newNumber;

            // Handle photo upload using new services
            $photoData = null;
            if ($request->hasFile('photo')) {
                $fileManager = app(FileManagerService::class);
                $imageProcessor = app(ImageProcessingService::class);

                // Process member photo with multiple sizes
                $photoResults = $imageProcessor->processMemberPhoto($request->file('photo'), $memberId);
                $photoData = json_encode($photoResults);
            }

            // Create member
            $member = Member::create([
                'member_id' => $memberId,
                'name' => $request->name,
                'father_or_husband_name' => $request->father_or_husband_name,
                'mother_name' => $request->mother_name,
                'present_address' => $request->present_address,
                'permanent_address' => $request->permanent_address,
                'nid_number' => $request->nid_number,
                'date_of_birth' => $request->date_of_birth,
                'religion' => $request->religion,
                'phone_number' => $request->phone_number,
                'blood_group' => $request->blood_group,
                'photo' => $photoData,
                'occupation' => $request->occupation,
                'monthly_income' => $request->monthly_income,
                'reference_id' => $request->reference_id,
                'branch_id' => $user->branch_id,
                'created_by' => $user->id,
            ]);

            // Log security activity
            $securityService->logActivity('member_created', [
                'member_id' => $member->id,
                'member_code' => $memberId,
                'created_by' => $user->id,
                'branch_id' => $user->branch_id
            ]);

            return redirect()->route('field-officer.members.show', $member)
                ->with('success', 'Member registered successfully! Member ID: ' . $memberId);

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to register member: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified member
     */
    public function show(Member $member)
    {
        $user = auth()->user();

        // Check if field officer can access this member
        if ($member->created_by !== $user->id) {
            abort(403, 'Unauthorized access to this member.');
        }

        $member->load(['branch', 'createdBy', 'reference', 'loans.installments', 'savingAccounts', 'loanApplications']);

        // Calculate member statistics
        $stats = [
            'total_loans' => $member->loans->count(),
            'active_loans' => $member->loans->where('status', 'active')->count(),
            'completed_loans' => $member->loans->where('status', 'completed')->count(),
            'total_loan_amount' => $member->loans->sum('loan_amount'),
            'total_outstanding' => $member->loans->where('status', 'active')->sum(function($loan) {
                return $loan->installments->where('status', 'pending')->sum('amount');
            }),
            'total_paid' => $member->loans->sum(function($loan) {
                return $loan->installments->where('status', 'paid')->sum('amount');
            }),
            'savings_balance' => $member->savingAccounts->sum('current_balance'),
            'pending_applications' => $member->loanApplications->where('status', 'pending')->count(),
        ];

        // Get upcoming installments
        $upcomingInstallments = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id)->where('status', 'active');
        })->where('status', 'pending')
        ->where('installment_date', '>=', now())
        ->with(['loan'])
        ->orderBy('installment_date')
        ->take(5)
        ->get();

        // Get recent payments
        $recentPayments = Installment::whereHas('loan', function($q) use ($member) {
            $q->where('member_id', $member->id);
        })->where('status', 'paid')
        ->with(['loan', 'collectedBy'])
        ->latest('collection_date')
        ->take(10)
        ->get();

        return view('field-officer.members.show', compact('member', 'stats', 'upcomingInstallments', 'recentPayments'));
    }

    /**
     * Show the form for editing the specified member
     */
    public function edit(Member $member)
    {
        $user = auth()->user();

        // Check if field officer can access this member
        if ($member->created_by !== $user->id) {
            abort(403, 'Unauthorized access to this member.');
        }

        // Get existing members for reference selection (excluding current member)
        $existingMembers = Member::where('created_by', $user->id)
            ->where('id', '!=', $member->id)
            ->select('id', 'name', 'member_id')
            ->get();

        return view('field-officer.members.edit', compact('member', 'existingMembers'));
    }

    /**
     * Update the specified member
     */
    public function update(MemberRegistrationRequest $request, Member $member)
    {
        $user = auth()->user();
        $securityService = new SecurityService();

        // Check if field officer can access this member
        if ($member->created_by !== $user->id) {
            abort(403, 'Unauthorized access to this member.');
        }

        try {
            $updateData = $request->except(['photo']);

            // Handle photo upload using new services
            if ($request->hasFile('photo')) {
                $fileManager = app(FileManagerService::class);
                $imageProcessor = app(ImageProcessingService::class);

                // Delete old photo files if they exist
                if ($member->photo) {
                    $oldPhotoData = json_decode($member->photo, true);
                    if ($oldPhotoData) {
                        $fileManager->deleteFile($oldPhotoData);
                    }
                }

                // Process new member photo with multiple sizes
                $photoResults = $imageProcessor->processMemberPhoto($request->file('photo'), $member->member_id);
                $updateData['photo'] = json_encode($photoResults);
            }

            $member->update($updateData);

            // Log security activity
            $securityService->logActivity('member_updated', [
                'member_id' => $member->id,
                'member_code' => $member->member_id,
                'updated_by' => $user->id,
                'changes' => array_keys($updateData)
            ]);

            return redirect()->route('field-officer.members.show', $member)
                ->with('success', 'Member information updated successfully!');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update member: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Get member details for AJAX
     */
    public function getMemberDetails(Member $member)
    {
        $user = auth()->user();

        // Check if field officer can access this member
        if ($member->created_by !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        $member->load(['loans' => function($query) {
            $query->where('status', 'active')->with('installments');
        }]);

        $currentLoan = $member->loans->first();
        $nextInstallment = null;

        if ($currentLoan) {
            $nextInstallment = $currentLoan->installments
                ->where('status', 'pending')
                ->sortBy('installment_date')
                ->first();
        }

        return response()->json([
            'member' => [
                'id' => $member->id,
                'member_id' => $member->member_id,
                'name' => $member->name,
                'phone_number' => $member->phone_number,
                'current_loan' => $currentLoan ? [
                    'loan_id' => $currentLoan->loan_id,
                    'loan_amount' => $currentLoan->loan_amount,
                    'outstanding_amount' => $currentLoan->installments->where('status', 'pending')->sum('amount'),
                ] : null,
                'next_installment' => $nextInstallment ? [
                    'id' => $nextInstallment->id,
                    'amount' => $nextInstallment->amount,
                    'due_date' => $nextInstallment->installment_date->format('Y-m-d'),
                    'installment_no' => $nextInstallment->installment_no,
                ] : null,
            ]
        ]);
    }

    /**
     * Search members for AJAX
     */
    public function search(Request $request)
    {
        $user = auth()->user();
        $query = $request->get('q');

        $members = Member::where('created_by', $user->id)
            ->where(function($q) use ($query) {
                $q->where('name', 'like', "%{$query}%")
                  ->orWhere('member_id', 'like', "%{$query}%")
                  ->orWhere('phone_number', 'like', "%{$query}%");
            })
            ->select('id', 'member_id', 'name', 'phone_number')
            ->limit(10)
            ->get();

        return response()->json($members);
    }
}
