<?php

namespace App\Http\Controllers\FieldOfficer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\LoanApplication;
use App\Models\Member;
use App\Models\Loan;
use Carbon\Carbon;

class LoanApplicationController extends Controller
{
    /**
     * Display loan applications submitted by field officer
     */
    public function index(Request $request)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');

        $query = LoanApplication::whereIn('member_id', $memberIds)
            ->with(['member', 'reviewedBy']);

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('applied_at', '>=', $request->date_from);
        }
        if ($request->filled('date_to')) {
            $query->whereDate('applied_at', '<=', $request->date_to);
        }

        $applications = $query->latest('applied_at')->paginate(15);

        return view('field-officer.loan-applications.index', compact('applications'));
    }

    /**
     * Show the form for creating a new loan application
     */
    public function create()
    {
        $user = auth()->user();
        
        // Get field officer's members who can apply for loans
        $members = Member::where('created_by', $user->id)
            ->with(['loans' => function($query) {
                $query->where('status', 'active');
            }])
            ->get()
            ->filter(function($member) {
                // Member can apply if they don't have an active loan
                return $member->loans->where('status', 'active')->isEmpty();
            });

        return view('field-officer.loan-applications.create', compact('members'));
    }

    /**
     * Store a newly created loan application
     */
    public function store(Request $request)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');

        $request->validate([
            'member_id' => 'required|in:' . $memberIds->implode(','),
            'applied_amount' => 'required|numeric|min:1000|max:500000',
            'reason' => 'required|string|max:1000',
            'recommender' => 'nullable|string|max:100',
            'advance_payment' => 'nullable|numeric|min:0|max:' . ($request->applied_amount * 0.2), // Max 20% advance
        ]);

        try {
            $member = Member::find($request->member_id);

            // Check if member already has an active loan
            $activeLoan = Loan::where('member_id', $member->id)
                ->where('status', 'active')
                ->first();

            if ($activeLoan) {
                return back()->with('error', 'Member already has an active loan.')
                    ->withInput();
            }

            // Check if member has a pending application
            $pendingApplication = LoanApplication::where('member_id', $member->id)
                ->where('status', 'pending')
                ->first();

            if ($pendingApplication) {
                return back()->with('error', 'Member already has a pending loan application.')
                    ->withInput();
            }

            // Calculate loan cycle number
            $loanCycleNumber = LoanApplication::where('member_id', $member->id)->count() + 1;

            // Create loan application
            $application = LoanApplication::create([
                'member_id' => $request->member_id,
                'applied_amount' => $request->applied_amount,
                'reason' => $request->reason,
                'loan_cycle_number' => $loanCycleNumber,
                'recommender' => $request->recommender,
                'advance_payment' => $request->advance_payment ?? 0,
                'status' => 'pending',
                'applied_at' => now(),
            ]);

            return redirect()->route('field-officer.loan-applications.show', $application)
                ->with('success', 'Loan application submitted successfully!');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to submit loan application: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Display the specified loan application
     */
    public function show(LoanApplication $loanApplication)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');

        // Check if field officer can access this application
        if (!$memberIds->contains($loanApplication->member_id)) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        $loanApplication->load(['member', 'reviewedBy', 'loan']);

        // Calculate loan details
        $repaymentAmount = $loanApplication->calculateRepaymentAmount();
        $totalRepayment = $loanApplication->calculateTotalRepaymentAmount();
        $totalInterest = $loanApplication->calculateTotalInterest();

        return view('field-officer.loan-applications.show', compact(
            'loanApplication', 
            'repaymentAmount', 
            'totalRepayment', 
            'totalInterest'
        ));
    }

    /**
     * Show the form for editing the specified loan application
     */
    public function edit(LoanApplication $loanApplication)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');

        // Check if field officer can access this application
        if (!$memberIds->contains($loanApplication->member_id)) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        // Only allow editing pending applications
        if ($loanApplication->status !== 'pending') {
            return redirect()->route('field-officer.loan-applications.show', $loanApplication)
                ->with('error', 'Cannot edit a ' . $loanApplication->status . ' application.');
        }

        $members = Member::where('created_by', $user->id)
            ->select('id', 'member_id', 'name')
            ->get();

        return view('field-officer.loan-applications.edit', compact('loanApplication', 'members'));
    }

    /**
     * Update the specified loan application
     */
    public function update(Request $request, LoanApplication $loanApplication)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');

        // Check if field officer can access this application
        if (!$memberIds->contains($loanApplication->member_id)) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        // Only allow editing pending applications
        if ($loanApplication->status !== 'pending') {
            return redirect()->route('field-officer.loan-applications.show', $loanApplication)
                ->with('error', 'Cannot edit a ' . $loanApplication->status . ' application.');
        }

        $request->validate([
            'member_id' => 'required|in:' . $memberIds->implode(','),
            'applied_amount' => 'required|numeric|min:1000|max:500000',
            'reason' => 'required|string|max:1000',
            'recommender' => 'nullable|string|max:100',
            'advance_payment' => 'nullable|numeric|min:0|max:' . ($request->applied_amount * 0.2),
        ]);

        try {
            $loanApplication->update([
                'member_id' => $request->member_id,
                'applied_amount' => $request->applied_amount,
                'reason' => $request->reason,
                'recommender' => $request->recommender,
                'advance_payment' => $request->advance_payment ?? 0,
            ]);

            return redirect()->route('field-officer.loan-applications.show', $loanApplication)
                ->with('success', 'Loan application updated successfully!');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to update loan application: ' . $e->getMessage())
                ->withInput();
        }
    }

    /**
     * Remove the specified loan application
     */
    public function destroy(LoanApplication $loanApplication)
    {
        $user = auth()->user();
        $memberIds = Member::where('created_by', $user->id)->pluck('id');

        // Check if field officer can access this application
        if (!$memberIds->contains($loanApplication->member_id)) {
            abort(403, 'Unauthorized access to this loan application.');
        }

        // Only allow deleting pending applications
        if ($loanApplication->status !== 'pending') {
            return redirect()->route('field-officer.loan-applications.index')
                ->with('error', 'Cannot delete a ' . $loanApplication->status . ' application.');
        }

        try {
            $loanApplication->delete();

            return redirect()->route('field-officer.loan-applications.index')
                ->with('success', 'Loan application deleted successfully!');

        } catch (\Exception $e) {
            return back()->with('error', 'Failed to delete loan application: ' . $e->getMessage());
        }
    }

    /**
     * Calculate loan repayment details (AJAX)
     */
    public function calculateRepayment(Request $request)
    {
        $request->validate([
            'loan_amount' => 'required|numeric|min:1000',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,monthly',
            'advance_payment' => 'nullable|numeric|min:0',
        ]);

        $loanAmount = $request->loan_amount;
        $interestRate = $request->interest_rate;
        $durationMonths = $request->loan_duration_months;
        $repaymentMethod = $request->repayment_method;
        $advancePayment = $request->advance_payment ?? 0;

        // Calculate based on repayment method
        if ($repaymentMethod === 'weekly') {
            $totalInstallments = $durationMonths * 4; // Approximate weeks in months
            $monthlyInterest = ($loanAmount * $interestRate / 100) / 12;
            $totalInterest = $monthlyInterest * $durationMonths;
            $totalRepayment = $loanAmount + $totalInterest;
            $netRepayment = $totalRepayment - $advancePayment;
            $installmentAmount = $netRepayment / $totalInstallments;
        } else {
            $totalInstallments = $durationMonths;
            $monthlyInterest = ($loanAmount * $interestRate / 100) / 12;
            $totalInterest = $monthlyInterest * $durationMonths;
            $totalRepayment = $loanAmount + $totalInterest;
            $netRepayment = $totalRepayment - $advancePayment;
            $installmentAmount = $netRepayment / $totalInstallments;
        }

        return response()->json([
            'loan_amount' => number_format($loanAmount, 2),
            'advance_payment' => number_format($advancePayment, 2),
            'total_interest' => number_format($totalInterest, 2),
            'total_repayment' => number_format($totalRepayment, 2),
            'net_repayment' => number_format($netRepayment, 2),
            'installment_amount' => number_format($installmentAmount, 2),
            'total_installments' => $totalInstallments,
            'repayment_method' => $repaymentMethod,
        ]);
    }

    /**
     * Get member loan eligibility (AJAX)
     */
    public function checkEligibility(Member $member)
    {
        $user = auth()->user();

        // Check if field officer can access this member
        if ($member->created_by !== $user->id) {
            return response()->json(['error' => 'Unauthorized'], 403);
        }

        // Check for active loans
        $activeLoan = Loan::where('member_id', $member->id)
            ->where('status', 'active')
            ->first();

        // Check for pending applications
        $pendingApplication = LoanApplication::where('member_id', $member->id)
            ->where('status', 'pending')
            ->first();

        // Calculate next loan cycle
        $loanCycleNumber = LoanApplication::where('member_id', $member->id)->count() + 1;

        // Get loan history
        $loanHistory = Loan::where('member_id', $member->id)
            ->with('installments')
            ->get();

        $eligibility = [
            'eligible' => !$activeLoan && !$pendingApplication,
            'reason' => null,
            'loan_cycle_number' => $loanCycleNumber,
            'loan_history_count' => $loanHistory->count(),
            'last_loan_status' => $loanHistory->last()?->status,
        ];

        if ($activeLoan) {
            $eligibility['reason'] = 'Member has an active loan';
        } elseif ($pendingApplication) {
            $eligibility['reason'] = 'Member has a pending loan application';
        }

        return response()->json($eligibility);
    }
}
