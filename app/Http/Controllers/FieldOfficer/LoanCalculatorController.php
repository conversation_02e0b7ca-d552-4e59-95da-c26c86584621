<?php

namespace App\Http\Controllers\FieldOfficer;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;

class LoanCalculatorController extends Controller
{
    /**
     * Show the loan calculator
     */
    public function index()
    {
        return view('field-officer.loan-calculator.index');
    }

    /**
     * Calculate loan repayment details
     */
    public function calculate(Request $request)
    {
        $request->validate([
            'loan_amount' => 'required|numeric|min:1000|max:1000000',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,monthly',
            'advance_payment' => 'nullable|numeric|min:0',
        ]);

        $loanAmount = $request->loan_amount;
        $interestRate = $request->interest_rate;
        $durationMonths = $request->loan_duration_months;
        $repaymentMethod = $request->repayment_method;
        $advancePayment = $request->advance_payment ?? 0;

        // Validate advance payment doesn't exceed 20% of loan amount
        if ($advancePayment > ($loanAmount * 0.20)) {
            return response()->json([
                'error' => 'Advance payment cannot exceed 20% of loan amount'
            ], 422);
        }

        // Calculate interest and repayment details
        $calculations = $this->performCalculations(
            $loanAmount, 
            $interestRate, 
            $durationMonths, 
            $repaymentMethod, 
            $advancePayment
        );

        return response()->json($calculations);
    }

    /**
     * Perform loan calculations
     */
    private function performCalculations($loanAmount, $interestRate, $durationMonths, $repaymentMethod, $advancePayment = 0)
    {
        // Calculate total interest
        $monthlyInterestRate = $interestRate / 100 / 12;
        $totalInterest = $loanAmount * $monthlyInterestRate * $durationMonths;
        
        // Calculate total repayment amount
        $totalRepayment = $loanAmount + $totalInterest;
        
        // Calculate net repayment after advance payment
        $netRepayment = $totalRepayment - $advancePayment;
        
        // Calculate number of installments based on repayment method
        if ($repaymentMethod === 'weekly') {
            $totalInstallments = $durationMonths * 4; // Approximate weeks in months
            $installmentFrequency = 'Weekly';
        } else {
            $totalInstallments = $durationMonths;
            $installmentFrequency = 'Monthly';
        }
        
        // Calculate installment amount
        $installmentAmount = $netRepayment / $totalInstallments;
        
        // Calculate effective interest rate
        $effectiveInterestRate = ($totalInterest / $loanAmount) * 100;
        
        // Generate installment schedule
        $installmentSchedule = $this->generateInstallmentSchedule(
            $installmentAmount, 
            $totalInstallments, 
            $repaymentMethod
        );

        return [
            'loan_amount' => number_format($loanAmount, 2),
            'interest_rate' => number_format($interestRate, 2),
            'duration_months' => $durationMonths,
            'repayment_method' => $repaymentMethod,
            'installment_frequency' => $installmentFrequency,
            'advance_payment' => number_format($advancePayment, 2),
            'total_interest' => number_format($totalInterest, 2),
            'total_repayment' => number_format($totalRepayment, 2),
            'net_repayment' => number_format($netRepayment, 2),
            'installment_amount' => number_format($installmentAmount, 2),
            'total_installments' => $totalInstallments,
            'effective_interest_rate' => number_format($effectiveInterestRate, 2),
            'installment_schedule' => $installmentSchedule,
            'summary' => [
                'principal' => $loanAmount,
                'interest' => $totalInterest,
                'advance' => $advancePayment,
                'total_payable' => $totalRepayment,
                'net_payable' => $netRepayment,
                'per_installment' => $installmentAmount,
            ]
        ];
    }

    /**
     * Generate installment schedule
     */
    private function generateInstallmentSchedule($installmentAmount, $totalInstallments, $repaymentMethod)
    {
        $schedule = [];
        $currentDate = now();
        
        for ($i = 1; $i <= min($totalInstallments, 12); $i++) { // Show first 12 installments
            if ($repaymentMethod === 'weekly') {
                $dueDate = $currentDate->copy()->addWeeks($i);
            } else {
                $dueDate = $currentDate->copy()->addMonths($i);
            }
            
            $schedule[] = [
                'installment_no' => $i,
                'due_date' => $dueDate->format('Y-m-d'),
                'amount' => number_format($installmentAmount, 2),
                'formatted_date' => $dueDate->format('M d, Y'),
            ];
        }
        
        if ($totalInstallments > 12) {
            $schedule[] = [
                'installment_no' => '...',
                'due_date' => '...',
                'amount' => '...',
                'formatted_date' => '... and ' . ($totalInstallments - 12) . ' more installments',
            ];
        }
        
        return $schedule;
    }

    /**
     * Compare different loan scenarios
     */
    public function compare(Request $request)
    {
        $request->validate([
            'scenarios' => 'required|array|min:2|max:4',
            'scenarios.*.loan_amount' => 'required|numeric|min:1000',
            'scenarios.*.interest_rate' => 'required|numeric|min:0|max:50',
            'scenarios.*.loan_duration_months' => 'required|integer|min:1|max:60',
            'scenarios.*.repayment_method' => 'required|in:weekly,monthly',
            'scenarios.*.advance_payment' => 'nullable|numeric|min:0',
        ]);

        $comparisons = [];
        
        foreach ($request->scenarios as $index => $scenario) {
            $calculations = $this->performCalculations(
                $scenario['loan_amount'],
                $scenario['interest_rate'],
                $scenario['loan_duration_months'],
                $scenario['repayment_method'],
                $scenario['advance_payment'] ?? 0
            );
            
            $comparisons["scenario_" . ($index + 1)] = $calculations;
        }

        return response()->json([
            'comparisons' => $comparisons,
            'best_scenario' => $this->findBestScenario($comparisons),
        ]);
    }

    /**
     * Find the best scenario based on lowest total cost
     */
    private function findBestScenario($comparisons)
    {
        $bestScenario = null;
        $lowestCost = PHP_FLOAT_MAX;
        
        foreach ($comparisons as $key => $scenario) {
            $totalCost = (float) str_replace(',', '', $scenario['total_repayment']);
            if ($totalCost < $lowestCost) {
                $lowestCost = $totalCost;
                $bestScenario = $key;
            }
        }
        
        return $bestScenario;
    }

    /**
     * Get loan calculation presets
     */
    public function getPresets()
    {
        $presets = [
            'micro_loan' => [
                'name' => 'Micro Loan',
                'loan_amount' => 10000,
                'interest_rate' => 12,
                'loan_duration_months' => 6,
                'repayment_method' => 'weekly',
                'advance_payment' => 0,
            ],
            'small_business' => [
                'name' => 'Small Business Loan',
                'loan_amount' => 50000,
                'interest_rate' => 15,
                'loan_duration_months' => 12,
                'repayment_method' => 'monthly',
                'advance_payment' => 5000,
            ],
            'agriculture' => [
                'name' => 'Agriculture Loan',
                'loan_amount' => 25000,
                'interest_rate' => 10,
                'loan_duration_months' => 9,
                'repayment_method' => 'monthly',
                'advance_payment' => 2500,
            ],
            'emergency' => [
                'name' => 'Emergency Loan',
                'loan_amount' => 15000,
                'interest_rate' => 18,
                'loan_duration_months' => 3,
                'repayment_method' => 'weekly',
                'advance_payment' => 0,
            ],
        ];

        return response()->json($presets);
    }

    /**
     * Calculate maximum loan amount based on income
     */
    public function calculateMaxLoan(Request $request)
    {
        $request->validate([
            'monthly_income' => 'required|numeric|min:1000',
            'existing_obligations' => 'nullable|numeric|min:0',
            'interest_rate' => 'required|numeric|min:0|max:50',
            'loan_duration_months' => 'required|integer|min:1|max:60',
            'repayment_method' => 'required|in:weekly,monthly',
        ]);

        $monthlyIncome = $request->monthly_income;
        $existingObligations = $request->existing_obligations ?? 0;
        $interestRate = $request->interest_rate;
        $durationMonths = $request->loan_duration_months;
        $repaymentMethod = $request->repayment_method;

        // Calculate available income for loan repayment (max 40% of net income)
        $netIncome = $monthlyIncome - $existingObligations;
        $maxRepaymentCapacity = $netIncome * 0.40;

        // Calculate installment frequency
        if ($repaymentMethod === 'weekly') {
            $installmentsPerMonth = 4;
            $totalInstallments = $durationMonths * 4;
        } else {
            $installmentsPerMonth = 1;
            $totalInstallments = $durationMonths;
        }

        $maxInstallmentAmount = $maxRepaymentCapacity / $installmentsPerMonth;

        // Calculate maximum loan amount
        $monthlyInterestRate = $interestRate / 100 / 12;
        $totalInterestFactor = 1 + ($monthlyInterestRate * $durationMonths);
        $maxLoanAmount = ($maxInstallmentAmount * $totalInstallments) / $totalInterestFactor;

        return response()->json([
            'monthly_income' => number_format($monthlyIncome, 2),
            'existing_obligations' => number_format($existingObligations, 2),
            'net_income' => number_format($netIncome, 2),
            'max_repayment_capacity' => number_format($maxRepaymentCapacity, 2),
            'max_installment_amount' => number_format($maxInstallmentAmount, 2),
            'max_loan_amount' => number_format($maxLoanAmount, 2),
            'recommended_loan_amount' => number_format($maxLoanAmount * 0.8, 2), // 80% of max for safety
            'debt_to_income_ratio' => number_format(($maxRepaymentCapacity / $monthlyIncome) * 100, 2),
        ]);
    }
}
