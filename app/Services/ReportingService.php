<?php

namespace App\Services;

use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Installment;
use App\Models\SavingAccount;
use App\Models\SavingTransaction;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ReportingService
{
    /**
     * Get financial summary for a branch
     */
    public function getBranchFinancialSummary(Branch $branch, Carbon $startDate, Carbon $endDate): array
    {
        $cacheKey = "branch_financial_{$branch->id}_{$startDate->format('Y-m-d')}_{$endDate->format('Y-m-d')}";
        
        return Cache::remember($cacheKey, 3600, function () use ($branch, $startDate, $endDate) {
            // Loan disbursements
            $disbursements = Loan::where('branch_id', $branch->id)
                ->whereBetween('loan_date', [$startDate, $endDate])
                ->sum('loan_amount');

            // Collections
            $collections = Installment::whereHas('loan', function ($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })
                ->where('status', 'paid')
                ->whereBetween('collection_date', [$startDate, $endDate])
                ->sum('amount');

            // Interest income
            $interestIncome = Installment::whereHas('loan', function ($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })
                ->where('status', 'paid')
                ->whereBetween('collection_date', [$startDate, $endDate])
                ->sum('interest_amount');

            // Outstanding loans
            $outstandingLoans = Loan::where('branch_id', $branch->id)
                ->where('status', 'active')
                ->sum('remaining_amount');

            // Savings deposits
            $savingsDeposits = SavingTransaction::whereHas('savingAccount.member', function ($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })
                ->where('transaction_type', 'deposit')
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->sum('amount');

            // Savings withdrawals
            $savingsWithdrawals = SavingTransaction::whereHas('savingAccount.member', function ($query) use ($branch) {
                $query->where('branch_id', $branch->id);
            })
                ->where('transaction_type', 'withdrawal')
                ->whereBetween('transaction_date', [$startDate, $endDate])
                ->sum('amount');

            return [
                'disbursements' => $disbursements,
                'collections' => $collections,
                'interest_income' => $interestIncome,
                'outstanding_loans' => $outstandingLoans,
                'savings_deposits' => $savingsDeposits,
                'savings_withdrawals' => $savingsWithdrawals,
                'net_savings_flow' => $savingsDeposits - $savingsWithdrawals,
                'net_income' => $collections + $interestIncome - $disbursements,
                'period' => [
                    'start' => $startDate->format('Y-m-d'),
                    'end' => $endDate->format('Y-m-d')
                ]
            ];
        });
    }

    /**
     * Get loan portfolio analysis
     */
    public function getLoanPortfolioAnalysis(Branch $branch = null, Carbon $asOfDate = null): array
    {
        $asOfDate = $asOfDate ?? Carbon::now();
        $query = Loan::query();

        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $loans = $query->with(['loanApplication.member', 'installments'])->get();

        $analysis = [
            'total_loans' => $loans->count(),
            'active_loans' => $loans->where('status', 'active')->count(),
            'completed_loans' => $loans->where('status', 'completed')->count(),
            'defaulted_loans' => $loans->where('status', 'defaulted')->count(),
            'total_disbursed' => $loans->sum('loan_amount'),
            'total_outstanding' => $loans->where('status', 'active')->sum('remaining_amount'),
            'total_collected' => $loans->sum('paid_amount'),
            'portfolio_at_risk' => 0,
            'average_loan_size' => $loans->count() > 0 ? $loans->avg('loan_amount') : 0,
            'by_status' => [],
            'by_duration' => [],
            'overdue_analysis' => []
        ];

        // Portfolio at Risk (PAR) calculation
        $overdueLoans = $loans->filter(function ($loan) use ($asOfDate) {
            return $loan->installments->where('status', 'pending')
                ->where('installment_date', '<', $asOfDate)
                ->count() > 0;
        });

        $analysis['portfolio_at_risk'] = $overdueLoans->sum('remaining_amount');
        $analysis['par_percentage'] = $analysis['total_outstanding'] > 0 
            ? ($analysis['portfolio_at_risk'] / $analysis['total_outstanding']) * 100 
            : 0;

        // Group by status
        $analysis['by_status'] = $loans->groupBy('status')->map(function ($group) {
            return [
                'count' => $group->count(),
                'amount' => $group->sum('loan_amount'),
                'percentage' => 0 // Will be calculated later
            ];
        });

        // Group by loan duration
        $analysis['by_duration'] = $loans->groupBy('loan_duration_months')->map(function ($group, $duration) {
            return [
                'duration' => $duration . ' months',
                'count' => $group->count(),
                'amount' => $group->sum('loan_amount')
            ];
        });

        return $analysis;
    }

    /**
     * Get field officer performance metrics
     */
    public function getFieldOfficerPerformance(User $fieldOfficer, Carbon $startDate, Carbon $endDate): array
    {
        $memberIds = Member::where('created_by', $fieldOfficer->id)->pluck('id');

        return [
            'officer_info' => [
                'name' => $fieldOfficer->name,
                'employee_id' => $fieldOfficer->employee_id,
                'branch' => $fieldOfficer->branch->name ?? 'N/A'
            ],
            'member_metrics' => [
                'total_members' => $memberIds->count(),
                'new_members' => Member::where('created_by', $fieldOfficer->id)
                    ->whereBetween('created_at', [$startDate, $endDate])
                    ->count(),
                'active_members' => Member::whereIn('id', $memberIds)
                    ->whereHas('loans', function ($query) {
                        $query->where('status', 'active');
                    })->count()
            ],
            'loan_metrics' => [
                'loans_disbursed' => Loan::whereIn('member_id', $memberIds)
                    ->whereBetween('loan_date', [$startDate, $endDate])
                    ->count(),
                'total_disbursed_amount' => Loan::whereIn('member_id', $memberIds)
                    ->whereBetween('loan_date', [$startDate, $endDate])
                    ->sum('loan_amount'),
                'applications_submitted' => LoanApplication::whereIn('member_id', $memberIds)
                    ->whereBetween('applied_at', [$startDate, $endDate])
                    ->count()
            ],
            'collection_metrics' => [
                'total_collected' => Installment::where('collected_by', $fieldOfficer->id)
                    ->where('status', 'paid')
                    ->whereBetween('collection_date', [$startDate, $endDate])
                    ->sum('amount'),
                'installments_collected' => Installment::where('collected_by', $fieldOfficer->id)
                    ->where('status', 'paid')
                    ->whereBetween('collection_date', [$startDate, $endDate])
                    ->count(),
                'collection_efficiency' => $this->calculateCollectionEfficiency($fieldOfficer, $startDate, $endDate)
            ],
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ]
        ];
    }

    /**
     * Calculate collection efficiency for field officer
     */
    private function calculateCollectionEfficiency(User $fieldOfficer, Carbon $startDate, Carbon $endDate): float
    {
        $memberIds = Member::where('created_by', $fieldOfficer->id)->pluck('id');

        $totalDue = Installment::whereHas('loan', function ($query) use ($memberIds) {
            $query->whereIn('member_id', $memberIds);
        })
            ->whereBetween('installment_date', [$startDate, $endDate])
            ->sum('amount');

        $totalCollected = Installment::where('collected_by', $fieldOfficer->id)
            ->where('status', 'paid')
            ->whereBetween('collection_date', [$startDate, $endDate])
            ->sum('amount');

        return $totalDue > 0 ? ($totalCollected / $totalDue) * 100 : 0;
    }

    /**
     * Get member growth analysis
     */
    public function getMemberGrowthAnalysis(Branch $branch = null, int $months = 12): array
    {
        $query = Member::query();
        
        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $monthlyData = $query->select(
            DB::raw('YEAR(created_at) as year'),
            DB::raw('MONTH(created_at) as month'),
            DB::raw('COUNT(*) as new_members')
        )
            ->where('created_at', '>=', Carbon::now()->subMonths($months))
            ->groupBy('year', 'month')
            ->orderBy('year', 'asc')
            ->orderBy('month', 'asc')
            ->get();

        $totalMembers = $query->count();
        $activeMembers = $query->whereHas('loans', function ($q) {
            $q->where('status', 'active');
        })->count();

        return [
            'total_members' => $totalMembers,
            'active_members' => $activeMembers,
            'inactive_members' => $totalMembers - $activeMembers,
            'monthly_growth' => $monthlyData->map(function ($item) {
                return [
                    'period' => Carbon::create($item->year, $item->month)->format('M Y'),
                    'new_members' => $item->new_members,
                    'year' => $item->year,
                    'month' => $item->month
                ];
            }),
            'growth_rate' => $this->calculateGrowthRate($monthlyData),
            'average_monthly_growth' => $monthlyData->avg('new_members')
        ];
    }

    /**
     * Calculate growth rate
     */
    private function calculateGrowthRate($monthlyData): float
    {
        if ($monthlyData->count() < 2) {
            return 0;
        }

        $first = $monthlyData->first()->new_members;
        $last = $monthlyData->last()->new_members;

        return $first > 0 ? (($last - $first) / $first) * 100 : 0;
    }

    /**
     * Get default rate analysis
     */
    public function getDefaultRateAnalysis(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null): array
    {
        $startDate = $startDate ?? Carbon::now()->subYear();
        $endDate = $endDate ?? Carbon::now();

        $query = Loan::query();
        
        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $loans = $query->whereBetween('loan_date', [$startDate, $endDate])->get();

        $totalLoans = $loans->count();
        $defaultedLoans = $loans->where('status', 'defaulted')->count();
        $defaultRate = $totalLoans > 0 ? ($defaultedLoans / $totalLoans) * 100 : 0;

        // Monthly default analysis
        $monthlyDefaults = $loans->where('status', 'defaulted')
            ->groupBy(function ($loan) {
                return $loan->created_at->format('Y-m');
            })
            ->map(function ($group, $month) {
                return [
                    'month' => $month,
                    'defaults' => $group->count(),
                    'amount' => $group->sum('loan_amount')
                ];
            });

        return [
            'total_loans' => $totalLoans,
            'defaulted_loans' => $defaultedLoans,
            'default_rate' => round($defaultRate, 2),
            'total_default_amount' => $loans->where('status', 'defaulted')->sum('loan_amount'),
            'monthly_defaults' => $monthlyDefaults->values(),
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ]
        ];
    }

    /**
     * Get collection efficiency report
     */
    public function getCollectionEfficiencyReport(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null): array
    {
        $startDate = $startDate ?? Carbon::now()->startOfMonth();
        $endDate = $endDate ?? Carbon::now()->endOfMonth();

        $query = Installment::query();
        
        if ($branch) {
            $query->whereHas('loan', function ($q) use ($branch) {
                $q->where('branch_id', $branch->id);
            });
        }

        $installments = $query->whereBetween('installment_date', [$startDate, $endDate])->get();

        $totalDue = $installments->sum('amount');
        $totalCollected = $installments->where('status', 'paid')->sum('amount');
        $collectionRate = $totalDue > 0 ? ($totalCollected / $totalDue) * 100 : 0;

        // On-time collection analysis
        $onTimeCollections = $installments->where('status', 'paid')
            ->filter(function ($installment) {
                return $installment->collection_date <= $installment->installment_date;
            });

        $onTimeRate = $installments->where('status', 'paid')->count() > 0 
            ? ($onTimeCollections->count() / $installments->where('status', 'paid')->count()) * 100 
            : 0;

        return [
            'total_due' => $totalDue,
            'total_collected' => $totalCollected,
            'collection_rate' => round($collectionRate, 2),
            'outstanding_amount' => $totalDue - $totalCollected,
            'on_time_collections' => $onTimeCollections->count(),
            'on_time_rate' => round($onTimeRate, 2),
            'overdue_installments' => $installments->where('status', 'pending')
                ->where('installment_date', '<', Carbon::now())->count(),
            'period' => [
                'start' => $startDate->format('Y-m-d'),
                'end' => $endDate->format('Y-m-d')
            ]
        ];
    }
}
