<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Carbon\Carbon;

class FileManagerService
{
    protected ImageManager $imageManager;
    protected SecurityService $securityService;

    // File type configurations
    protected array $imageTypes = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
    protected array $documentTypes = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'txt', 'csv'];
    
    // Image size configurations
    protected array $imageSizes = [
        'thumbnail' => ['width' => 150, 'height' => 150],
        'small' => ['width' => 300, 'height' => 300],
        'medium' => ['width' => 600, 'height' => 600],
        'large' => ['width' => 1200, 'height' => 1200],
    ];

    public function __construct(SecurityService $securityService)
    {
        $this->imageManager = new ImageManager(new Driver());
        $this->securityService = $securityService;
    }

    /**
     * Upload and process a file
     */
    public function uploadFile(
        UploadedFile $file, 
        string $category, 
        array $options = []
    ): array {
        try {
            // Validate file security
            $validation = $this->securityService->validateFileUpload($file);
            if (!$validation['valid']) {
                throw new \Exception('File validation failed: ' . implode(', ', $validation['errors']));
            }

            // Determine file type and storage disk
            $fileType = $this->determineFileType($file);
            $disk = $this->getDiskForFileType($fileType);
            
            // Generate unique filename
            $filename = $this->generateUniqueFilename($file, $category, $options);
            
            // Create directory structure
            $directory = $this->createDirectoryStructure($category, $fileType);
            $fullPath = $directory . '/' . $filename;

            if ($fileType === 'image') {
                return $this->processImageUpload($file, $fullPath, $disk, $options);
            } else {
                return $this->processDocumentUpload($file, $fullPath, $disk, $options);
            }

        } catch (\Exception $e) {
            Log::error('File upload failed', [
                'error' => $e->getMessage(),
                'file' => $file->getClientOriginalName(),
                'category' => $category
            ]);
            
            throw $e;
        }
    }

    /**
     * Process image upload with multiple sizes
     */
    protected function processImageUpload(
        UploadedFile $file, 
        string $path, 
        string $disk, 
        array $options
    ): array {
        $results = [];
        
        // Store original image
        $originalPath = Storage::disk($disk)->putFileAs(
            dirname($path), 
            $file, 
            basename($path)
        );
        
        $results['original'] = [
            'path' => $originalPath,
            'url' => Storage::disk($disk)->url($originalPath),
            'size' => $file->getSize(),
            'disk' => $disk
        ];

        // Generate different sizes
        $generateSizes = $options['sizes'] ?? ['thumbnail', 'medium'];
        
        foreach ($generateSizes as $sizeName) {
            if (isset($this->imageSizes[$sizeName])) {
                $resizedPath = $this->generateResizedImage(
                    $originalPath, 
                    $sizeName, 
                    $disk, 
                    $options
                );
                
                if ($resizedPath) {
                    $results[$sizeName] = [
                        'path' => $resizedPath,
                        'url' => Storage::disk($disk)->url($resizedPath),
                        'disk' => $disk
                    ];
                }
            }
        }

        // Add watermark if requested
        if ($options['watermark'] ?? false) {
            $watermarkedPath = $this->addWatermark($originalPath, $disk, $options);
            if ($watermarkedPath) {
                $results['watermarked'] = [
                    'path' => $watermarkedPath,
                    'url' => Storage::disk($disk)->url($watermarkedPath),
                    'disk' => $disk
                ];
            }
        }

        // Clean EXIF data for security
        $this->cleanImageExifData($originalPath, $disk);

        return $results;
    }

    /**
     * Process document upload
     */
    protected function processDocumentUpload(
        UploadedFile $file, 
        string $path, 
        string $disk, 
        array $options
    ): array {
        // Store document
        $storedPath = Storage::disk($disk)->putFileAs(
            dirname($path), 
            $file, 
            basename($path)
        );

        $result = [
            'original' => [
                'path' => $storedPath,
                'size' => $file->getSize(),
                'disk' => $disk,
                'mime_type' => $file->getMimeType()
            ]
        ];

        // Create backup if versioning is enabled
        if ($options['versioning'] ?? false) {
            $backupPath = $this->createFileBackup($storedPath, $disk);
            if ($backupPath) {
                $result['backup'] = [
                    'path' => $backupPath,
                    'disk' => 'backups'
                ];
            }
        }

        return $result;
    }

    /**
     * Generate resized image
     */
    protected function generateResizedImage(
        string $originalPath, 
        string $sizeName, 
        string $disk, 
        array $options
    ): ?string {
        try {
            $sizeConfig = $this->imageSizes[$sizeName];
            $originalFullPath = Storage::disk($disk)->path($originalPath);
            
            // Load and resize image
            $image = $this->imageManager->read($originalFullPath);
            
            // Resize maintaining aspect ratio
            $image->scale(
                width: $sizeConfig['width'],
                height: $sizeConfig['height']
            );

            // Apply optimization
            if ($options['optimize'] ?? true) {
                $image->optimize();
            }

            // Generate resized filename
            $pathInfo = pathinfo($originalPath);
            $resizedFilename = $pathInfo['filename'] . '_' . $sizeName . '.' . $pathInfo['extension'];
            $resizedPath = $pathInfo['dirname'] . '/' . $resizedFilename;
            
            // Save resized image
            $resizedFullPath = Storage::disk($disk)->path($resizedPath);
            $image->save($resizedFullPath);
            
            return $resizedPath;
            
        } catch (\Exception $e) {
            Log::error('Image resize failed', [
                'error' => $e->getMessage(),
                'original_path' => $originalPath,
                'size' => $sizeName
            ]);
            
            return null;
        }
    }

    /**
     * Add watermark to image
     */
    protected function addWatermark(string $imagePath, string $disk, array $options): ?string
    {
        try {
            $watermarkText = $options['watermark_text'] ?? config('app.name');
            $originalFullPath = Storage::disk($disk)->path($imagePath);
            
            // Load image
            $image = $this->imageManager->read($originalFullPath);
            
            // Add text watermark
            $image->text($watermarkText, 50, 50, function ($font) {
                $font->size(24);
                $font->color('rgba(255, 255, 255, 0.7)');
                $font->align('left');
                $font->valign('top');
            });

            // Generate watermarked filename
            $pathInfo = pathinfo($imagePath);
            $watermarkedFilename = $pathInfo['filename'] . '_watermarked.' . $pathInfo['extension'];
            $watermarkedPath = $pathInfo['dirname'] . '/' . $watermarkedFilename;
            
            // Save watermarked image
            $watermarkedFullPath = Storage::disk($disk)->path($watermarkedPath);
            $image->save($watermarkedFullPath);
            
            return $watermarkedPath;
            
        } catch (\Exception $e) {
            Log::error('Watermark addition failed', [
                'error' => $e->getMessage(),
                'image_path' => $imagePath
            ]);
            
            return null;
        }
    }

    /**
     * Clean EXIF data from image for security
     */
    protected function cleanImageExifData(string $imagePath, string $disk): void
    {
        try {
            $fullPath = Storage::disk($disk)->path($imagePath);
            
            // Load and save image without EXIF data
            $image = $this->imageManager->read($fullPath);
            $image->save($fullPath);
            
        } catch (\Exception $e) {
            Log::warning('EXIF cleaning failed', [
                'error' => $e->getMessage(),
                'image_path' => $imagePath
            ]);
        }
    }

    /**
     * Create file backup for versioning
     */
    protected function createFileBackup(string $filePath, string $sourceDisk): ?string
    {
        try {
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $pathInfo = pathinfo($filePath);
            $backupFilename = $pathInfo['filename'] . '_backup_' . $timestamp . '.' . $pathInfo['extension'];
            $backupPath = 'file-backups/' . date('Y/m') . '/' . $backupFilename;
            
            // Copy file to backup disk
            $fileContent = Storage::disk($sourceDisk)->get($filePath);
            Storage::disk('backups')->put($backupPath, $fileContent);
            
            return $backupPath;
            
        } catch (\Exception $e) {
            Log::error('File backup failed', [
                'error' => $e->getMessage(),
                'file_path' => $filePath
            ]);
            
            return null;
        }
    }

    /**
     * Determine file type based on extension
     */
    protected function determineFileType(UploadedFile $file): string
    {
        $extension = strtolower($file->getClientOriginalExtension());
        
        if (in_array($extension, $this->imageTypes)) {
            return 'image';
        } elseif (in_array($extension, $this->documentTypes)) {
            return 'document';
        }
        
        return 'other';
    }

    /**
     * Get appropriate disk for file type
     */
    protected function getDiskForFileType(string $fileType): string
    {
        return match ($fileType) {
            'image' => 'images',
            'document' => 'documents',
            default => 'public'
        };
    }

    /**
     * Generate unique filename
     */
    protected function generateUniqueFilename(UploadedFile $file, string $category, array $options): string
    {
        $extension = $file->getClientOriginalExtension();
        $prefix = $options['prefix'] ?? $category;
        $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
        $random = Str::random(8);
        
        return $prefix . '_' . $timestamp . '_' . $random . '.' . $extension;
    }

    /**
     * Create directory structure
     */
    protected function createDirectoryStructure(string $category, string $fileType): string
    {
        $year = date('Y');
        $month = date('m');

        return $category . '/' . $fileType . '/' . $year . '/' . $month;
    }

    /**
     * Delete file and all its variants
     */
    public function deleteFile(array $fileData): bool
    {
        try {
            $deleted = true;

            foreach ($fileData as $variant => $data) {
                if (isset($data['path']) && isset($data['disk'])) {
                    if (!Storage::disk($data['disk'])->delete($data['path'])) {
                        $deleted = false;
                        Log::warning('Failed to delete file variant', [
                            'variant' => $variant,
                            'path' => $data['path'],
                            'disk' => $data['disk']
                        ]);
                    }
                }
            }

            return $deleted;

        } catch (\Exception $e) {
            Log::error('File deletion failed', [
                'error' => $e->getMessage(),
                'file_data' => $fileData
            ]);

            return false;
        }
    }

    /**
     * Get secure download URL for private files
     */
    public function getSecureDownloadUrl(string $filePath, string $disk, int $expirationMinutes = 60): string
    {
        if (Storage::disk($disk)->exists($filePath)) {
            // For private disks, generate temporary URL
            if (in_array($disk, ['documents', 'local', 'backups'])) {
                return route('secure-file-download', [
                    'disk' => $disk,
                    'path' => encrypt($filePath),
                    'expires' => encrypt(now()->addMinutes($expirationMinutes)->timestamp)
                ]);
            }

            // For public disks, return direct URL
            return Storage::disk($disk)->url($filePath);
        }

        throw new \Exception('File not found');
    }

    /**
     * Validate file access permissions
     */
    public function canAccessFile(string $filePath, string $disk, $user): bool
    {
        // Implement your access control logic here
        // This is a basic example - customize based on your needs

        if ($user->hasRole('admin')) {
            return true;
        }

        // Check if file belongs to user's branch
        if (str_contains($filePath, 'members/') && $user->hasRole('field-officer')) {
            return true;
        }

        // Add more specific access rules as needed
        return false;
    }

    /**
     * Get file information
     */
    public function getFileInfo(string $filePath, string $disk): array
    {
        if (!Storage::disk($disk)->exists($filePath)) {
            throw new \Exception('File not found');
        }

        $fullPath = Storage::disk($disk)->path($filePath);

        return [
            'path' => $filePath,
            'disk' => $disk,
            'size' => Storage::disk($disk)->size($filePath),
            'last_modified' => Storage::disk($disk)->lastModified($filePath),
            'mime_type' => Storage::disk($disk)->mimeType($filePath),
            'exists' => true,
            'url' => $this->getFileUrl($filePath, $disk)
        ];
    }

    /**
     * Get file URL (public or secure)
     */
    protected function getFileUrl(string $filePath, string $disk): string
    {
        if (in_array($disk, ['images', 'public'])) {
            return Storage::disk($disk)->url($filePath);
        }

        // Return placeholder for private files
        return route('secure-file-download', [
            'disk' => $disk,
            'path' => encrypt($filePath)
        ]);
    }

    /**
     * Cleanup temporary files
     */
    public function cleanupTempFiles(int $olderThanHours = 24): int
    {
        $deletedCount = 0;
        $cutoffTime = now()->subHours($olderThanHours);

        try {
            $files = Storage::disk('temp')->allFiles();

            foreach ($files as $file) {
                $lastModified = Storage::disk('temp')->lastModified($file);

                if ($lastModified < $cutoffTime->timestamp) {
                    if (Storage::disk('temp')->delete($file)) {
                        $deletedCount++;
                    }
                }
            }

            Log::info('Temporary files cleanup completed', [
                'deleted_count' => $deletedCount,
                'cutoff_time' => $cutoffTime
            ]);

        } catch (\Exception $e) {
            Log::error('Temporary files cleanup failed', [
                'error' => $e->getMessage()
            ]);
        }

        return $deletedCount;
    }

    /**
     * Get storage usage statistics
     */
    public function getStorageStats(): array
    {
        $stats = [];
        $disks = ['images', 'documents', 'public', 'temp', 'backups'];

        foreach ($disks as $disk) {
            try {
                $files = Storage::disk($disk)->allFiles();
                $totalSize = 0;

                foreach ($files as $file) {
                    $totalSize += Storage::disk($disk)->size($file);
                }

                $stats[$disk] = [
                    'file_count' => count($files),
                    'total_size' => $totalSize,
                    'total_size_human' => $this->formatBytes($totalSize)
                ];

            } catch (\Exception $e) {
                $stats[$disk] = [
                    'error' => $e->getMessage()
                ];
            }
        }

        return $stats;
    }

    /**
     * Format bytes to human readable format
     */
    protected function formatBytes(int $bytes, int $precision = 2): string
    {
        $units = ['B', 'KB', 'MB', 'GB', 'TB'];

        for ($i = 0; $bytes > 1024 && $i < count($units) - 1; $i++) {
            $bytes /= 1024;
        }

        return round($bytes, $precision) . ' ' . $units[$i];
    }

    /**
     * Quarantine suspicious file
     */
    public function quarantineFile(string $filePath, string $sourceDisk, string $reason): bool
    {
        try {
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            $pathInfo = pathinfo($filePath);
            $quarantineFilename = $pathInfo['filename'] . '_quarantined_' . $timestamp . '.' . $pathInfo['extension'];
            $quarantinePath = 'quarantined/' . date('Y/m') . '/' . $quarantineFilename;

            // Move file to quarantine
            $fileContent = Storage::disk($sourceDisk)->get($filePath);
            Storage::disk('quarantine')->put($quarantinePath, $fileContent);

            // Delete from original location
            Storage::disk($sourceDisk)->delete($filePath);

            // Log quarantine action
            Log::warning('File quarantined', [
                'original_path' => $filePath,
                'quarantine_path' => $quarantinePath,
                'reason' => $reason,
                'timestamp' => $timestamp
            ]);

            return true;

        } catch (\Exception $e) {
            Log::error('File quarantine failed', [
                'error' => $e->getMessage(),
                'file_path' => $filePath,
                'reason' => $reason
            ]);

            return false;
        }
    }
}
