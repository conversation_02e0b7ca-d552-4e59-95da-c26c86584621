<?php

namespace App\Services;

use Barryvdh\DomPDF\Facade\Pdf;
use App\Models\Branch;
use App\Models\Member;
use App\Models\User;
use Carbon\Carbon;

class PDFReportService
{
    private $reportingService;

    public function __construct(ReportingService $reportingService)
    {
        $this->reportingService = $reportingService;
    }

    /**
     * Generate branch financial report PDF
     */
    public function generateBranchFinancialReport(Branch $branch, Carbon $startDate, Carbon $endDate): \Illuminate\Http\Response
    {
        $financialData = $this->reportingService->getBranchFinancialSummary($branch, $startDate, $endDate);
        $portfolioData = $this->reportingService->getLoanPortfolioAnalysis($branch);
        $collectionData = $this->reportingService->getCollectionEfficiencyReport($branch, $startDate, $endDate);

        $data = [
            'branch' => $branch,
            'financial_summary' => $financialData,
            'portfolio_analysis' => $portfolioData,
            'collection_efficiency' => $collectionData,
            'report_date' => Carbon::now(),
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ]
        ];

        $pdf = Pdf::loadView('reports.pdf.branch-financial', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);

        $filename = "branch-financial-report-{$branch->code}-" . $startDate->format('Y-m-d') . "-to-" . $endDate->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Generate loan disbursement report PDF
     */
    public function generateLoanDisbursementReport(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null): \Illuminate\Http\Response
    {
        $startDate = $startDate ?? Carbon::now()->startOfMonth();
        $endDate = $endDate ?? Carbon::now()->endOfMonth();

        $query = \App\Models\Loan::with(['loanApplication.member.branch', 'loanApplication.reviewedBy']);
        
        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $loans = $query->whereBetween('loan_date', [$startDate, $endDate])
            ->orderBy('loan_date', 'desc')
            ->get();

        $summary = [
            'total_loans' => $loans->count(),
            'total_amount' => $loans->sum('loan_amount'),
            'average_loan_size' => $loans->count() > 0 ? $loans->avg('loan_amount') : 0,
            'by_branch' => $loans->groupBy('branch.name')->map(function ($group) {
                return [
                    'count' => $group->count(),
                    'amount' => $group->sum('loan_amount')
                ];
            })
        ];

        $data = [
            'loans' => $loans,
            'summary' => $summary,
            'branch' => $branch,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'report_date' => Carbon::now()
        ];

        $pdf = Pdf::loadView('reports.pdf.loan-disbursement', $data)
            ->setPaper('a4', 'landscape')
            ->setOptions([
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);

        $branchCode = $branch ? $branch->code : 'all-branches';
        $filename = "loan-disbursement-report-{$branchCode}-" . $startDate->format('Y-m-d') . "-to-" . $endDate->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Generate field officer performance report PDF
     */
    public function generateFieldOfficerPerformanceReport(User $fieldOfficer, Carbon $startDate, Carbon $endDate): \Illuminate\Http\Response
    {
        $performanceData = $this->reportingService->getFieldOfficerPerformance($fieldOfficer, $startDate, $endDate);
        
        // Get additional details
        $memberIds = \App\Models\Member::where('created_by', $fieldOfficer->id)->pluck('id');
        
        $recentMembers = \App\Models\Member::where('created_by', $fieldOfficer->id)
            ->whereBetween('created_at', [$startDate, $endDate])
            ->with('branch')
            ->orderBy('created_at', 'desc')
            ->get();

        $recentLoans = \App\Models\Loan::whereIn('member_id', $memberIds)
            ->whereBetween('loan_date', [$startDate, $endDate])
            ->with(['loanApplication.member'])
            ->orderBy('loan_date', 'desc')
            ->get();

        $collections = \App\Models\Installment::where('collected_by', $fieldOfficer->id)
            ->where('status', 'paid')
            ->whereBetween('collection_date', [$startDate, $endDate])
            ->with(['loan.loanApplication.member'])
            ->orderBy('collection_date', 'desc')
            ->get();

        $data = [
            'field_officer' => $fieldOfficer,
            'performance' => $performanceData,
            'recent_members' => $recentMembers,
            'recent_loans' => $recentLoans,
            'collections' => $collections,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'report_date' => Carbon::now()
        ];

        $pdf = Pdf::loadView('reports.pdf.field-officer-performance', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);

        $filename = "field-officer-performance-{$fieldOfficer->employee_id}-" . $startDate->format('Y-m-d') . "-to-" . $endDate->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Generate member directory report PDF
     */
    public function generateMemberDirectoryReport(Branch $branch = null): \Illuminate\Http\Response
    {
        $query = Member::with(['branch', 'createdBy', 'loans', 'savingAccounts']);
        
        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $members = $query->orderBy('member_id')->get();

        $summary = [
            'total_members' => $members->count(),
            'active_members' => $members->filter(function ($member) {
                return $member->loans->where('status', 'active')->count() > 0;
            })->count(),
            'by_branch' => $members->groupBy('branch.name')->map(function ($group) {
                return $group->count();
            }),
            'by_religion' => $members->groupBy('religion')->map(function ($group) {
                return $group->count();
            }),
            'average_age' => $members->avg(function ($member) {
                return Carbon::parse($member->date_of_birth)->age;
            })
        ];

        $data = [
            'members' => $members,
            'summary' => $summary,
            'branch' => $branch,
            'report_date' => Carbon::now()
        ];

        $pdf = Pdf::loadView('reports.pdf.member-directory', $data)
            ->setPaper('a4', 'landscape')
            ->setOptions([
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);

        $branchCode = $branch ? $branch->code : 'all-branches';
        $filename = "member-directory-{$branchCode}-" . Carbon::now()->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Generate outstanding loans report PDF
     */
    public function generateOutstandingLoansReport(Branch $branch = null): \Illuminate\Http\Response
    {
        $query = \App\Models\Loan::with(['loanApplication.member.branch', 'installments'])
            ->where('status', 'active');
        
        if ($branch) {
            $query->where('branch_id', $branch->id);
        }

        $loans = $query->orderBy('loan_date', 'desc')->get();

        // Calculate overdue amounts
        $overdueLoans = $loans->filter(function ($loan) {
            return $loan->installments->where('status', 'pending')
                ->where('installment_date', '<', Carbon::now())
                ->count() > 0;
        });

        $summary = [
            'total_loans' => $loans->count(),
            'total_outstanding' => $loans->sum('remaining_amount'),
            'overdue_loans' => $overdueLoans->count(),
            'overdue_amount' => $overdueLoans->sum('remaining_amount'),
            'by_duration' => $loans->groupBy('loan_duration_months')->map(function ($group, $duration) {
                return [
                    'count' => $group->count(),
                    'amount' => $group->sum('remaining_amount')
                ];
            }),
            'portfolio_at_risk' => $overdueLoans->sum('remaining_amount')
        ];

        $data = [
            'loans' => $loans,
            'overdue_loans' => $overdueLoans,
            'summary' => $summary,
            'branch' => $branch,
            'report_date' => Carbon::now()
        ];

        $pdf = Pdf::loadView('reports.pdf.outstanding-loans', $data)
            ->setPaper('a4', 'landscape')
            ->setOptions([
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);

        $branchCode = $branch ? $branch->code : 'all-branches';
        $filename = "outstanding-loans-report-{$branchCode}-" . Carbon::now()->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Generate profit/loss statement PDF
     */
    public function generateProfitLossStatement(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null): \Illuminate\Http\Response
    {
        $startDate = $startDate ?? Carbon::now()->startOfMonth();
        $endDate = $endDate ?? Carbon::now()->endOfMonth();

        $financialData = $this->reportingService->getBranchFinancialSummary($branch, $startDate, $endDate);

        // Calculate additional P&L items
        $revenue = [
            'interest_income' => $financialData['interest_income'],
            'service_charges' => 0, // Would be calculated from fee transactions
            'other_income' => 0,
            'total_revenue' => $financialData['interest_income']
        ];

        $expenses = [
            'staff_salaries' => 0, // Would be calculated from payroll
            'office_rent' => 0,
            'utilities' => 0,
            'administrative_expenses' => 0,
            'loan_loss_provision' => 0,
            'other_expenses' => 0,
            'total_expenses' => 0
        ];

        $netIncome = $revenue['total_revenue'] - $expenses['total_expenses'];

        $data = [
            'branch' => $branch,
            'revenue' => $revenue,
            'expenses' => $expenses,
            'net_income' => $netIncome,
            'financial_data' => $financialData,
            'period' => [
                'start' => $startDate,
                'end' => $endDate
            ],
            'report_date' => Carbon::now()
        ];

        $pdf = Pdf::loadView('reports.pdf.profit-loss', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);

        $branchCode = $branch ? $branch->code : 'consolidated';
        $filename = "profit-loss-statement-{$branchCode}-" . $startDate->format('Y-m-d') . "-to-" . $endDate->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }

    /**
     * Generate member loan history report PDF
     */
    public function generateMemberLoanHistoryReport(Member $member): \Illuminate\Http\Response
    {
        $loans = $member->loans()->with(['loanApplication', 'installments.collectedBy'])->orderBy('loan_date', 'desc')->get();

        $summary = [
            'total_loans' => $loans->count(),
            'total_borrowed' => $loans->sum('loan_amount'),
            'total_paid' => $loans->sum('paid_amount'),
            'current_outstanding' => $loans->where('status', 'active')->sum('remaining_amount'),
            'completed_loans' => $loans->where('status', 'completed')->count(),
            'active_loans' => $loans->where('status', 'active')->count()
        ];

        $data = [
            'member' => $member,
            'loans' => $loans,
            'summary' => $summary,
            'report_date' => Carbon::now()
        ];

        $pdf = Pdf::loadView('reports.pdf.member-loan-history', $data)
            ->setPaper('a4', 'portrait')
            ->setOptions([
                'defaultFont' => 'sans-serif',
                'isHtml5ParserEnabled' => true,
                'isRemoteEnabled' => true
            ]);

        $filename = "member-loan-history-{$member->member_id}-" . Carbon::now()->format('Y-m-d') . ".pdf";
        
        return $pdf->download($filename);
    }
}
