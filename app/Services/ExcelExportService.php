<?php

namespace App\Services;

use Maatwebsite\Excel\Facades\Excel;
use App\Exports\MembersExport;
use App\Exports\LoansExport;
use App\Exports\InstallmentsExport;
use App\Exports\FinancialReportExport;
use App\Exports\PerformanceReportExport;
use App\Models\Branch;
use App\Models\User;
use Carbon\Carbon;

class ExcelExportService
{
    /**
     * Export members data to Excel
     */
    public function exportMembers(Branch $branch = null, array $filters = []): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'members-export-' . Carbon::now()->format('Y-m-d-H-i-s') . '.xlsx';
        
        return Excel::download(new MembersExport($branch, $filters), $filename);
    }

    /**
     * Export loans data to Excel
     */
    public function exportLoans(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null, array $filters = []): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'loans-export-' . Carbon::now()->format('Y-m-d-H-i-s') . '.xlsx';
        
        return Excel::download(new LoansExport($branch, $startDate, $endDate, $filters), $filename);
    }

    /**
     * Export installments data to Excel
     */
    public function exportInstallments(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null, array $filters = []): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'installments-export-' . Carbon::now()->format('Y-m-d-H-i-s') . '.xlsx';
        
        return Excel::download(new InstallmentsExport($branch, $startDate, $endDate, $filters), $filename);
    }

    /**
     * Export financial report to Excel
     */
    public function exportFinancialReport(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $startDate = $startDate ?? Carbon::now()->startOfMonth();
        $endDate = $endDate ?? Carbon::now()->endOfMonth();
        
        $branchCode = $branch ? $branch->code : 'all-branches';
        $filename = "financial-report-{$branchCode}-" . $startDate->format('Y-m-d') . "-to-" . $endDate->format('Y-m-d') . '.xlsx';
        
        return Excel::download(new FinancialReportExport($branch, $startDate, $endDate), $filename);
    }

    /**
     * Export performance report to Excel
     */
    public function exportPerformanceReport(User $fieldOfficer = null, Carbon $startDate = null, Carbon $endDate = null): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $startDate = $startDate ?? Carbon::now()->startOfMonth();
        $endDate = $endDate ?? Carbon::now()->endOfMonth();
        
        $officerCode = $fieldOfficer ? $fieldOfficer->employee_id : 'all-officers';
        $filename = "performance-report-{$officerCode}-" . $startDate->format('Y-m-d') . "-to-" . $endDate->format('Y-m-d') . '.xlsx';
        
        return Excel::download(new PerformanceReportExport($fieldOfficer, $startDate, $endDate), $filename);
    }

    /**
     * Export members to CSV
     */
    public function exportMembersCSV(Branch $branch = null, array $filters = []): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'members-export-' . Carbon::now()->format('Y-m-d-H-i-s') . '.csv';
        
        return Excel::download(new MembersExport($branch, $filters), $filename, \Maatwebsite\Excel\Excel::CSV);
    }

    /**
     * Export loans to CSV
     */
    public function exportLoansCSV(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null, array $filters = []): \Symfony\Component\HttpFoundation\BinaryFileResponse
    {
        $filename = 'loans-export-' . Carbon::now()->format('Y-m-d-H-i-s') . '.csv';
        
        return Excel::download(new LoansExport($branch, $startDate, $endDate, $filters), $filename, \Maatwebsite\Excel\Excel::CSV);
    }
}
