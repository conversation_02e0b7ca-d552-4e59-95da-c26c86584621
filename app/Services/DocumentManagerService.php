<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Carbon\Carbon;

class DocumentManagerService
{
    protected SecurityService $securityService;
    
    // Document categories and their settings
    protected array $documentCategories = [
        'reports' => [
            'max_size' => 10 * 1024 * 1024, // 10MB
            'allowed_types' => ['pdf', 'doc', 'docx'],
            'versioning' => true,
            'encryption' => false
        ],
        'member_documents' => [
            'max_size' => 5 * 1024 * 1024, // 5MB
            'allowed_types' => ['pdf', 'jpg', 'jpeg', 'png'],
            'versioning' => true,
            'encryption' => true
        ],
        'financial_reports' => [
            'max_size' => 20 * 1024 * 1024, // 20MB
            'allowed_types' => ['pdf', 'xls', 'xlsx', 'csv'],
            'versioning' => true,
            'encryption' => true
        ],
        'system_backups' => [
            'max_size' => 100 * 1024 * 1024, // 100MB
            'allowed_types' => ['zip', 'sql', 'json'],
            'versioning' => false,
            'encryption' => true
        ]
    ];

    public function __construct(SecurityService $securityService)
    {
        $this->securityService = $securityService;
    }

    /**
     * Store document with versioning and security
     */
    public function storeDocument(
        UploadedFile $file, 
        string $category, 
        array $metadata = []
    ): array {
        try {
            // Validate category
            if (!isset($this->documentCategories[$category])) {
                throw new \Exception('Invalid document category: ' . $category);
            }
            
            $categoryConfig = $this->documentCategories[$category];
            
            // Validate file
            $this->validateDocument($file, $categoryConfig);
            
            // Generate document info
            $documentId = $this->generateDocumentId($category);
            $filename = $this->generateSecureFilename($file, $documentId);
            $path = $this->getDocumentPath($category, $filename);
            
            // Store document
            $storedPath = Storage::disk('documents')->putFileAs(
                dirname($path),
                $file,
                basename($path)
            );
            
            $result = [
                'document_id' => $documentId,
                'original_name' => $file->getClientOriginalName(),
                'stored_path' => $storedPath,
                'disk' => 'documents',
                'size' => $file->getSize(),
                'mime_type' => $file->getMimeType(),
                'category' => $category,
                'metadata' => $metadata,
                'created_at' => now(),
                'checksum' => $this->calculateChecksum($storedPath)
            ];
            
            // Create version record if versioning is enabled
            if ($categoryConfig['versioning']) {
                $result['version'] = $this->createDocumentVersion($result);
            }
            
            // Encrypt if required
            if ($categoryConfig['encryption']) {
                $result['encrypted'] = $this->encryptDocument($storedPath);
            }
            
            // Log document storage
            Log::info('Document stored successfully', [
                'document_id' => $documentId,
                'category' => $category,
                'size' => $file->getSize(),
                'original_name' => $file->getClientOriginalName()
            ]);
            
            return $result;
            
        } catch (\Exception $e) {
            Log::error('Document storage failed', [
                'error' => $e->getMessage(),
                'category' => $category,
                'file' => $file->getClientOriginalName()
            ]);
            
            throw $e;
        }
    }

    /**
     * Retrieve document with access control
     */
    public function retrieveDocument(string $documentId, $user): array
    {
        try {
            // Find document record (you would typically store this in database)
            $documentPath = $this->findDocumentPath($documentId);
            
            if (!$documentPath || !Storage::disk('documents')->exists($documentPath)) {
                throw new \Exception('Document not found');
            }
            
            // Check access permissions
            if (!$this->canAccessDocument($documentId, $user)) {
                throw new \Exception('Access denied');
            }
            
            // Get document info
            $info = $this->getDocumentInfo($documentPath);
            
            // Generate secure download URL
            $info['download_url'] = $this->generateSecureDownloadUrl($documentPath, $user);
            
            return $info;
            
        } catch (\Exception $e) {
            Log::error('Document retrieval failed', [
                'error' => $e->getMessage(),
                'document_id' => $documentId,
                'user_id' => $user->id ?? null
            ]);
            
            throw $e;
        }
    }

    /**
     * Create document version
     */
    protected function createDocumentVersion(array $documentData): array
    {
        try {
            $versionId = Str::uuid();
            $timestamp = Carbon::now()->format('Y-m-d_H-i-s');
            
            // Create version directory
            $versionPath = 'versions/' . $documentData['document_id'] . '/' . $versionId;
            
            // Copy document to version storage
            $sourceContent = Storage::disk('documents')->get($documentData['stored_path']);
            $versionFilePath = $versionPath . '/' . $timestamp . '_' . basename($documentData['stored_path']);
            
            Storage::disk('backups')->put($versionFilePath, $sourceContent);
            
            $versionData = [
                'version_id' => $versionId,
                'document_id' => $documentData['document_id'],
                'version_path' => $versionFilePath,
                'created_at' => now(),
                'size' => $documentData['size'],
                'checksum' => $documentData['checksum']
            ];
            
            Log::info('Document version created', $versionData);
            
            return $versionData;
            
        } catch (\Exception $e) {
            Log::error('Document versioning failed', [
                'error' => $e->getMessage(),
                'document_id' => $documentData['document_id']
            ]);
            
            return [];
        }
    }

    /**
     * Encrypt document (placeholder - implement based on your encryption needs)
     */
    protected function encryptDocument(string $documentPath): bool
    {
        try {
            // This is a placeholder for document encryption
            // Implement your encryption logic here
            
            Log::info('Document encryption completed', [
                'document_path' => $documentPath
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            Log::error('Document encryption failed', [
                'error' => $e->getMessage(),
                'document_path' => $documentPath
            ]);
            
            return false;
        }
    }

    /**
     * Validate document against category rules
     */
    protected function validateDocument(UploadedFile $file, array $categoryConfig): void
    {
        // Check file size
        if ($file->getSize() > $categoryConfig['max_size']) {
            throw new \Exception('File size exceeds maximum allowed size for this category');
        }
        
        // Check file type
        $extension = strtolower($file->getClientOriginalExtension());
        if (!in_array($extension, $categoryConfig['allowed_types'])) {
            throw new \Exception('File type not allowed for this category');
        }
        
        // Use security service for additional validation
        $validation = $this->securityService->validateFileUpload($file);
        if (!$validation['valid']) {
            throw new \Exception('Security validation failed: ' . implode(', ', $validation['errors']));
        }
    }

    /**
     * Generate unique document ID
     */
    protected function generateDocumentId(string $category): string
    {
        $prefix = strtoupper(substr($category, 0, 3));
        $timestamp = Carbon::now()->format('YmdHis');
        $random = Str::random(6);
        
        return $prefix . '_' . $timestamp . '_' . $random;
    }

    /**
     * Generate secure filename
     */
    protected function generateSecureFilename(UploadedFile $file, string $documentId): string
    {
        $extension = $file->getClientOriginalExtension();
        return $documentId . '.' . $extension;
    }

    /**
     * Get document storage path
     */
    protected function getDocumentPath(string $category, string $filename): string
    {
        $year = date('Y');
        $month = date('m');
        
        return $category . '/' . $year . '/' . $month . '/' . $filename;
    }

    /**
     * Calculate file checksum
     */
    protected function calculateChecksum(string $filePath): string
    {
        $fullPath = Storage::disk('documents')->path($filePath);
        return hash_file('sha256', $fullPath);
    }

    /**
     * Find document path by ID (placeholder - implement database lookup)
     */
    protected function findDocumentPath(string $documentId): ?string
    {
        // This would typically query your database
        // For now, return null as placeholder
        return null;
    }

    /**
     * Check document access permissions
     */
    protected function canAccessDocument(string $documentId, $user): bool
    {
        // Implement your access control logic
        if ($user->hasRole('admin')) {
            return true;
        }
        
        // Add more specific access rules
        return false;
    }

    /**
     * Get document information
     */
    protected function getDocumentInfo(string $documentPath): array
    {
        return [
            'path' => $documentPath,
            'size' => Storage::disk('documents')->size($documentPath),
            'last_modified' => Storage::disk('documents')->lastModified($documentPath),
            'mime_type' => Storage::disk('documents')->mimeType($documentPath),
            'checksum' => $this->calculateChecksum($documentPath)
        ];
    }

    /**
     * Generate secure download URL
     */
    protected function generateSecureDownloadUrl(string $documentPath, $user): string
    {
        $token = encrypt([
            'path' => $documentPath,
            'user_id' => $user->id,
            'expires' => now()->addHours(1)->timestamp
        ]);
        
        return route('secure-document-download', ['token' => $token]);
    }

    /**
     * Archive old documents
     */
    public function archiveOldDocuments(string $category, int $olderThanDays = 365): int
    {
        $archivedCount = 0;
        $cutoffDate = now()->subDays($olderThanDays);
        
        try {
            // This would typically query your database for old documents
            // For now, this is a placeholder implementation
            
            Log::info('Document archiving completed', [
                'category' => $category,
                'archived_count' => $archivedCount,
                'cutoff_date' => $cutoffDate
            ]);
            
        } catch (\Exception $e) {
            Log::error('Document archiving failed', [
                'error' => $e->getMessage(),
                'category' => $category
            ]);
        }
        
        return $archivedCount;
    }

    /**
     * Get document categories configuration
     */
    public function getDocumentCategories(): array
    {
        return $this->documentCategories;
    }

    /**
     * Validate document category
     */
    public function isValidCategory(string $category): bool
    {
        return isset($this->documentCategories[$category]);
    }
}
