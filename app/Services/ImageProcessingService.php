<?php

namespace App\Services;

use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;
use Intervention\Image\ImageManager;
use Intervention\Image\Drivers\Gd\Driver;
use Intervention\Image\Interfaces\ImageInterface;

class ImageProcessingService
{
    protected ImageManager $imageManager;
    
    // Image quality settings
    protected array $qualitySettings = [
        'thumbnail' => 70,
        'small' => 75,
        'medium' => 80,
        'large' => 85,
        'original' => 90
    ];

    // Watermark settings
    protected array $watermarkSettings = [
        'opacity' => 0.7,
        'position' => 'bottom-right',
        'margin' => 20,
        'font_size' => 16
    ];

    public function __construct()
    {
        $this->imageManager = new ImageManager(new Driver());
    }

    /**
     * Process member photo with specific requirements
     */
    public function processMemberPhoto(UploadedFile $file, string $memberId): array
    {
        $results = [];
        
        try {
            // Load original image
            $image = $this->imageManager->read($file->getPathname());
            
            // Auto-orient image based on EXIF data
            $image = $this->autoOrientImage($image);
            
            // Generate different sizes for member photo
            $sizes = [
                'thumbnail' => ['width' => 100, 'height' => 100],
                'profile' => ['width' => 200, 'height' => 200],
                'full' => ['width' => 400, 'height' => 400]
            ];
            
            foreach ($sizes as $sizeName => $dimensions) {
                $processedImage = clone $image;
                
                // Resize and crop to square
                $processedImage = $this->resizeAndCrop(
                    $processedImage, 
                    $dimensions['width'], 
                    $dimensions['height']
                );
                
                // Apply member photo specific optimizations
                $processedImage = $this->optimizeForWeb($processedImage, $sizeName);
                
                // Save processed image
                $filename = $memberId . '_' . $sizeName . '.jpg';
                $path = 'members/photos/' . date('Y/m') . '/' . $filename;
                
                $this->saveImage($processedImage, $path, 'images');
                
                $results[$sizeName] = [
                    'path' => $path,
                    'url' => Storage::disk('images')->url($path),
                    'disk' => 'images'
                ];
            }
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error('Member photo processing failed', [
                'error' => $e->getMessage(),
                'member_id' => $memberId,
                'file' => $file->getClientOriginalName()
            ]);
            
            throw $e;
        }
    }

    /**
     * Process advertisement image
     */
    public function processAdvertisementImage(UploadedFile $file, string $adId): array
    {
        $results = [];
        
        try {
            $image = $this->imageManager->read($file->getPathname());
            $image = $this->autoOrientImage($image);
            
            // Advertisement image sizes
            $sizes = [
                'banner' => ['width' => 1200, 'height' => 400],
                'sidebar' => ['width' => 300, 'height' => 250],
                'thumbnail' => ['width' => 150, 'height' => 150]
            ];
            
            foreach ($sizes as $sizeName => $dimensions) {
                $processedImage = clone $image;
                
                // Resize maintaining aspect ratio with background fill
                $processedImage = $this->resizeWithBackground(
                    $processedImage,
                    $dimensions['width'],
                    $dimensions['height'],
                    '#ffffff'
                );
                
                // Optimize for web
                $processedImage = $this->optimizeForWeb($processedImage, $sizeName);
                
                // Save processed image
                $filename = 'ad_' . $adId . '_' . $sizeName . '.jpg';
                $path = 'advertisements/' . date('Y/m') . '/' . $filename;
                
                $this->saveImage($processedImage, $path, 'images');
                
                $results[$sizeName] = [
                    'path' => $path,
                    'url' => Storage::disk('images')->url($path),
                    'disk' => 'images'
                ];
            }
            
            return $results;
            
        } catch (\Exception $e) {
            Log::error('Advertisement image processing failed', [
                'error' => $e->getMessage(),
                'ad_id' => $adId,
                'file' => $file->getClientOriginalName()
            ]);
            
            throw $e;
        }
    }

    /**
     * Auto-orient image based on EXIF data
     */
    protected function autoOrientImage(ImageInterface $image): ImageInterface
    {
        try {
            // Intervention Image v3 handles auto-orientation automatically
            // This method is kept for explicit control if needed
            return $image;
        } catch (\Exception $e) {
            Log::warning('Auto-orientation failed', ['error' => $e->getMessage()]);
            return $image;
        }
    }

    /**
     * Resize and crop image to exact dimensions
     */
    protected function resizeAndCrop(ImageInterface $image, int $width, int $height): ImageInterface
    {
        // Get current dimensions
        $currentWidth = $image->width();
        $currentHeight = $image->height();
        
        // Calculate aspect ratios
        $currentRatio = $currentWidth / $currentHeight;
        $targetRatio = $width / $height;
        
        if ($currentRatio > $targetRatio) {
            // Image is wider, scale by height and crop width
            $newHeight = $height;
            $newWidth = (int) ($height * $currentRatio);
        } else {
            // Image is taller, scale by width and crop height
            $newWidth = $width;
            $newHeight = (int) ($width / $currentRatio);
        }
        
        // Resize image
        $image->resize($newWidth, $newHeight);
        
        // Crop to exact dimensions from center
        $image->crop($width, $height, position: 'center');
        
        return $image;
    }

    /**
     * Resize with background fill
     */
    protected function resizeWithBackground(
        ImageInterface $image, 
        int $width, 
        int $height, 
        string $backgroundColor = '#ffffff'
    ): ImageInterface {
        // Calculate scaling to fit within dimensions
        $currentWidth = $image->width();
        $currentHeight = $image->height();
        
        $scaleX = $width / $currentWidth;
        $scaleY = $height / $currentHeight;
        $scale = min($scaleX, $scaleY);
        
        $newWidth = (int) ($currentWidth * $scale);
        $newHeight = (int) ($currentHeight * $scale);
        
        // Resize image
        $image->resize($newWidth, $newHeight);
        
        // Create canvas with background color
        $canvas = $this->imageManager->create($width, $height)->fill($backgroundColor);
        
        // Calculate position to center the image
        $x = (int) (($width - $newWidth) / 2);
        $y = (int) (($height - $newHeight) / 2);
        
        // Place image on canvas
        $canvas->place($image, 'top-left', $x, $y);
        
        return $canvas;
    }

    /**
     * Optimize image for web
     */
    protected function optimizeForWeb(ImageInterface $image, string $sizeType): ImageInterface
    {
        $quality = $this->qualitySettings[$sizeType] ?? $this->qualitySettings['medium'];
        
        // Apply sharpening for smaller images
        if (in_array($sizeType, ['thumbnail', 'small'])) {
            $image->sharpen(10);
        }
        
        // Convert to RGB if needed (removes alpha channel for JPEG)
        // This is handled automatically by Intervention Image v3
        
        return $image;
    }

    /**
     * Add watermark to image
     */
    public function addWatermark(
        ImageInterface $image, 
        string $text, 
        array $options = []
    ): ImageInterface {
        $settings = array_merge($this->watermarkSettings, $options);
        
        try {
            // Add text watermark
            $image->text($text, $settings['margin'], $settings['margin'], function ($font) use ($settings) {
                $font->size($settings['font_size']);
                $font->color('rgba(255, 255, 255, ' . $settings['opacity'] . ')');
                $font->align('left');
                $font->valign('bottom');
            });
            
            return $image;
            
        } catch (\Exception $e) {
            Log::warning('Watermark addition failed', [
                'error' => $e->getMessage(),
                'text' => $text
            ]);
            
            return $image;
        }
    }

    /**
     * Save image to storage
     */
    protected function saveImage(ImageInterface $image, string $path, string $disk): void
    {
        $fullPath = Storage::disk($disk)->path($path);
        
        // Ensure directory exists
        $directory = dirname($fullPath);
        if (!is_dir($directory)) {
            mkdir($directory, 0755, true);
        }
        
        // Save image as JPEG with optimization
        $image->toJpeg(quality: $this->qualitySettings['medium'])->save($fullPath);
    }

    /**
     * Generate image thumbnail on-the-fly
     */
    public function generateThumbnail(string $imagePath, string $disk, int $width = 150, int $height = 150): string
    {
        try {
            $pathInfo = pathinfo($imagePath);
            $thumbnailFilename = $pathInfo['filename'] . '_thumb_' . $width . 'x' . $height . '.jpg';
            $thumbnailPath = $pathInfo['dirname'] . '/' . $thumbnailFilename;
            
            // Check if thumbnail already exists
            if (Storage::disk($disk)->exists($thumbnailPath)) {
                return $thumbnailPath;
            }
            
            // Load original image
            $originalFullPath = Storage::disk($disk)->path($imagePath);
            $image = $this->imageManager->read($originalFullPath);
            
            // Generate thumbnail
            $thumbnail = $this->resizeAndCrop($image, $width, $height);
            $thumbnail = $this->optimizeForWeb($thumbnail, 'thumbnail');
            
            // Save thumbnail
            $this->saveImage($thumbnail, $thumbnailPath, $disk);
            
            return $thumbnailPath;
            
        } catch (\Exception $e) {
            Log::error('Thumbnail generation failed', [
                'error' => $e->getMessage(),
                'image_path' => $imagePath,
                'dimensions' => $width . 'x' . $height
            ]);
            
            throw $e;
        }
    }

    /**
     * Convert image format
     */
    public function convertImageFormat(
        string $imagePath, 
        string $disk, 
        string $targetFormat = 'jpg'
    ): string {
        try {
            $pathInfo = pathinfo($imagePath);
            $convertedFilename = $pathInfo['filename'] . '.' . $targetFormat;
            $convertedPath = $pathInfo['dirname'] . '/' . $convertedFilename;
            
            // Load original image
            $originalFullPath = Storage::disk($disk)->path($imagePath);
            $image = $this->imageManager->read($originalFullPath);
            
            // Save in new format
            $convertedFullPath = Storage::disk($disk)->path($convertedPath);
            
            switch (strtolower($targetFormat)) {
                case 'jpg':
                case 'jpeg':
                    $image->toJpeg(quality: 85)->save($convertedFullPath);
                    break;
                case 'png':
                    $image->toPng()->save($convertedFullPath);
                    break;
                case 'webp':
                    $image->toWebp(quality: 85)->save($convertedFullPath);
                    break;
                default:
                    throw new \Exception('Unsupported target format: ' . $targetFormat);
            }
            
            return $convertedPath;
            
        } catch (\Exception $e) {
            Log::error('Image format conversion failed', [
                'error' => $e->getMessage(),
                'image_path' => $imagePath,
                'target_format' => $targetFormat
            ]);
            
            throw $e;
        }
    }
}
