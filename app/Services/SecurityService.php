<?php

namespace App\Services;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Hash;
use App\Models\User;
use App\Models\ActivityLog;

class SecurityService
{
    /**
     * Log security-related activities
     */
    public function logActivity(string $action, array $data = [], User $user = null): void
    {
        $user = $user ?? auth()->user();
        
        $activityData = [
            'user_id' => $user?->id,
            'action' => $action,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'url' => request()->fullUrl(),
            'method' => request()->method(),
            'data' => $data,
            'timestamp' => now(),
        ];

        // Log to database
        ActivityLog::create($activityData);

        // Log to file for critical actions
        if ($this->isCriticalAction($action)) {
            Log::warning('Critical security action', $activityData);
        }
    }

    /**
     * Check if action is critical
     */
    private function isCriticalAction(string $action): bool
    {
        $criticalActions = [
            'login_failed',
            'password_changed',
            'role_changed',
            'user_created',
            'user_deleted',
            'loan_approved',
            'loan_rejected',
            'large_transaction',
            'data_export',
            'settings_changed'
        ];

        return in_array($action, $criticalActions);
    }

    /**
     * Validate password strength
     */
    public function validatePasswordStrength(string $password): array
    {
        $errors = [];
        
        if (strlen($password) < 8) {
            $errors[] = 'Password must be at least 8 characters long.';
        }

        if (!preg_match('/[a-z]/', $password)) {
            $errors[] = 'Password must contain at least one lowercase letter.';
        }

        if (!preg_match('/[A-Z]/', $password)) {
            $errors[] = 'Password must contain at least one uppercase letter.';
        }

        if (!preg_match('/[0-9]/', $password)) {
            $errors[] = 'Password must contain at least one number.';
        }

        if (!preg_match('/[^a-zA-Z0-9]/', $password)) {
            $errors[] = 'Password must contain at least one special character.';
        }

        // Check against common passwords
        if ($this->isCommonPassword($password)) {
            $errors[] = 'Password is too common. Please choose a more secure password.';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors,
            'strength' => $this->calculatePasswordStrength($password)
        ];
    }

    /**
     * Check if password is commonly used
     */
    private function isCommonPassword(string $password): bool
    {
        $commonPasswords = [
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'letmein', 'welcome', 'monkey',
            'dragon', 'master', 'shadow', 'superman', 'michael',
            'bangladesh', 'dhaka', 'chittagong', 'sylhet'
        ];

        return in_array(strtolower($password), $commonPasswords);
    }

    /**
     * Calculate password strength score
     */
    private function calculatePasswordStrength(string $password): int
    {
        $score = 0;
        
        // Length bonus
        $score += min(strlen($password) * 2, 20);
        
        // Character variety bonus
        if (preg_match('/[a-z]/', $password)) $score += 5;
        if (preg_match('/[A-Z]/', $password)) $score += 5;
        if (preg_match('/[0-9]/', $password)) $score += 5;
        if (preg_match('/[^a-zA-Z0-9]/', $password)) $score += 10;
        
        // Complexity bonus
        if (preg_match('/[a-z].*[A-Z]|[A-Z].*[a-z]/', $password)) $score += 5;
        if (preg_match('/[a-zA-Z].*[0-9]|[0-9].*[a-zA-Z]/', $password)) $score += 5;
        
        // Penalty for common patterns
        if (preg_match('/(.)\1{2,}/', $password)) $score -= 10; // Repeated characters
        if (preg_match('/123|abc|qwe/i', $password)) $score -= 10; // Sequential patterns
        
        return max(0, min(100, $score));
    }

    /**
     * Check for suspicious login attempts
     */
    public function checkSuspiciousLogin(Request $request): array
    {
        $ip = $request->ip();
        $email = $request->input('email');
        $userAgent = $request->userAgent();
        
        $suspiciousFactors = [];
        
        // Check for rapid login attempts
        $recentAttempts = Cache::get("login_attempts:{$ip}", 0);
        if ($recentAttempts > 3) {
            $suspiciousFactors[] = 'Multiple rapid login attempts from same IP';
        }
        
        // Check for multiple IPs for same email
        $emailAttempts = Cache::get("email_attempts:{$email}", []);
        if (count($emailAttempts) > 3) {
            $suspiciousFactors[] = 'Login attempts from multiple IP addresses';
        }
        
        // Check for unusual user agent
        if (empty($userAgent) || strlen($userAgent) < 10) {
            $suspiciousFactors[] = 'Suspicious or missing user agent';
        }
        
        // Check for known malicious patterns
        $maliciousPatterns = ['bot', 'crawler', 'scanner', 'hack', 'exploit'];
        foreach ($maliciousPatterns as $pattern) {
            if (stripos($userAgent, $pattern) !== false) {
                $suspiciousFactors[] = 'Potentially malicious user agent';
                break;
            }
        }
        
        // Check for geographic anomalies (simplified)
        $user = User::where('email', $email)->first();
        if ($user && $user->last_login_ip && $user->last_login_ip !== $ip) {
            // In a real implementation, you'd use a GeoIP service
            $suspiciousFactors[] = 'Login from different location';
        }
        
        return [
            'suspicious' => !empty($suspiciousFactors),
            'factors' => $suspiciousFactors,
            'risk_level' => $this->calculateRiskLevel($suspiciousFactors)
        ];
    }

    /**
     * Calculate risk level based on suspicious factors
     */
    private function calculateRiskLevel(array $factors): string
    {
        $count = count($factors);
        
        if ($count === 0) return 'low';
        if ($count <= 2) return 'medium';
        return 'high';
    }

    /**
     * Sanitize input data
     */
    public function sanitizeInput(array $data): array
    {
        $sanitized = [];
        
        foreach ($data as $key => $value) {
            if (is_string($value)) {
                // Remove potential XSS
                $value = strip_tags($value);
                
                // Remove potential SQL injection patterns
                $value = preg_replace('/[\'";\\\\]/', '', $value);
                
                // Trim whitespace
                $value = trim($value);
                
                $sanitized[$key] = $value;
            } elseif (is_array($value)) {
                $sanitized[$key] = $this->sanitizeInput($value);
            } else {
                $sanitized[$key] = $value;
            }
        }
        
        return $sanitized;
    }

    /**
     * Generate secure token
     */
    public function generateSecureToken(int $length = 32): string
    {
        return bin2hex(random_bytes($length / 2));
    }

    /**
     * Validate file upload security
     */
    public function validateFileUpload($file): array
    {
        $errors = [];
        
        if (!$file || !$file->isValid()) {
            $errors[] = 'Invalid file upload.';
            return ['valid' => false, 'errors' => $errors];
        }
        
        // Check file size (max 5MB)
        if ($file->getSize() > 5 * 1024 * 1024) {
            $errors[] = 'File size exceeds 5MB limit.';
        }
        
        // Check MIME type
        $allowedMimes = [
            'image/jpeg', 'image/png', 'image/jpg', 'image/gif',
            'application/pdf', 'text/plain', 'text/csv'
        ];
        
        if (!in_array($file->getMimeType(), $allowedMimes)) {
            $errors[] = 'File type not allowed.';
        }
        
        // Check for malicious content
        $content = file_get_contents($file->getPathname());
        $maliciousPatterns = [
            '/<\?php/i', '/<script/i', '/javascript:/i', '/eval\(/i'
        ];
        
        foreach ($maliciousPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $errors[] = 'Potentially malicious file content detected.';
                break;
            }
        }
        
        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Check for blocked IPs
     */
    public function isIpBlocked(string $ip): bool
    {
        return Cache::has("blocked_ip:{$ip}");
    }

    /**
     * Block IP address
     */
    public function blockIp(string $ip, int $minutes = 60): void
    {
        Cache::put("blocked_ip:{$ip}", true, now()->addMinutes($minutes));
        
        Log::warning('IP address blocked', [
            'ip' => $ip,
            'blocked_until' => now()->addMinutes($minutes),
            'reason' => 'Security violation'
        ]);
    }

    /**
     * Generate audit trail
     */
    public function generateAuditTrail(string $entity, int $entityId, string $action, array $changes = []): void
    {
        $auditData = [
            'user_id' => auth()->id(),
            'entity_type' => $entity,
            'entity_id' => $entityId,
            'action' => $action,
            'changes' => $changes,
            'ip_address' => request()->ip(),
            'user_agent' => request()->userAgent(),
            'created_at' => now()
        ];
        
        // Store in audit log table (you'd need to create this table)
        Log::info('Audit trail', $auditData);
    }

    /**
     * Encrypt sensitive data
     */
    public function encryptSensitiveData(string $data): string
    {
        return encrypt($data);
    }

    /**
     * Decrypt sensitive data
     */
    public function decryptSensitiveData(string $encryptedData): string
    {
        return decrypt($encryptedData);
    }
}
