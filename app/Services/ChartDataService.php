<?php

namespace App\Services;

use App\Models\Branch;
use App\Models\Member;
use App\Models\Loan;
use App\Models\Installment;
use App\Models\SavingTransaction;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;

class ChartDataService
{
    /**
     * Get monthly loan disbursement chart data
     */
    public function getMonthlyLoanDisbursementData(Branch $branch = null, int $months = 12): array
    {
        $cacheKey = "monthly_disbursement_" . ($branch ? $branch->id : 'all') . "_{$months}";
        
        return Cache::remember($cacheKey, 1800, function () use ($branch, $months) {
            $query = Loan::select(
                DB::raw('YEAR(loan_date) as year'),
                DB::raw('MONTH(loan_date) as month'),
                DB::raw('COUNT(*) as loan_count'),
                DB::raw('SUM(loan_amount) as total_amount')
            );

            if ($branch) {
                $query->where('branch_id', $branch->id);
            }

            $data = $query->where('loan_date', '>=', Carbon::now()->subMonths($months))
                ->groupBy('year', 'month')
                ->orderBy('year', 'asc')
                ->orderBy('month', 'asc')
                ->get();

            $labels = [];
            $loanCounts = [];
            $amounts = [];

            foreach ($data as $item) {
                $labels[] = Carbon::create($item->year, $item->month)->format('M Y');
                $loanCounts[] = $item->loan_count;
                $amounts[] = $item->total_amount;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => 'Number of Loans',
                        'data' => $loanCounts,
                        'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'borderWidth' => 2,
                        'yAxisID' => 'y'
                    ],
                    [
                        'label' => 'Amount (৳)',
                        'data' => $amounts,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 2,
                        'yAxisID' => 'y1'
                    ]
                ]
            ];
        });
    }

    /**
     * Get collection efficiency trend data
     */
    public function getCollectionEfficiencyTrendData(Branch $branch = null, int $months = 12): array
    {
        $cacheKey = "collection_efficiency_" . ($branch ? $branch->id : 'all') . "_{$months}";
        
        return Cache::remember($cacheKey, 1800, function () use ($branch, $months) {
            $labels = [];
            $efficiencyData = [];

            for ($i = $months - 1; $i >= 0; $i--) {
                $startDate = Carbon::now()->subMonths($i)->startOfMonth();
                $endDate = Carbon::now()->subMonths($i)->endOfMonth();
                
                $query = Installment::whereBetween('installment_date', [$startDate, $endDate]);
                
                if ($branch) {
                    $query->whereHas('loan', function ($q) use ($branch) {
                        $q->where('branch_id', $branch->id);
                    });
                }

                $totalDue = $query->sum('amount');
                $totalCollected = $query->where('status', 'paid')->sum('amount');
                
                $efficiency = $totalDue > 0 ? ($totalCollected / $totalDue) * 100 : 0;

                $labels[] = $startDate->format('M Y');
                $efficiencyData[] = round($efficiency, 2);
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => 'Collection Efficiency (%)',
                        'data' => $efficiencyData,
                        'backgroundColor' => 'rgba(75, 192, 192, 0.2)',
                        'borderColor' => 'rgba(75, 192, 192, 1)',
                        'borderWidth' => 2,
                        'fill' => true
                    ]
                ]
            ];
        });
    }

    /**
     * Get loan status distribution pie chart data
     */
    public function getLoanStatusDistributionData(Branch $branch = null): array
    {
        $cacheKey = "loan_status_distribution_" . ($branch ? $branch->id : 'all');
        
        return Cache::remember($cacheKey, 1800, function () use ($branch) {
            $query = Loan::select('status', DB::raw('COUNT(*) as count'));
            
            if ($branch) {
                $query->where('branch_id', $branch->id);
            }

            $data = $query->groupBy('status')->get();

            $labels = [];
            $counts = [];
            $colors = [
                'active' => '#36A2EB',
                'completed' => '#4BC0C0',
                'defaulted' => '#FF6384',
                'pending' => '#FFCE56',
                'rejected' => '#FF9F40'
            ];

            foreach ($data as $item) {
                $labels[] = ucfirst($item->status);
                $counts[] = $item->count;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'data' => $counts,
                        'backgroundColor' => array_values($colors),
                        'borderWidth' => 1
                    ]
                ]
            ];
        });
    }

    /**
     * Get member growth chart data
     */
    public function getMemberGrowthData(Branch $branch = null, int $months = 12): array
    {
        $cacheKey = "member_growth_" . ($branch ? $branch->id : 'all') . "_{$months}";
        
        return Cache::remember($cacheKey, 1800, function () use ($branch, $months) {
            $labels = [];
            $newMembers = [];
            $totalMembers = [];
            $runningTotal = 0;

            for ($i = $months - 1; $i >= 0; $i--) {
                $startDate = Carbon::now()->subMonths($i)->startOfMonth();
                $endDate = Carbon::now()->subMonths($i)->endOfMonth();
                
                $query = Member::whereBetween('created_at', [$startDate, $endDate]);
                
                if ($branch) {
                    $query->where('branch_id', $branch->id);
                }

                $monthlyNew = $query->count();
                $runningTotal += $monthlyNew;

                $labels[] = $startDate->format('M Y');
                $newMembers[] = $monthlyNew;
                $totalMembers[] = $runningTotal;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => 'New Members',
                        'data' => $newMembers,
                        'backgroundColor' => 'rgba(153, 102, 255, 0.2)',
                        'borderColor' => 'rgba(153, 102, 255, 1)',
                        'borderWidth' => 2,
                        'type' => 'bar'
                    ],
                    [
                        'label' => 'Total Members',
                        'data' => $totalMembers,
                        'backgroundColor' => 'rgba(255, 159, 64, 0.2)',
                        'borderColor' => 'rgba(255, 159, 64, 1)',
                        'borderWidth' => 2,
                        'type' => 'line'
                    ]
                ]
            ];
        });
    }

    /**
     * Get portfolio at risk trend data
     */
    public function getPortfolioAtRiskTrendData(Branch $branch = null, int $months = 12): array
    {
        $cacheKey = "par_trend_" . ($branch ? $branch->id : 'all') . "_{$months}";
        
        return Cache::remember($cacheKey, 1800, function () use ($branch, $months) {
            $labels = [];
            $parAmounts = [];
            $parPercentages = [];

            for ($i = $months - 1; $i >= 0; $i--) {
                $asOfDate = Carbon::now()->subMonths($i)->endOfMonth();
                
                $query = Loan::where('status', 'active');
                
                if ($branch) {
                    $query->where('branch_id', $branch->id);
                }

                $activeLoans = $query->get();
                $totalOutstanding = $activeLoans->sum('remaining_amount');

                // Calculate PAR (loans with overdue installments)
                $parAmount = 0;
                foreach ($activeLoans as $loan) {
                    $overdueInstallments = $loan->installments()
                        ->where('status', 'pending')
                        ->where('installment_date', '<', $asOfDate)
                        ->count();
                    
                    if ($overdueInstallments > 0) {
                        $parAmount += $loan->remaining_amount;
                    }
                }

                $parPercentage = $totalOutstanding > 0 ? ($parAmount / $totalOutstanding) * 100 : 0;

                $labels[] = $asOfDate->format('M Y');
                $parAmounts[] = $parAmount;
                $parPercentages[] = round($parPercentage, 2);
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => 'PAR Amount (৳)',
                        'data' => $parAmounts,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 2,
                        'yAxisID' => 'y'
                    ],
                    [
                        'label' => 'PAR Percentage (%)',
                        'data' => $parPercentages,
                        'backgroundColor' => 'rgba(54, 162, 235, 0.2)',
                        'borderColor' => 'rgba(54, 162, 235, 1)',
                        'borderWidth' => 2,
                        'yAxisID' => 'y1'
                    ]
                ]
            ];
        });
    }

    /**
     * Get branch comparison data
     */
    public function getBranchComparisonData(array $metrics = ['loans', 'members', 'collections']): array
    {
        $cacheKey = "branch_comparison_" . implode('_', $metrics);
        
        return Cache::remember($cacheKey, 1800, function () use ($metrics) {
            $branches = Branch::where('is_active', true)->get();
            $labels = $branches->pluck('name')->toArray();
            $datasets = [];

            if (in_array('loans', $metrics)) {
                $loanData = [];
                foreach ($branches as $branch) {
                    $loanData[] = Loan::where('branch_id', $branch->id)->count();
                }
                
                $datasets[] = [
                    'label' => 'Total Loans',
                    'data' => $loanData,
                    'backgroundColor' => 'rgba(54, 162, 235, 0.6)'
                ];
            }

            if (in_array('members', $metrics)) {
                $memberData = [];
                foreach ($branches as $branch) {
                    $memberData[] = Member::where('branch_id', $branch->id)->count();
                }
                
                $datasets[] = [
                    'label' => 'Total Members',
                    'data' => $memberData,
                    'backgroundColor' => 'rgba(255, 99, 132, 0.6)'
                ];
            }

            if (in_array('collections', $metrics)) {
                $collectionData = [];
                foreach ($branches as $branch) {
                    $amount = Installment::whereHas('loan', function ($q) use ($branch) {
                        $q->where('branch_id', $branch->id);
                    })
                    ->where('status', 'paid')
                    ->whereMonth('collection_date', Carbon::now()->month)
                    ->sum('amount');
                    
                    $collectionData[] = $amount;
                }
                
                $datasets[] = [
                    'label' => 'Monthly Collections (৳)',
                    'data' => $collectionData,
                    'backgroundColor' => 'rgba(75, 192, 192, 0.6)'
                ];
            }

            return [
                'labels' => $labels,
                'datasets' => $datasets
            ];
        });
    }

    /**
     * Get savings account trend data
     */
    public function getSavingsTrendData(Branch $branch = null, int $months = 12): array
    {
        $cacheKey = "savings_trend_" . ($branch ? $branch->id : 'all') . "_{$months}";
        
        return Cache::remember($cacheKey, 1800, function () use ($branch, $months) {
            $labels = [];
            $deposits = [];
            $withdrawals = [];
            $netFlow = [];

            for ($i = $months - 1; $i >= 0; $i--) {
                $startDate = Carbon::now()->subMonths($i)->startOfMonth();
                $endDate = Carbon::now()->subMonths($i)->endOfMonth();
                
                $depositQuery = SavingTransaction::where('transaction_type', 'deposit')
                    ->whereBetween('transaction_date', [$startDate, $endDate]);
                
                $withdrawalQuery = SavingTransaction::where('transaction_type', 'withdrawal')
                    ->whereBetween('transaction_date', [$startDate, $endDate]);
                
                if ($branch) {
                    $depositQuery->whereHas('savingAccount.member', function ($q) use ($branch) {
                        $q->where('branch_id', $branch->id);
                    });
                    
                    $withdrawalQuery->whereHas('savingAccount.member', function ($q) use ($branch) {
                        $q->where('branch_id', $branch->id);
                    });
                }

                $monthlyDeposits = $depositQuery->sum('amount');
                $monthlyWithdrawals = $withdrawalQuery->sum('amount');
                $monthlyNetFlow = $monthlyDeposits - $monthlyWithdrawals;

                $labels[] = $startDate->format('M Y');
                $deposits[] = $monthlyDeposits;
                $withdrawals[] = $monthlyWithdrawals;
                $netFlow[] = $monthlyNetFlow;
            }

            return [
                'labels' => $labels,
                'datasets' => [
                    [
                        'label' => 'Deposits (৳)',
                        'data' => $deposits,
                        'backgroundColor' => 'rgba(75, 192, 192, 0.2)',
                        'borderColor' => 'rgba(75, 192, 192, 1)',
                        'borderWidth' => 2
                    ],
                    [
                        'label' => 'Withdrawals (৳)',
                        'data' => $withdrawals,
                        'backgroundColor' => 'rgba(255, 99, 132, 0.2)',
                        'borderColor' => 'rgba(255, 99, 132, 1)',
                        'borderWidth' => 2
                    ],
                    [
                        'label' => 'Net Flow (৳)',
                        'data' => $netFlow,
                        'backgroundColor' => 'rgba(153, 102, 255, 0.2)',
                        'borderColor' => 'rgba(153, 102, 255, 1)',
                        'borderWidth' => 2
                    ]
                ]
            ];
        });
    }
}
