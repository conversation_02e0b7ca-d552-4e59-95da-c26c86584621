<?php

namespace App\Services;

use App\Models\User;
use App\Models\Member;
use App\Models\Loan;
use App\Models\LoanApplication;
use App\Models\Branch;
use Carbon\Carbon;

class BusinessRuleValidationService
{
    /**
     * Validate loan eligibility for a member
     */
    public function validateLoanEligibility(Member $member, float $requestedAmount): array
    {
        $errors = [];

        // Age validation
        $age = Carbon::parse($member->date_of_birth)->age;
        if ($age < 18 || $age > 65) {
            $errors[] = 'Member must be between 18 and 65 years old to be eligible for a loan.';
        }

        // Membership duration validation
        $membershipMonths = $member->created_at->diffInMonths(now());
        if ($membershipMonths < 3) {
            $errors[] = 'Member must be registered for at least 3 months before applying for a loan.';
        }

        // Active loan check
        $activeLoan = Loan::where('member_id', $member->id)
            ->whereIn('status', ['active', 'approved'])
            ->exists();
        
        if ($activeLoan) {
            $errors[] = 'Member already has an active loan.';
        }

        // Income-based loan limit
        $maxLoanAmount = $member->monthly_income * 10; // 10x monthly income
        if ($requestedAmount > $maxLoanAmount) {
            $errors[] = "Maximum loan amount for this member is ৳" . number_format($maxLoanAmount) . " (10x monthly income).";
        }

        // Previous loan performance check
        $defaultedLoans = Loan::where('member_id', $member->id)
            ->where('status', 'defaulted')
            ->count();
        
        if ($defaultedLoans > 0) {
            $errors[] = 'Member has defaulted loans and is not eligible for new loans.';
        }

        // Check for recent loan applications
        $recentApplications = LoanApplication::where('member_id', $member->id)
            ->where('created_at', '>', now()->subDays(30))
            ->where('status', 'rejected')
            ->count();
        
        if ($recentApplications >= 3) {
            $errors[] = 'Member has too many recent rejected applications. Please wait before applying again.';
        }

        return [
            'eligible' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate branch assignment for user
     */
    public function validateBranchAssignment(User $user, int $branchId): array
    {
        $errors = [];
        $branch = Branch::find($branchId);

        if (!$branch) {
            $errors[] = 'Selected branch does not exist.';
            return ['valid' => false, 'errors' => $errors];
        }

        // Check if branch is active
        if (!$branch->is_active) {
            $errors[] = 'Cannot assign users to inactive branches.';
        }

        // Manager assignment validation
        if ($user->role === 'manager') {
            $existingManager = User::where('role', 'manager')
                ->where('branch_id', $branchId)
                ->where('id', '!=', $user->id)
                ->exists();
            
            if ($existingManager) {
                $errors[] = 'This branch already has a manager assigned.';
            }
        }

        // Field officer capacity check
        if ($user->role === 'field_officer') {
            $fieldOfficerCount = User::where('role', 'field_officer')
                ->where('branch_id', $branchId)
                ->where('is_active', true)
                ->where('id', '!=', $user->id)
                ->count();
            
            if ($fieldOfficerCount >= 10) {
                $errors[] = 'This branch already has the maximum number of field officers (10).';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate role change restrictions
     */
    public function validateRoleChange(User $user, string $newRole, User $currentUser): array
    {
        $errors = [];

        // Self role change restrictions
        if ($user->id === $currentUser->id) {
            $errors[] = 'You cannot change your own role.';
        }

        // Admin role restrictions
        if ($user->role === 'admin' && $currentUser->role !== 'admin') {
            $errors[] = 'Only admins can modify admin accounts.';
        }

        if ($newRole === 'admin' && $currentUser->role !== 'admin') {
            $errors[] = 'Only admins can assign admin role.';
        }

        // Manager restrictions
        if ($currentUser->role === 'manager') {
            $allowedRoles = ['field_officer'];
            if (!in_array($newRole, $allowedRoles)) {
                $errors[] = 'Managers can only assign field officer role.';
            }

            // Branch restriction for managers
            if ($user->branch_id !== $currentUser->branch_id) {
                $errors[] = 'Managers can only modify users in their own branch.';
            }
        }

        // Check for dependent data
        if ($user->role === 'field_officer' && $newRole !== 'field_officer') {
            $memberCount = Member::where('created_by', $user->id)->count();
            if ($memberCount > 0) {
                $errors[] = 'Cannot change role of field officer who has registered members.';
            }
        }

        if ($user->role === 'manager' && $newRole !== 'manager') {
            $branchUsers = User::where('branch_id', $user->branch_id)
                ->where('role', 'field_officer')
                ->where('id', '!=', $user->id)
                ->count();
            
            if ($branchUsers > 0) {
                $errors[] = 'Cannot change role of manager who has field officers in their branch.';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate transaction limits
     */
    public function validateTransactionLimits(User $user, float $amount, string $transactionType): array
    {
        $errors = [];

        // Daily transaction limits by role
        $dailyLimits = [
            'field_officer' => 100000, // ৳1,00,000
            'manager' => 500000,       // ৳5,00,000
            'admin' => 1000000,        // ৳10,00,000
        ];

        $userLimit = $dailyLimits[$user->role] ?? 50000;

        // Check today's transactions
        $todayTransactions = \App\Models\SavingTransaction::where('created_by', $user->id)
            ->whereDate('transaction_date', now())
            ->sum('amount');

        if (($todayTransactions + $amount) > $userLimit) {
            $remaining = $userLimit - $todayTransactions;
            $errors[] = "Daily transaction limit exceeded. Remaining limit: ৳" . number_format($remaining, 2);
        }

        // Single transaction limits
        $singleTransactionLimits = [
            'deposit' => 50000,
            'withdrawal' => 25000,
            'loan_payment' => 100000,
            'loan_disbursement' => 500000,
        ];

        $singleLimit = $singleTransactionLimits[$transactionType] ?? 10000;
        
        if ($amount > $singleLimit) {
            $errors[] = "Single transaction limit for {$transactionType} is ৳" . number_format($singleLimit, 2);
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate member registration eligibility
     */
    public function validateMemberRegistration(array $memberData, User $fieldOfficer): array
    {
        $errors = [];

        // Field officer member limit
        $memberCount = Member::where('created_by', $fieldOfficer->id)->count();
        $maxMembers = 200; // Max 200 members per field officer

        if ($memberCount >= $maxMembers) {
            $errors[] = "Field officer has reached maximum member limit ({$maxMembers}).";
        }

        // Age validation
        if (isset($memberData['date_of_birth'])) {
            $age = Carbon::parse($memberData['date_of_birth'])->age;
            if ($age < 18 || $age > 80) {
                $errors[] = 'Member age must be between 18 and 80 years.';
            }
        }

        // Income validation
        if (isset($memberData['monthly_income']) && $memberData['monthly_income'] < 5000) {
            $errors[] = 'Minimum monthly income requirement is ৳5,000.';
        }

        // Family size validation
        if (isset($memberData['family_members']) && $memberData['family_members'] > 20) {
            $errors[] = 'Maximum family size allowed is 20 members.';
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate loan approval authority
     */
    public function validateLoanApprovalAuthority(User $user, float $loanAmount): array
    {
        $errors = [];

        // Loan approval limits by role
        $approvalLimits = [
            'field_officer' => 0,      // Cannot approve loans
            'manager' => 100000,       // Up to ৳1,00,000
            'admin' => 500000,         // Up to ৳5,00,000
        ];

        $userLimit = $approvalLimits[$user->role] ?? 0;

        if ($user->role === 'field_officer') {
            $errors[] = 'Field officers cannot approve loans.';
        } elseif ($loanAmount > $userLimit) {
            $errors[] = "Your loan approval limit is ৳" . number_format($userLimit, 2) . ". This loan requires higher authority approval.";
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }

    /**
     * Validate data access permissions
     */
    public function validateDataAccess(User $user, $resourceOwnerId = null, $branchId = null): array
    {
        $errors = [];

        // Admin can access everything
        if ($user->role === 'admin') {
            return ['valid' => true, 'errors' => []];
        }

        // Manager can access their branch data
        if ($user->role === 'manager') {
            if ($branchId && $branchId !== $user->branch_id) {
                $errors[] = 'You can only access data from your assigned branch.';
            }
        }

        // Field officer can access their own data
        if ($user->role === 'field_officer') {
            if ($resourceOwnerId && $resourceOwnerId !== $user->id) {
                $errors[] = 'You can only access data you created.';
            }
            
            if ($branchId && $branchId !== $user->branch_id) {
                $errors[] = 'You can only access data from your assigned branch.';
            }
        }

        return [
            'valid' => empty($errors),
            'errors' => $errors
        ];
    }
}
