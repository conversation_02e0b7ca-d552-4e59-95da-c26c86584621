<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class BangladeshNidRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidBangladeshNid($value)) {
            $fail('The :attribute must be a valid Bangladesh National ID number.');
        }
    }

    /**
     * Validate Bangladesh National ID number format
     */
    private function isValidBangladeshNid(string $nid): bool
    {
        // Remove any non-digit characters
        $nid = preg_replace('/\D/', '', $nid);

        // Check if NID is empty after cleaning
        if (empty($nid)) {
            return false;
        }

        // Bangladesh NID formats:
        // Old format: 10 digits
        // New format: 13 digits
        // Smart NID: 17 digits
        $validLengths = [10, 13, 17];

        if (!in_array(strlen($nid), $validLengths)) {
            return false;
        }

        // Additional validation for specific formats
        if (strlen($nid) === 10) {
            // Old format: Should not start with 0
            if ($nid[0] === '0') {
                return false;
            }
        }

        if (strlen($nid) === 13) {
            // New format: Should not start with 0
            if ($nid[0] === '0') {
                return false;
            }
        }

        if (strlen($nid) === 17) {
            // Smart NID format: Should not start with 0
            if ($nid[0] === '0') {
                return false;
            }
        }

        // Check for obviously invalid patterns
        if ($this->hasInvalidPatterns($nid)) {
            return false;
        }

        return true;
    }

    /**
     * Check for invalid patterns in NID
     */
    private function hasInvalidPatterns(string $nid): bool
    {
        // Check for all same digits (e.g., 1111111111)
        if (preg_match('/^(\d)\1+$/', $nid)) {
            return true;
        }

        // Check for sequential patterns (e.g., 1234567890)
        if ($this->isSequential($nid)) {
            return true;
        }

        // Check for common invalid patterns
        $invalidPatterns = [
            '0000000000',
            '1111111111',
            '2222222222',
            '3333333333',
            '4444444444',
            '5555555555',
            '6666666666',
            '7777777777',
            '8888888888',
            '9999999999',
            '1234567890',
            '0987654321'
        ];

        foreach ($invalidPatterns as $pattern) {
            if (strpos($nid, $pattern) !== false) {
                return true;
            }
        }

        return false;
    }

    /**
     * Check if the NID contains sequential digits
     */
    private function isSequential(string $nid): bool
    {
        $length = strlen($nid);
        
        // Check ascending sequence
        $ascending = true;
        for ($i = 1; $i < $length; $i++) {
            if ((int)$nid[$i] !== ((int)$nid[$i-1] + 1) % 10) {
                $ascending = false;
                break;
            }
        }

        // Check descending sequence
        $descending = true;
        for ($i = 1; $i < $length; $i++) {
            if ((int)$nid[$i] !== ((int)$nid[$i-1] - 1 + 10) % 10) {
                $descending = false;
                break;
            }
        }

        return $ascending || $descending;
    }
}
