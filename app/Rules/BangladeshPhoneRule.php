<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class BangladeshPhoneRule implements ValidationRule
{
    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidBangladeshPhone($value)) {
            $fail('The :attribute must be a valid Bangladesh phone number.');
        }
    }

    /**
     * Validate Bangladesh phone number format
     */
    private function isValidBangladeshPhone(string $phone): bool
    {
        // Remove any non-digit characters except +
        $phone = preg_replace('/[^\d+]/', '', $phone);

        // Check if phone is empty after cleaning
        if (empty($phone)) {
            return false;
        }

        // Bangladesh mobile number patterns:
        // With country code: +8801XXXXXXXXX (14 digits total)
        // Without country code: 01XXXXXXXXX (11 digits)
        // International format: 8801XXXXXXXXX (13 digits)

        // Remove country code if present
        if (strpos($phone, '+88') === 0) {
            $phone = substr($phone, 3);
        } elseif (strpos($phone, '88') === 0 && strlen($phone) === 13) {
            $phone = substr($phone, 2);
        }

        // Now phone should be in format: 01XXXXXXXXX (11 digits)
        if (strlen($phone) !== 11) {
            return false;
        }

        // Must start with 01
        if (substr($phone, 0, 2) !== '01') {
            return false;
        }

        // Third digit must be valid operator code
        $operatorCodes = ['3', '4', '5', '6', '7', '8', '9'];
        if (!in_array($phone[2], $operatorCodes)) {
            return false;
        }

        // Check for obviously invalid patterns
        if ($this->hasInvalidPatterns($phone)) {
            return false;
        }

        // Validate specific operator patterns
        return $this->validateOperatorPattern($phone);
    }

    /**
     * Check for invalid patterns in phone number
     */
    private function hasInvalidPatterns(string $phone): bool
    {
        // Check for all same digits after 01 (e.g., 01111111111)
        $afterPrefix = substr($phone, 2);
        if (preg_match('/^(\d)\1+$/', $afterPrefix)) {
            return true;
        }

        // Check for sequential patterns
        if ($this->isSequential($afterPrefix)) {
            return true;
        }

        return false;
    }

    /**
     * Check if the phone number contains sequential digits
     */
    private function isSequential(string $digits): bool
    {
        $length = strlen($digits);
        
        if ($length < 4) {
            return false;
        }

        // Check for sequences of 4 or more consecutive digits
        for ($i = 0; $i <= $length - 4; $i++) {
            $sequence = substr($digits, $i, 4);
            
            // Check ascending sequence
            $ascending = true;
            for ($j = 1; $j < 4; $j++) {
                if ((int)$sequence[$j] !== ((int)$sequence[$j-1] + 1) % 10) {
                    $ascending = false;
                    break;
                }
            }

            // Check descending sequence
            $descending = true;
            for ($j = 1; $j < 4; $j++) {
                if ((int)$sequence[$j] !== ((int)$sequence[$j-1] - 1 + 10) % 10) {
                    $descending = false;
                    break;
                }
            }

            if ($ascending || $descending) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate operator-specific patterns
     */
    private function validateOperatorPattern(string $phone): bool
    {
        $operatorCode = $phone[2];

        // Operator-specific validations
        switch ($operatorCode) {
            case '3': // Grameenphone
            case '4': // Grameenphone
                return $this->validateGrameenphone($phone);
            
            case '5': // Banglalink
                return $this->validateBanglalink($phone);
            
            case '6': // Robi
                return $this->validateRobi($phone);
            
            case '7': // Robi/Airtel
                return $this->validateRobiAirtel($phone);
            
            case '8': // Robi/Airtel
                return $this->validateRobiAirtel($phone);
            
            case '9': // Banglalink
                return $this->validateBanglalink($phone);
            
            default:
                return false;
        }
    }

    /**
     * Validate Grameenphone number patterns
     */
    private function validateGrameenphone(string $phone): bool
    {
        // Grameenphone patterns: 013, 014
        $prefix = substr($phone, 0, 3);
        return in_array($prefix, ['013', '014']);
    }

    /**
     * Validate Banglalink number patterns
     */
    private function validateBanglalink(string $phone): bool
    {
        // Banglalink patterns: 015, 019
        $prefix = substr($phone, 0, 3);
        return in_array($prefix, ['015', '019']);
    }

    /**
     * Validate Robi number patterns
     */
    private function validateRobi(string $phone): bool
    {
        // Robi patterns: 016
        $prefix = substr($phone, 0, 3);
        return $prefix === '016';
    }

    /**
     * Validate Robi/Airtel number patterns
     */
    private function validateRobiAirtel(string $phone): bool
    {
        // Robi/Airtel patterns: 017, 018
        $prefix = substr($phone, 0, 3);
        return in_array($prefix, ['017', '018']);
    }
}
