<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use Carbon\Carbon;

class AgeRestrictionRule implements ValidationRule
{
    protected $minAge;
    protected $maxAge;

    /**
     * Create a new rule instance.
     */
    public function __construct($minAge = 18, $maxAge = 80)
    {
        $this->minAge = $minAge;
        $this->maxAge = $maxAge;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidAge($value)) {
            $fail($this->getErrorMessage());
        }
    }

    /**
     * Validate age based on date of birth
     */
    private function isValidAge($dateOfBirth): bool
    {
        try {
            $birthDate = Carbon::parse($dateOfBirth);
            $age = $birthDate->age;

            return $age >= $this->minAge && $age <= $this->maxAge;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get error message
     */
    private function getErrorMessage(): string
    {
        return "Age must be between {$this->minAge} and {$this->maxAge} years.";
    }
}
