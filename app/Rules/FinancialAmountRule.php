<?php

namespace App\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class FinancialAmountRule implements ValidationRule
{
    protected $minAmount;
    protected $maxAmount;
    protected $decimalPlaces;

    /**
     * Create a new rule instance.
     */
    public function __construct($minAmount = 0, $maxAmount = null, $decimalPlaces = 2)
    {
        $this->minAmount = $minAmount;
        $this->maxAmount = $maxAmount;
        $this->decimalPlaces = $decimalPlaces;
    }

    /**
     * Run the validation rule.
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (!$this->isValidFinancialAmount($value)) {
            $fail($this->getErrorMessage($attribute, $value));
        }
    }

    /**
     * Validate financial amount
     */
    private function isValidFinancialAmount($value): bool
    {
        // Check if value is numeric
        if (!is_numeric($value)) {
            return false;
        }

        $amount = (float) $value;

        // Check minimum amount
        if ($amount < $this->minAmount) {
            return false;
        }

        // Check maximum amount
        if ($this->maxAmount !== null && $amount > $this->maxAmount) {
            return false;
        }

        // Check decimal places
        if (!$this->hasValidDecimalPlaces($value)) {
            return false;
        }

        // Check for negative zero
        if ($amount == 0 && $value < 0) {
            return false;
        }

        return true;
    }

    /**
     * Check if the value has valid decimal places
     */
    private function hasValidDecimalPlaces($value): bool
    {
        $stringValue = (string) $value;
        
        // If no decimal point, it's valid
        if (strpos($stringValue, '.') === false) {
            return true;
        }

        // Get decimal part
        $decimalPart = substr($stringValue, strpos($stringValue, '.') + 1);
        
        // Check if decimal places exceed the limit
        if (strlen($decimalPart) > $this->decimalPlaces) {
            return false;
        }

        return true;
    }

    /**
     * Get appropriate error message
     */
    private function getErrorMessage(string $attribute, $value): string
    {
        if (!is_numeric($value)) {
            return "The {$attribute} must be a valid number.";
        }

        $amount = (float) $value;

        if ($amount < $this->minAmount) {
            return "The {$attribute} must be at least ৳" . number_format($this->minAmount, 2) . ".";
        }

        if ($this->maxAmount !== null && $amount > $this->maxAmount) {
            return "The {$attribute} cannot exceed ৳" . number_format($this->maxAmount, 2) . ".";
        }

        if (!$this->hasValidDecimalPlaces($value)) {
            return "The {$attribute} cannot have more than {$this->decimalPlaces} decimal places.";
        }

        return "The {$attribute} is not a valid financial amount.";
    }
}
