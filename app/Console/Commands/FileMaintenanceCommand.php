<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Services\FileManagerService;
use App\Services\DocumentManagerService;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Log;

class FileMaintenanceCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'files:maintenance 
                            {--cleanup-temp : Clean up temporary files}
                            {--archive-old : Archive old documents}
                            {--optimize-images : Optimize existing images}
                            {--check-integrity : Check file integrity}
                            {--all : Run all maintenance tasks}';

    /**
     * The console command description.
     */
    protected $description = 'Perform file system maintenance tasks';

    protected FileManagerService $fileManager;
    protected DocumentManagerService $documentManager;

    public function __construct(
        FileManagerService $fileManager,
        DocumentManagerService $documentManager
    ) {
        parent::__construct();
        $this->fileManager = $fileManager;
        $this->documentManager = $documentManager;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Starting file maintenance tasks...');
        
        $runAll = $this->option('all');
        
        if ($runAll || $this->option('cleanup-temp')) {
            $this->cleanupTempFiles();
        }
        
        if ($runAll || $this->option('archive-old')) {
            $this->archiveOldDocuments();
        }
        
        if ($runAll || $this->option('optimize-images')) {
            $this->optimizeImages();
        }
        
        if ($runAll || $this->option('check-integrity')) {
            $this->checkFileIntegrity();
        }
        
        if (!$runAll && !$this->option('cleanup-temp') && !$this->option('archive-old') && 
            !$this->option('optimize-images') && !$this->option('check-integrity')) {
            $this->showMaintenanceMenu();
        }
        
        $this->info('File maintenance completed!');
    }

    /**
     * Show interactive maintenance menu
     */
    protected function showMaintenanceMenu()
    {
        $choice = $this->choice(
            'Which maintenance task would you like to run?',
            [
                'cleanup-temp' => 'Clean up temporary files',
                'archive-old' => 'Archive old documents',
                'optimize-images' => 'Optimize existing images',
                'check-integrity' => 'Check file integrity',
                'storage-stats' => 'Show storage statistics',
                'all' => 'Run all maintenance tasks'
            ]
        );
        
        switch ($choice) {
            case 'cleanup-temp':
                $this->cleanupTempFiles();
                break;
            case 'archive-old':
                $this->archiveOldDocuments();
                break;
            case 'optimize-images':
                $this->optimizeImages();
                break;
            case 'check-integrity':
                $this->checkFileIntegrity();
                break;
            case 'storage-stats':
                $this->showStorageStats();
                break;
            case 'all':
                $this->cleanupTempFiles();
                $this->archiveOldDocuments();
                $this->optimizeImages();
                $this->checkFileIntegrity();
                break;
        }
    }

    /**
     * Clean up temporary files
     */
    protected function cleanupTempFiles()
    {
        $this->info('Cleaning up temporary files...');
        
        try {
            $deletedCount = $this->fileManager->cleanupTempFiles(24);
            $this->info("Deleted {$deletedCount} temporary files older than 24 hours.");
            
            // Also clean up failed uploads and processing files
            $this->cleanupFailedUploads();
            
        } catch (\Exception $e) {
            $this->error('Failed to cleanup temporary files: ' . $e->getMessage());
            Log::error('File cleanup failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Archive old documents
     */
    protected function archiveOldDocuments()
    {
        $this->info('Archiving old documents...');
        
        try {
            $categories = $this->documentManager->getDocumentCategories();
            $totalArchived = 0;
            
            foreach (array_keys($categories) as $category) {
                $archived = $this->documentManager->archiveOldDocuments($category, 365);
                $totalArchived += $archived;
                $this->line("Archived {$archived} documents from category: {$category}");
            }
            
            $this->info("Total archived documents: {$totalArchived}");
            
        } catch (\Exception $e) {
            $this->error('Failed to archive documents: ' . $e->getMessage());
            Log::error('Document archiving failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Optimize existing images
     */
    protected function optimizeImages()
    {
        $this->info('Optimizing existing images...');
        
        try {
            $optimizedCount = 0;
            $disks = ['images', 'public'];
            
            foreach ($disks as $disk) {
                $files = Storage::disk($disk)->allFiles();
                
                foreach ($files as $file) {
                    if ($this->isImageFile($file)) {
                        if ($this->optimizeImage($file, $disk)) {
                            $optimizedCount++;
                        }
                    }
                }
            }
            
            $this->info("Optimized {$optimizedCount} images.");
            
        } catch (\Exception $e) {
            $this->error('Failed to optimize images: ' . $e->getMessage());
            Log::error('Image optimization failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Check file integrity
     */
    protected function checkFileIntegrity()
    {
        $this->info('Checking file integrity...');
        
        try {
            $corruptedFiles = [];
            $checkedCount = 0;
            $disks = ['images', 'documents', 'public'];
            
            foreach ($disks as $disk) {
                $files = Storage::disk($disk)->allFiles();
                
                foreach ($files as $file) {
                    $checkedCount++;
                    
                    if (!$this->verifyFileIntegrity($file, $disk)) {
                        $corruptedFiles[] = ['disk' => $disk, 'file' => $file];
                    }
                    
                    // Show progress for large numbers of files
                    if ($checkedCount % 100 === 0) {
                        $this->line("Checked {$checkedCount} files...");
                    }
                }
            }
            
            $this->info("Checked {$checkedCount} files.");
            
            if (empty($corruptedFiles)) {
                $this->info('All files passed integrity check.');
            } else {
                $this->warn('Found ' . count($corruptedFiles) . ' corrupted files:');
                foreach ($corruptedFiles as $file) {
                    $this->line("- {$file['disk']}: {$file['file']}");
                }
            }
            
        } catch (\Exception $e) {
            $this->error('Failed to check file integrity: ' . $e->getMessage());
            Log::error('File integrity check failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Show storage statistics
     */
    protected function showStorageStats()
    {
        $this->info('Storage Statistics:');
        
        try {
            $stats = $this->fileManager->getStorageStats();
            
            $this->table(
                ['Disk', 'Files', 'Total Size'],
                collect($stats)->map(function ($stat, $disk) {
                    return [
                        $disk,
                        $stat['file_count'] ?? 'Error',
                        $stat['total_size_human'] ?? $stat['error'] ?? 'Unknown'
                    ];
                })->toArray()
            );
            
        } catch (\Exception $e) {
            $this->error('Failed to get storage statistics: ' . $e->getMessage());
        }
    }

    /**
     * Clean up failed uploads
     */
    protected function cleanupFailedUploads()
    {
        try {
            // Clean up orphaned temporary files
            $tempFiles = Storage::disk('temp')->allFiles();
            $deletedCount = 0;
            
            foreach ($tempFiles as $file) {
                $lastModified = Storage::disk('temp')->lastModified($file);
                
                // Delete files older than 1 hour
                if ($lastModified < now()->subHour()->timestamp) {
                    Storage::disk('temp')->delete($file);
                    $deletedCount++;
                }
            }
            
            if ($deletedCount > 0) {
                $this->line("Cleaned up {$deletedCount} failed upload files.");
            }
            
        } catch (\Exception $e) {
            Log::warning('Failed upload cleanup failed', ['error' => $e->getMessage()]);
        }
    }

    /**
     * Check if file is an image
     */
    protected function isImageFile(string $file): bool
    {
        $imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];
        $extension = strtolower(pathinfo($file, PATHINFO_EXTENSION));
        
        return in_array($extension, $imageExtensions);
    }

    /**
     * Optimize a single image
     */
    protected function optimizeImage(string $file, string $disk): bool
    {
        try {
            // This is a placeholder for image optimization
            // You could implement actual optimization logic here
            return true;
            
        } catch (\Exception $e) {
            Log::warning('Image optimization failed', [
                'file' => $file,
                'disk' => $disk,
                'error' => $e->getMessage()
            ]);
            
            return false;
        }
    }

    /**
     * Verify file integrity
     */
    protected function verifyFileIntegrity(string $file, string $disk): bool
    {
        try {
            // Check if file exists and is readable
            if (!Storage::disk($disk)->exists($file)) {
                return false;
            }
            
            // Check file size
            $size = Storage::disk($disk)->size($file);
            if ($size === false || $size === 0) {
                return false;
            }
            
            // For images, try to verify they can be read
            if ($this->isImageFile($file)) {
                $fullPath = Storage::disk($disk)->path($file);
                $imageInfo = @getimagesize($fullPath);
                
                return $imageInfo !== false;
            }
            
            return true;
            
        } catch (\Exception $e) {
            return false;
        }
    }
}
