<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Advertisement extends Model
{
    use HasFactory;

    protected $fillable = [
        'title',
        'content',
        'image_path',
        'link_url',
        'position',
        'priority',
        'is_active',
        'start_date',
        'end_date',
        'target_audience',
        'click_count',
        'view_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'start_date' => 'date',
        'end_date' => 'date',
        'click_count' => 'integer',
        'view_count' => 'integer',
        'priority' => 'integer',
    ];

    /**
     * Business logic methods
     */
    public function activate(): void
    {
        $this->update(['is_active' => true]);
    }

    public function deactivate(): void
    {
        $this->update(['is_active' => false]);
    }

    public function incrementViewCount(): void
    {
        $this->increment('view_count');
    }

    public function incrementClickCount(): void
    {
        $this->increment('click_count');
    }

    public function isCurrentlyActive(): bool
    {
        if (!$this->is_active) {
            return false;
        }

        $now = now();
        
        if ($this->start_date && $now->lt($this->start_date)) {
            return false;
        }

        if ($this->end_date && $now->gt($this->end_date)) {
            return false;
        }

        return true;
    }

    public function isExpired(): bool
    {
        return $this->end_date && now()->gt($this->end_date);
    }

    public function isScheduled(): bool
    {
        return $this->start_date && now()->lt($this->start_date);
    }

    public function getDaysRemaining(): int
    {
        if (!$this->end_date) {
            return -1; // No end date
        }

        return max(0, now()->diffInDays($this->end_date, false));
    }

    public function getClickThroughRate(): float
    {
        if ($this->view_count == 0) {
            return 0;
        }

        return ($this->click_count / $this->view_count) * 100;
    }

    /**
     * Scopes
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true)
                    ->where(function ($q) {
                        $q->whereNull('start_date')
                          ->orWhere('start_date', '<=', now());
                    })
                    ->where(function ($q) {
                        $q->whereNull('end_date')
                          ->orWhere('end_date', '>=', now());
                    });
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    public function scopeExpired($query)
    {
        return $query->where('end_date', '<', now());
    }

    public function scopeScheduled($query)
    {
        return $query->where('start_date', '>', now());
    }

    public function scopeByPosition($query, $position)
    {
        return $query->where('position', $position);
    }

    public function scopeByTargetAudience($query, $audience)
    {
        return $query->where('target_audience', $audience);
    }

    public function scopeOrderByPriority($query)
    {
        return $query->orderBy('priority', 'desc');
    }

    public function scopePopular($query, $minViews = 100)
    {
        return $query->where('view_count', '>=', $minViews);
    }

    public function scopeHighPerforming($query, $minCTR = 5.0)
    {
        return $query->whereRaw('(click_count / GREATEST(view_count, 1)) * 100 >= ?', [$minCTR]);
    }

    /**
     * Accessors
     */
    public function getStatusAttribute(): string
    {
        if (!$this->is_active) {
            return 'Inactive';
        }

        if ($this->isExpired()) {
            return 'Expired';
        }

        if ($this->isScheduled()) {
            return 'Scheduled';
        }

        return 'Active';
    }

    public function getStatusBadgeAttribute(): string
    {
        return match($this->getStatusAttribute()) {
            'Active' => '<span class="badge bg-success">Active</span>',
            'Inactive' => '<span class="badge bg-secondary">Inactive</span>',
            'Expired' => '<span class="badge bg-danger">Expired</span>',
            'Scheduled' => '<span class="badge bg-info">Scheduled</span>',
            default => '<span class="badge bg-warning">Unknown</span>'
        };
    }

    public function getPositionDisplayAttribute(): string
    {
        return match($this->position) {
            'header' => 'Header Banner',
            'sidebar' => 'Sidebar',
            'footer' => 'Footer',
            'popup' => 'Popup',
            'inline' => 'Inline Content',
            default => ucfirst($this->position ?? 'Unknown')
        };
    }

    public function getTargetAudienceDisplayAttribute(): string
    {
        return match($this->target_audience) {
            'all' => 'All Users',
            'members' => 'Members Only',
            'staff' => 'Staff Only',
            'managers' => 'Managers Only',
            'admins' => 'Administrators Only',
            default => ucfirst($this->target_audience ?? 'All')
        };
    }

    public function getFormattedClickThroughRateAttribute(): string
    {
        return number_format($this->getClickThroughRate(), 2) . '%';
    }

    public function getTruncatedContentAttribute(): string
    {
        return strlen($this->content) > 100 
            ? substr($this->content, 0, 100) . '...' 
            : $this->content;
    }

    /**
     * Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'title' => 'required|string|max:255',
            'content' => 'required|string|max:1000',
            'image_path' => 'nullable|string|max:255',
            'link_url' => 'nullable|url|max:255',
            'position' => 'required|in:header,sidebar,footer,popup,inline',
            'priority' => 'nullable|integer|min:1|max:10',
            'is_active' => 'boolean',
            'start_date' => 'nullable|date',
            'end_date' => 'nullable|date|after_or_equal:start_date',
            'target_audience' => 'required|in:all,members,staff,managers,admins',
        ];
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($advertisement) {
            if (is_null($advertisement->priority)) {
                $advertisement->priority = 5; // Default priority
            }
            
            if (is_null($advertisement->is_active)) {
                $advertisement->is_active = true;
            }
        });
    }
}
