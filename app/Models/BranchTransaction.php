<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class BranchTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'branch_id',
        'transaction_type',
        'amount',
        'description',
        'transaction_date',
        'entered_by',
        'reference_number',
        'category',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'transaction_date' => 'date',
    ];

    /**
     * Relationships
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function enteredBy()
    {
        return $this->belongsTo(User::class, 'entered_by');
    }

    /**
     * Business logic methods
     */
    public function getMonthlyTotal($month = null, $year = null, $type = null): float
    {
        $month = $month ?? now()->month;
        $year = $year ?? now()->year;

        $query = static::whereMonth('transaction_date', $month)
                      ->whereYear('transaction_date', $year);

        if ($type) {
            $query->where('transaction_type', $type);
        }

        return $query->sum('amount');
    }

    public function isIncome(): bool
    {
        return $this->transaction_type === 'income';
    }

    public function isExpense(): bool
    {
        return $this->transaction_type === 'expense';
    }

    /**
     * Scopes
     */
    public function scopeIncome($query)
    {
        return $query->where('transaction_type', 'income');
    }

    public function scopeExpense($query)
    {
        return $query->where('transaction_type', 'expense');
    }

    public function scopeByBranch($query, $branchId)
    {
        return $query->where('branch_id', $branchId);
    }

    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('transaction_date', now()->month)
                    ->whereYear('transaction_date', now()->year);
    }

    public function scopeThisYear($query)
    {
        return $query->whereYear('transaction_date', now()->year);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('transaction_date', today());
    }

    /**
     * Accessors
     */
    public function getTransactionTypeBadgeAttribute(): string
    {
        return match($this->transaction_type) {
            'income' => '<span class="badge bg-success">Income</span>',
            'expense' => '<span class="badge bg-danger">Expense</span>',
            default => '<span class="badge bg-secondary">Unknown</span>'
        };
    }

    public function getFormattedAmountAttribute(): string
    {
        $symbol = $this->isIncome() ? '+' : '-';
        return $symbol . ' ৳ ' . number_format($this->amount, 2);
    }

    public function getFormattedAmountPlainAttribute(): string
    {
        return '৳ ' . number_format($this->amount, 2);
    }

    public function getCategoryDisplayAttribute(): string
    {
        return match($this->category) {
            'loan_disbursement' => 'Loan Disbursement',
            'installment_collection' => 'Installment Collection',
            'savings_deposit' => 'Savings Deposit',
            'savings_withdrawal' => 'Savings Withdrawal',
            'office_expense' => 'Office Expense',
            'staff_salary' => 'Staff Salary',
            'utility_bills' => 'Utility Bills',
            'maintenance' => 'Maintenance',
            'other' => 'Other',
            default => ucfirst(str_replace('_', ' ', $this->category ?? 'Unknown'))
        };
    }

    /**
     * Static methods for calculations
     */
    public static function getTotalIncomeForBranch($branchId, $month = null, $year = null): float
    {
        $month = $month ?? now()->month;
        $year = $year ?? now()->year;

        return static::where('branch_id', $branchId)
                    ->where('transaction_type', 'income')
                    ->whereMonth('transaction_date', $month)
                    ->whereYear('transaction_date', $year)
                    ->sum('amount');
    }

    public static function getTotalExpenseForBranch($branchId, $month = null, $year = null): float
    {
        $month = $month ?? now()->month;
        $year = $year ?? now()->year;

        return static::where('branch_id', $branchId)
                    ->where('transaction_type', 'expense')
                    ->whereMonth('transaction_date', $month)
                    ->whereYear('transaction_date', $year)
                    ->sum('amount');
    }

    public static function getNetIncomeForBranch($branchId, $month = null, $year = null): float
    {
        $income = static::getTotalIncomeForBranch($branchId, $month, $year);
        $expense = static::getTotalExpenseForBranch($branchId, $month, $year);

        return $income - $expense;
    }

    public static function getCategoryTotalsForBranch($branchId, $month = null, $year = null): array
    {
        $month = $month ?? now()->month;
        $year = $year ?? now()->year;

        return static::where('branch_id', $branchId)
                    ->whereMonth('transaction_date', $month)
                    ->whereYear('transaction_date', $year)
                    ->selectRaw('category, transaction_type, SUM(amount) as total')
                    ->groupBy('category', 'transaction_type')
                    ->get()
                    ->groupBy('category')
                    ->map(function ($transactions) {
                        return [
                            'income' => $transactions->where('transaction_type', 'income')->sum('total'),
                            'expense' => $transactions->where('transaction_type', 'expense')->sum('total'),
                        ];
                    })
                    ->toArray();
    }

    /**
     * Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'branch_id' => 'required|exists:branches,id',
            'transaction_type' => 'required|in:income,expense',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:500',
            'transaction_date' => 'required|date',
            'entered_by' => 'required|exists:users,id',
            'reference_number' => 'nullable|string|max:50',
            'category' => 'required|string|max:50',
        ];
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (!$transaction->transaction_date) {
                $transaction->transaction_date = now();
            }
            
            if (!$transaction->reference_number) {
                $transaction->reference_number = 'TXN-' . strtoupper(uniqid());
            }
        });
    }
}
