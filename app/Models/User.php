<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'member_id',
        'branch_id',
        'is_active',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
            'is_active' => 'boolean',
        ];
    }

    /**
     * Relationships
     */
    public function branch()
    {
        return $this->belongsTo(Branch::class);
    }

    public function member()
    {
        return $this->hasOne(Member::class, 'member_id', 'member_id');
    }

    public function managedBranches()
    {
        return $this->hasMany(Branch::class, 'manager_id');
    }

    public function createdMembers()
    {
        return $this->hasMany(Member::class, 'created_by');
    }

    public function reviewedLoanApplications()
    {
        return $this->hasMany(LoanApplication::class, 'reviewed_by');
    }

    public function collectedInstallments()
    {
        return $this->hasMany(Installment::class, 'collected_by');
    }

    public function branchTransactions()
    {
        return $this->hasMany(BranchTransaction::class, 'entered_by');
    }

    public function createdSavingAccounts()
    {
        return $this->hasMany(SavingAccount::class, 'created_by');
    }

    /**
     * Role-based methods
     */
    public function isAdmin(): bool
    {
        return $this->role === 'admin';
    }

    public function isManager(): bool
    {
        return $this->role === 'manager';
    }

    public function isFieldOfficer(): bool
    {
        return $this->role === 'field_officer';
    }

    public function isMember(): bool
    {
        return $this->role === 'member';
    }

    /**
     * Scopes
     */
    public function scopeAdmins($query)
    {
        return $query->where('role', 'admin');
    }

    public function scopeManagers($query)
    {
        return $query->where('role', 'manager');
    }

    public function scopeFieldOfficers($query)
    {
        return $query->where('role', 'field_officer');
    }

    public function scopeMembers($query)
    {
        return $query->where('role', 'member');
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    public function scopeInactive($query)
    {
        return $query->where('is_active', false);
    }

    /**
     * Business logic methods
     */
    public function canManageBranch(Branch $branch): bool
    {
        return $this->isAdmin() || ($this->isManager() && $this->managedBranches->contains($branch));
    }

    public function canAccessBranch(Branch $branch): bool
    {
        return $this->isAdmin() || $this->branch_id === $branch->id;
    }

    public function getDisplayRoleAttribute(): string
    {
        return match($this->role) {
            'admin' => 'Administrator',
            'manager' => 'Branch Manager',
            'field_officer' => 'Field Officer',
            'member' => 'Member',
            default => 'Unknown'
        };
    }
}
