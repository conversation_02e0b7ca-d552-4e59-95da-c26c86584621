<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SavingTransaction extends Model
{
    use HasFactory;

    protected $fillable = [
        'saving_account_id',
        'transaction_type',
        'amount',
        'description',
        'transaction_date',
        'entered_by',
        'balance_after',
        'reference_number',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'balance_after' => 'decimal:2',
        'transaction_date' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function savingAccount()
    {
        return $this->belongsTo(SavingAccount::class);
    }

    public function enteredBy()
    {
        return $this->belongsTo(User::class, 'entered_by');
    }

    /**
     * Scopes
     */
    public function scopeDeposits($query)
    {
        return $query->where('transaction_type', 'deposit');
    }

    public function scopeWithdrawals($query)
    {
        return $query->where('transaction_type', 'withdrawal');
    }

    public function scopeByAccount($query, $accountId)
    {
        return $query->where('saving_account_id', $accountId);
    }

    public function scopeByDateRange($query, $startDate, $endDate)
    {
        return $query->whereBetween('transaction_date', [$startDate, $endDate]);
    }

    public function scopeToday($query)
    {
        return $query->whereDate('transaction_date', today());
    }

    public function scopeThisMonth($query)
    {
        return $query->whereMonth('transaction_date', now()->month)
                    ->whereYear('transaction_date', now()->year);
    }

    /**
     * Accessors
     */
    public function getTransactionTypeBadgeAttribute(): string
    {
        return match($this->transaction_type) {
            'deposit' => '<span class="badge bg-success">Deposit</span>',
            'withdrawal' => '<span class="badge bg-danger">Withdrawal</span>',
            default => '<span class="badge bg-secondary">Unknown</span>'
        };
    }

    public function getFormattedAmountAttribute(): string
    {
        $symbol = $this->transaction_type === 'deposit' ? '+' : '-';
        return $symbol . ' ৳ ' . number_format($this->amount, 2);
    }

    public function getFormattedBalanceAfterAttribute(): string
    {
        return '৳ ' . number_format($this->balance_after, 2);
    }

    /**
     * Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'saving_account_id' => 'required|exists:saving_accounts,id',
            'transaction_type' => 'required|in:deposit,withdrawal',
            'amount' => 'required|numeric|min:0.01',
            'description' => 'required|string|max:255',
            'transaction_date' => 'required|date',
            'entered_by' => 'required|exists:users,id',
            'balance_after' => 'required|numeric|min:0',
            'reference_number' => 'nullable|string|max:50',
        ];
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($transaction) {
            if (!$transaction->reference_number) {
                $transaction->reference_number = 'SAV-TXN-' . strtoupper(uniqid());
            }
        });
    }
}
