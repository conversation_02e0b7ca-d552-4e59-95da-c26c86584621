<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class Installment extends Model
{
    use HasFactory;

    protected $fillable = [
        'loan_id',
        'installment_no',
        'amount',
        'installment_date',
        'status',
        'collection_date',
        'collected_by',
        'late_fee',
        'notes',
    ];

    protected $casts = [
        'amount' => 'decimal:2',
        'late_fee' => 'decimal:2',
        'installment_date' => 'date',
        'collection_date' => 'datetime',
    ];

    /**
     * Relationships
     */
    public function loan()
    {
        return $this->belongsTo(Loan::class);
    }

    public function collectedBy()
    {
        return $this->belongsTo(User::class, 'collected_by');
    }

    /**
     * Business logic methods
     */
    public function markAsPaid(User $collector, float $lateFee = 0, string $notes = null): void
    {
        $this->update([
            'status' => 'paid',
            'collection_date' => now(),
            'collected_by' => $collector->id,
            'late_fee' => $lateFee,
            'notes' => $notes,
        ]);
    }

    public function calculateDueAmount(): float
    {
        $baseAmount = $this->amount;
        $lateFee = $this->calculateLateFee();
        
        return $baseAmount + $lateFee;
    }

    public function calculateLateFee(): float
    {
        if ($this->status === 'paid' || !$this->isOverdue()) {
            return 0;
        }

        $daysOverdue = $this->getDaysOverdue();
        $lateFeePerDay = 5; // ৳5 per day late fee
        
        return min($daysOverdue * $lateFeePerDay, $this->amount * 0.1); // Max 10% of installment amount
    }

    public function isOverdue(): bool
    {
        return $this->status === 'pending' && $this->installment_date->isPast();
    }

    public function getDaysOverdue(): int
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        return $this->installment_date->diffInDays(now());
    }

    public function canBeCollected(): bool
    {
        return $this->status === 'pending';
    }

    public function getGracePeriodEndDate(): Carbon
    {
        return $this->installment_date->addDays(7); // 7 days grace period
    }

    public function isInGracePeriod(): bool
    {
        return $this->status === 'pending' && 
               $this->installment_date->isPast() && 
               now()->lte($this->getGracePeriodEndDate());
    }

    /**
     * Scopes
     */
    public function scopePending($query)
    {
        return $query->where('status', 'pending');
    }

    public function scopePaid($query)
    {
        return $query->where('status', 'paid');
    }

    public function scopeOverdue($query)
    {
        return $query->where('status', 'pending')
                    ->where('installment_date', '<', now());
    }

    public function scopeDueToday($query)
    {
        return $query->where('status', 'pending')
                    ->whereDate('installment_date', today());
    }

    public function scopeDueThisWeek($query)
    {
        return $query->where('status', 'pending')
                    ->whereBetween('installment_date', [
                        now()->startOfWeek(),
                        now()->endOfWeek()
                    ]);
    }

    public function scopeDueThisMonth($query)
    {
        return $query->where('status', 'pending')
                    ->whereMonth('installment_date', now()->month)
                    ->whereYear('installment_date', now()->year);
    }

    public function scopeByLoan($query, $loanId)
    {
        return $query->where('loan_id', $loanId);
    }

    public function scopeByCollector($query, $collectorId)
    {
        return $query->where('collected_by', $collectorId);
    }

    public function scopeCollectedBetween($query, $startDate, $endDate)
    {
        return $query->where('status', 'paid')
                    ->whereBetween('collection_date', [$startDate, $endDate]);
    }

    /**
     * Accessors
     */
    public function getStatusBadgeAttribute(): string
    {
        if ($this->status === 'paid') {
            return '<span class="badge bg-success">Paid</span>';
        } elseif ($this->isOverdue()) {
            return '<span class="badge bg-danger">Overdue</span>';
        } elseif ($this->installment_date->isToday()) {
            return '<span class="badge bg-warning">Due Today</span>';
        } else {
            return '<span class="badge bg-info">Pending</span>';
        }
    }

    public function getFormattedAmountAttribute(): string
    {
        return '৳ ' . number_format($this->amount, 2);
    }

    public function getFormattedDueAmountAttribute(): string
    {
        return '৳ ' . number_format($this->calculateDueAmount(), 2);
    }

    public function getFormattedLateFeeAttribute(): string
    {
        return '৳ ' . number_format($this->calculateLateFee(), 2);
    }

    public function getInstallmentTitleAttribute(): string
    {
        return "Installment #{$this->installment_no}";
    }

    public function getDaysUntilDueAttribute(): int
    {
        if ($this->installment_date->isPast()) {
            return 0;
        }

        return now()->diffInDays($this->installment_date);
    }

    /**
     * Validation rules
     */
    public static function validationRules(): array
    {
        return [
            'loan_id' => 'required|exists:loans,id',
            'installment_no' => 'required|integer|min:1',
            'amount' => 'required|numeric|min:0',
            'installment_date' => 'required|date',
            'status' => 'required|in:pending,paid',
            'collection_date' => 'nullable|date',
            'collected_by' => 'nullable|exists:users,id',
            'late_fee' => 'nullable|numeric|min:0',
            'notes' => 'nullable|string|max:500',
        ];
    }

    /**
     * Boot method
     */
    protected static function boot()
    {
        parent::boot();

        static::creating(function ($installment) {
            if (!$installment->status) {
                $installment->status = 'pending';
            }
        });

        static::updating(function ($installment) {
            // Auto-set collection date when marking as paid
            if ($installment->isDirty('status') && $installment->status === 'paid' && !$installment->collection_date) {
                $installment->collection_date = now();
            }
        });
    }
}
