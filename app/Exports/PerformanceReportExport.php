<?php

namespace App\Exports;

use App\Models\User;
use App\Services\ReportingService;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class PerformanceReportExport implements WithMultipleSheets
{
    protected $fieldOfficer;
    protected $startDate;
    protected $endDate;
    protected $reportingService;

    public function __construct(User $fieldOfficer = null, Carbon $startDate = null, Carbon $endDate = null)
    {
        $this->fieldOfficer = $fieldOfficer;
        $this->startDate = $startDate ?? Carbon::now()->startOfMonth();
        $this->endDate = $endDate ?? Carbon::now()->endOfMonth();
        $this->reportingService = new ReportingService();
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        if ($this->fieldOfficer) {
            // Single officer performance
            $sheets[] = new OfficerPerformanceSheet($this->fieldOfficer, $this->startDate, $this->endDate, $this->reportingService);
        } else {
            // All officers summary
            $sheets[] = new AllOfficersPerformanceSheet($this->startDate, $this->endDate, $this->reportingService);
        }

        return $sheets;
    }
}

class OfficerPerformanceSheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected $fieldOfficer;
    protected $startDate;
    protected $endDate;
    protected $reportingService;

    public function __construct(User $fieldOfficer, Carbon $startDate, Carbon $endDate, ReportingService $reportingService)
    {
        $this->fieldOfficer = $fieldOfficer;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->reportingService = $reportingService;
    }

    public function array(): array
    {
        $data = $this->reportingService->getFieldOfficerPerformance($this->fieldOfficer, $this->startDate, $this->endDate);

        return [
            ['Performance Metric', 'Value'],
            ['Officer Name', $data['officer_info']['name']],
            ['Employee ID', $data['officer_info']['employee_id']],
            ['Branch', $data['officer_info']['branch']],
            [''], // Empty row
            ['Member Metrics', ''],
            ['Total Members', $data['member_metrics']['total_members']],
            ['New Members (Period)', $data['member_metrics']['new_members']],
            ['Active Members', $data['member_metrics']['active_members']],
            [''], // Empty row
            ['Loan Metrics', ''],
            ['Loans Disbursed (Period)', $data['loan_metrics']['loans_disbursed']],
            ['Total Disbursed Amount (৳)', number_format($data['loan_metrics']['total_disbursed_amount'], 2)],
            ['Applications Submitted', $data['loan_metrics']['applications_submitted']],
            [''], // Empty row
            ['Collection Metrics', ''],
            ['Total Collected (৳)', number_format($data['collection_metrics']['total_collected'], 2)],
            ['Installments Collected', $data['collection_metrics']['installments_collected']],
            ['Collection Efficiency (%)', number_format($data['collection_metrics']['collection_efficiency'], 2)],
        ];
    }

    public function headings(): array
    {
        return [
            ['Field Officer Performance Report'],
            ['Period: ' . $this->startDate->format('Y-m-d') . ' to ' . $this->endDate->format('Y-m-d')],
            ['Generated: ' . Carbon::now()->format('Y-m-d H:i:s')],
            [''], // Empty row
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 16]],
            2 => ['font' => ['bold' => true]],
            3 => ['font' => ['italic' => true]],
            5 => ['font' => ['bold' => true]], // Data headers
            10 => ['font' => ['bold' => true]], // Section headers
            15 => ['font' => ['bold' => true]],
            20 => ['font' => ['bold' => true]],
        ];
    }

    public function title(): string
    {
        return 'Officer Performance';
    }
}

class AllOfficersPerformanceSheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected $startDate;
    protected $endDate;
    protected $reportingService;

    public function __construct(Carbon $startDate, Carbon $endDate, ReportingService $reportingService)
    {
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->reportingService = $reportingService;
    }

    public function array(): array
    {
        $fieldOfficers = User::where('role', 'field_officer')
            ->where('is_active', true)
            ->with('branch')
            ->get();

        $result = [
            [
                'Officer Name',
                'Employee ID',
                'Branch',
                'Total Members',
                'New Members',
                'Active Members',
                'Loans Disbursed',
                'Amount Disbursed (৳)',
                'Total Collected (৳)',
                'Collection Efficiency (%)'
            ]
        ];

        foreach ($fieldOfficers as $officer) {
            $performance = $this->reportingService->getFieldOfficerPerformance($officer, $this->startDate, $this->endDate);
            
            $result[] = [
                $performance['officer_info']['name'],
                $performance['officer_info']['employee_id'],
                $performance['officer_info']['branch'],
                $performance['member_metrics']['total_members'],
                $performance['member_metrics']['new_members'],
                $performance['member_metrics']['active_members'],
                $performance['loan_metrics']['loans_disbursed'],
                number_format($performance['loan_metrics']['total_disbursed_amount'], 2),
                number_format($performance['collection_metrics']['total_collected'], 2),
                number_format($performance['collection_metrics']['collection_efficiency'], 2)
            ];
        }

        return $result;
    }

    public function headings(): array
    {
        return [
            ['All Field Officers Performance Report'],
            ['Period: ' . $this->startDate->format('Y-m-d') . ' to ' . $this->endDate->format('Y-m-d')],
            ['Generated: ' . Carbon::now()->format('Y-m-d H:i:s')],
            [''], // Empty row
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 16]],
            2 => ['font' => ['bold' => true]],
            3 => ['font' => ['italic' => true]],
            5 => ['font' => ['bold' => true]], // Data headers
        ];
    }

    public function title(): string
    {
        return 'All Officers Performance';
    }
}
