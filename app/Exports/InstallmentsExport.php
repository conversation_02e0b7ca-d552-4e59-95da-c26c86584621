<?php

namespace App\Exports;

use App\Models\Installment;
use App\Models\Branch;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class InstallmentsExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $branch;
    protected $startDate;
    protected $endDate;
    protected $filters;

    public function __construct(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null, array $filters = [])
    {
        $this->branch = $branch;
        $this->startDate = $startDate ?? Carbon::now()->startOfMonth();
        $this->endDate = $endDate ?? Carbon::now()->endOfMonth();
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Installment::with(['loan.loanApplication.member.branch', 'collectedBy']);

        if ($this->branch) {
            $query->whereHas('loan', function ($q) {
                $q->where('branch_id', $this->branch->id);
            });
        }

        $query->whereBetween('installment_date', [$this->startDate, $this->endDate]);

        // Apply filters
        if (isset($this->filters['status']) && $this->filters['status']) {
            $query->where('status', $this->filters['status']);
        }

        if (isset($this->filters['collected_by']) && $this->filters['collected_by']) {
            $query->where('collected_by', $this->filters['collected_by']);
        }

        return $query->orderBy('installment_date', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Installment ID',
            'Loan ID',
            'Member ID',
            'Member Name',
            'Branch',
            'Installment No',
            'Due Date',
            'Amount',
            'Interest Amount',
            'Principal Amount',
            'Late Fee',
            'Status',
            'Collection Date',
            'Collected By',
            'Days Overdue',
            'Payment Method',
            'Reference Number'
        ];
    }

    /**
     * @param Installment $installment
     * @return array
     */
    public function map($installment): array
    {
        $daysOverdue = 0;
        if ($installment->status === 'pending' && $installment->installment_date < Carbon::now()) {
            $daysOverdue = Carbon::now()->diffInDays($installment->installment_date);
        }

        return [
            $installment->id,
            $installment->loan->loan_id ?? '',
            $installment->loan->loanApplication->member->member_id ?? '',
            $installment->loan->loanApplication->member->name ?? '',
            $installment->loan->loanApplication->member->branch->name ?? '',
            $installment->installment_no,
            $installment->installment_date->format('Y-m-d'),
            $installment->amount,
            $installment->interest_amount ?? 0,
            $installment->principal_amount ?? 0,
            $installment->late_fee ?? 0,
            ucfirst($installment->status),
            $installment->collection_date ? $installment->collection_date->format('Y-m-d') : '',
            $installment->collectedBy->name ?? '',
            $daysOverdue,
            $installment->payment_method ?? '',
            $installment->reference_number ?? ''
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Installments Report';
    }
}
