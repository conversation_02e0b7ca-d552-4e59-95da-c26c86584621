<?php

namespace App\Exports;

use App\Models\Member;
use App\Models\Branch;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class MembersExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $branch;
    protected $filters;

    public function __construct(Branch $branch = null, array $filters = [])
    {
        $this->branch = $branch;
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Member::with(['branch', 'createdBy', 'loans', 'savingAccounts']);

        if ($this->branch) {
            $query->where('branch_id', $this->branch->id);
        }

        // Apply filters
        if (isset($this->filters['status'])) {
            if ($this->filters['status'] === 'active') {
                $query->whereHas('loans', function ($q) {
                    $q->where('status', 'active');
                });
            } elseif ($this->filters['status'] === 'inactive') {
                $query->whereDoesntHave('loans', function ($q) {
                    $q->where('status', 'active');
                });
            }
        }

        if (isset($this->filters['religion'])) {
            $query->where('religion', $this->filters['religion']);
        }

        if (isset($this->filters['date_from']) && isset($this->filters['date_to'])) {
            $query->whereBetween('created_at', [
                Carbon::parse($this->filters['date_from']),
                Carbon::parse($this->filters['date_to'])
            ]);
        }

        return $query->orderBy('member_id')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Member ID',
            'Name',
            'Father/Husband Name',
            'Mother Name',
            'Date of Birth',
            'Age',
            'NID Number',
            'Phone Number',
            'Present Address',
            'Permanent Address',
            'Occupation',
            'Monthly Income',
            'Religion',
            'Blood Group',
            'Marital Status',
            'Family Members',
            'Branch',
            'Created By',
            'Registration Date',
            'Active Loans',
            'Total Loan Amount',
            'Savings Balance',
            'Status'
        ];
    }

    /**
     * @param Member $member
     * @return array
     */
    public function map($member): array
    {
        $activeLoans = $member->loans->where('status', 'active');
        $savingsBalance = $member->savingAccounts->sum('current_balance');

        return [
            $member->member_id,
            $member->name,
            $member->father_or_husband_name,
            $member->mother_name,
            $member->date_of_birth ? $member->date_of_birth->format('Y-m-d') : '',
            $member->date_of_birth ? Carbon::parse($member->date_of_birth)->age : '',
            $member->nid_number,
            $member->phone_number,
            $member->present_address,
            $member->permanent_address,
            $member->occupation,
            $member->monthly_income,
            ucfirst($member->religion),
            $member->blood_group,
            ucfirst($member->marital_status ?? ''),
            $member->family_members,
            $member->branch->name ?? '',
            $member->createdBy->name ?? '',
            $member->created_at->format('Y-m-d'),
            $activeLoans->count(),
            $activeLoans->sum('loan_amount'),
            $savingsBalance,
            $activeLoans->count() > 0 ? 'Active' : 'Inactive'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Members Report';
    }
}
