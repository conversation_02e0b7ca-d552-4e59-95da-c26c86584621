<?php

namespace App\Exports;

use App\Models\Loan;
use App\Models\Branch;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class LoansExport implements FromCollection, WithHeadings, WithMapping, WithStyles, WithTitle
{
    protected $branch;
    protected $startDate;
    protected $endDate;
    protected $filters;

    public function __construct(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null, array $filters = [])
    {
        $this->branch = $branch;
        $this->startDate = $startDate ?? Carbon::now()->startOfMonth();
        $this->endDate = $endDate ?? Carbon::now()->endOfMonth();
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Loan::with(['loanApplication.member.branch', 'loanApplication.reviewedBy', 'installments']);

        if ($this->branch) {
            $query->where('branch_id', $this->branch->id);
        }

        $query->whereBetween('loan_date', [$this->startDate, $this->endDate]);

        // Apply filters
        if (isset($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (isset($this->filters['loan_duration'])) {
            $query->where('loan_duration_months', $this->filters['loan_duration']);
        }

        if (isset($this->filters['min_amount']) && isset($this->filters['max_amount'])) {
            $query->whereBetween('loan_amount', [
                $this->filters['min_amount'],
                $this->filters['max_amount']
            ]);
        }

        return $query->orderBy('loan_date', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Loan ID',
            'Member ID',
            'Member Name',
            'Branch',
            'Loan Amount',
            'Interest Rate (%)',
            'Duration (Months)',
            'Monthly Installment',
            'Total Repayment',
            'Paid Amount',
            'Remaining Amount',
            'Loan Date',
            'First Installment Date',
            'Last Installment Date',
            'Status',
            'Approved By',
            'Repayment Method',
            'Advance Payment',
            'Total Installments',
            'Paid Installments',
            'Overdue Installments',
            'Collection Efficiency (%)'
        ];
    }

    /**
     * @param Loan $loan
     * @return array
     */
    public function map($loan): array
    {
        $totalInstallments = $loan->installments->count();
        $paidInstallments = $loan->installments->where('status', 'paid')->count();
        $overdueInstallments = $loan->installments->where('status', 'pending')
            ->where('installment_date', '<', Carbon::now())->count();
        
        $collectionEfficiency = $totalInstallments > 0 ? ($paidInstallments / $totalInstallments) * 100 : 0;

        return [
            $loan->loan_id,
            $loan->loanApplication->member->member_id ?? '',
            $loan->loanApplication->member->name ?? '',
            $loan->loanApplication->member->branch->name ?? '',
            $loan->loan_amount,
            $loan->interest_rate,
            $loan->loan_duration_months,
            $loan->monthly_installment_amount,
            $loan->total_repayment_amount,
            $loan->paid_amount,
            $loan->remaining_amount,
            $loan->loan_date ? $loan->loan_date->format('Y-m-d') : '',
            $loan->first_installment_date ? $loan->first_installment_date->format('Y-m-d') : '',
            $loan->last_installment_date ? $loan->last_installment_date->format('Y-m-d') : '',
            ucfirst($loan->status),
            $loan->loanApplication->reviewedBy->name ?? '',
            ucfirst(str_replace('_', ' ', $loan->repayment_method)),
            $loan->advance_payment ?? 0,
            $totalInstallments,
            $paidInstallments,
            $overdueInstallments,
            round($collectionEfficiency, 2)
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text.
            1 => ['font' => ['bold' => true]],
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'Loans Report';
    }
}
