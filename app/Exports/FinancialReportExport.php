<?php

namespace App\Exports;

use App\Models\Branch;
use App\Services\ReportingService;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use Carbon\Carbon;

class FinancialReportExport implements WithMultipleSheets
{
    protected $branch;
    protected $startDate;
    protected $endDate;
    protected $reportingService;

    public function __construct(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null)
    {
        $this->branch = $branch;
        $this->startDate = $startDate ?? Carbon::now()->startOfMonth();
        $this->endDate = $endDate ?? Carbon::now()->endOfMonth();
        $this->reportingService = new ReportingService();
    }

    /**
     * @return array
     */
    public function sheets(): array
    {
        $sheets = [];

        // Financial Summary Sheet
        $sheets[] = new FinancialSummarySheet($this->branch, $this->startDate, $this->endDate, $this->reportingService);

        // Portfolio Analysis Sheet
        $sheets[] = new PortfolioAnalysisSheet($this->branch, $this->reportingService);

        // Collection Efficiency Sheet
        $sheets[] = new CollectionEfficiencySheet($this->branch, $this->startDate, $this->endDate, $this->reportingService);

        return $sheets;
    }
}

class FinancialSummarySheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected $branch;
    protected $startDate;
    protected $endDate;
    protected $reportingService;

    public function __construct(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null, ReportingService $reportingService = null)
    {
        $this->branch = $branch;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->reportingService = $reportingService;
    }

    public function array(): array
    {
        $data = $this->reportingService->getBranchFinancialSummary($this->branch, $this->startDate, $this->endDate);

        return [
            ['Metric', 'Amount (৳)'],
            ['Loan Disbursements', number_format($data['disbursements'], 2)],
            ['Collections', number_format($data['collections'], 2)],
            ['Interest Income', number_format($data['interest_income'], 2)],
            ['Outstanding Loans', number_format($data['outstanding_loans'], 2)],
            ['Savings Deposits', number_format($data['savings_deposits'], 2)],
            ['Savings Withdrawals', number_format($data['savings_withdrawals'], 2)],
            ['Net Savings Flow', number_format($data['net_savings_flow'], 2)],
            ['Net Income', number_format($data['net_income'], 2)],
        ];
    }

    public function headings(): array
    {
        return [
            ['Financial Summary Report'],
            ['Branch: ' . ($this->branch ? $this->branch->name : 'All Branches')],
            ['Period: ' . $this->startDate->format('Y-m-d') . ' to ' . $this->endDate->format('Y-m-d')],
            ['Generated: ' . Carbon::now()->format('Y-m-d H:i:s')],
            [''], // Empty row
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 16]],
            2 => ['font' => ['bold' => true]],
            3 => ['font' => ['bold' => true]],
            4 => ['font' => ['italic' => true]],
            6 => ['font' => ['bold' => true]], // Data headers
        ];
    }

    public function title(): string
    {
        return 'Financial Summary';
    }
}

class PortfolioAnalysisSheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected $branch;
    protected $reportingService;

    public function __construct(Branch $branch = null, ReportingService $reportingService = null)
    {
        $this->branch = $branch;
        $this->reportingService = $reportingService;
    }

    public function array(): array
    {
        $data = $this->reportingService->getLoanPortfolioAnalysis($this->branch);

        $result = [
            ['Metric', 'Value'],
            ['Total Loans', $data['total_loans']],
            ['Active Loans', $data['active_loans']],
            ['Completed Loans', $data['completed_loans']],
            ['Defaulted Loans', $data['defaulted_loans']],
            ['Total Disbursed (৳)', number_format($data['total_disbursed'], 2)],
            ['Total Outstanding (৳)', number_format($data['total_outstanding'], 2)],
            ['Total Collected (৳)', number_format($data['total_collected'], 2)],
            ['Portfolio at Risk (৳)', number_format($data['portfolio_at_risk'], 2)],
            ['PAR Percentage (%)', number_format($data['par_percentage'], 2)],
            ['Average Loan Size (৳)', number_format($data['average_loan_size'], 2)],
            [''], // Empty row
            ['Loans by Status', ''],
        ];

        foreach ($data['by_status'] as $status => $info) {
            $result[] = [ucfirst($status), $info['count'] . ' loans (৳' . number_format($info['amount'], 2) . ')'];
        }

        return $result;
    }

    public function headings(): array
    {
        return [
            ['Loan Portfolio Analysis'],
            ['Branch: ' . ($this->branch ? $this->branch->name : 'All Branches')],
            ['As of: ' . Carbon::now()->format('Y-m-d')],
            [''], // Empty row
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 16]],
            2 => ['font' => ['bold' => true]],
            3 => ['font' => ['bold' => true]],
            5 => ['font' => ['bold' => true]], // Data headers
        ];
    }

    public function title(): string
    {
        return 'Portfolio Analysis';
    }
}

class CollectionEfficiencySheet implements FromArray, WithHeadings, WithStyles, WithTitle
{
    protected $branch;
    protected $startDate;
    protected $endDate;
    protected $reportingService;

    public function __construct(Branch $branch = null, Carbon $startDate = null, Carbon $endDate = null, ReportingService $reportingService = null)
    {
        $this->branch = $branch;
        $this->startDate = $startDate;
        $this->endDate = $endDate;
        $this->reportingService = $reportingService;
    }

    public function array(): array
    {
        $data = $this->reportingService->getCollectionEfficiencyReport($this->branch, $this->startDate, $this->endDate);

        return [
            ['Metric', 'Amount/Count'],
            ['Total Due (৳)', number_format($data['total_due'], 2)],
            ['Total Collected (৳)', number_format($data['total_collected'], 2)],
            ['Collection Rate (%)', number_format($data['collection_rate'], 2)],
            ['Outstanding Amount (৳)', number_format($data['outstanding_amount'], 2)],
            ['On-time Collections', $data['on_time_collections']],
            ['On-time Rate (%)', number_format($data['on_time_rate'], 2)],
            ['Overdue Installments', $data['overdue_installments']],
        ];
    }

    public function headings(): array
    {
        return [
            ['Collection Efficiency Report'],
            ['Branch: ' . ($this->branch ? $this->branch->name : 'All Branches')],
            ['Period: ' . $this->startDate->format('Y-m-d') . ' to ' . $this->endDate->format('Y-m-d')],
            [''], // Empty row
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1 => ['font' => ['bold' => true, 'size' => 16]],
            2 => ['font' => ['bold' => true]],
            3 => ['font' => ['bold' => true]],
            5 => ['font' => ['bold' => true]], // Data headers
        ];
    }

    public function title(): string
    {
        return 'Collection Efficiency';
    }
}
